import type { FC } from 'react'
import {
  ThreadListItemPrimitive,
  ThreadListPrimitive,
} from '@assistant-ui/react'
import { ArchiveIcon, PlusIcon } from 'lucide-react'

import { Button } from '@/components/ui/button'
import { TooltipIconButton } from '@/components/assistant-ui/tooltip-icon-button'

export const ThreadList: FC = () => {
  return (
    <ThreadListPrimitive.Root className="flex flex-col items-stretch gap-1.5">
      <ThreadListNew />
      <ThreadListItems />
    </ThreadListPrimitive.Root>
  )
}

const ThreadListNew: FC = () => {
  return (
    <ThreadListPrimitive.New asChild>
      <Button
        className="data-[active]:bg-slate-100 hover:bg-slate-100 flex items-center justify-start gap-1 rounded-lg px-2.5 py-2 text-start dark:data-[active]:bg-slate-800 dark:hover:bg-slate-800"
        variant="ghost"
      >
        <PlusIcon />
        New Thread
      </Button>
    </ThreadListPrimitive.New>
  )
}

const ThreadListItems: FC = () => {
  return <ThreadListPrimitive.Items components={{ ThreadListItem }} />
}

const ThreadListItem: FC = () => {
  return (
    <ThreadListItemPrimitive.Root className="data-[active]:bg-slate-100 hover:bg-slate-100 focus-visible:bg-slate-100 focus-visible:ring-slate-950 flex items-center gap-2 rounded-lg transition-all focus-visible:outline-none focus-visible:ring-2 dark:data-[active]:bg-slate-800 dark:hover:bg-slate-800 dark:focus-visible:bg-slate-800 dark:focus-visible:ring-slate-300">
      <ThreadListItemPrimitive.Trigger className="flex-grow px-3 py-2 text-start">
        <ThreadListItemTitle />
      </ThreadListItemPrimitive.Trigger>
      <ThreadListItemArchive />
    </ThreadListItemPrimitive.Root>
  )
}

const ThreadListItemTitle: FC = () => {
  return (
    <p className="text-sm">
      <ThreadListItemPrimitive.Title fallback="New Chat" />
    </p>
  )
}

const ThreadListItemArchive: FC = () => {
  return (
    <ThreadListItemPrimitive.Archive asChild>
      <TooltipIconButton
        className="hover:text-slate-900 text-slate-950 ml-auto mr-3 size-4 p-0 dark:hover:text-slate-50 dark:text-slate-50"
        variant="ghost"
        tooltip="Archive thread"
      >
        <ArchiveIcon />
      </TooltipIconButton>
    </ThreadListItemPrimitive.Archive>
  )
}

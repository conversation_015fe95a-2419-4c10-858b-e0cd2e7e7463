"use server";

import prisma from "@/app/libs/prismadb";
import { Search } from "@prisma/client";
import { SearchResult, processSearchResults } from "@/app/api/rag/utils";

export type fetchedSearchesType = {
  conversationId: string;
  selected_node_id: string;
  search_result: SearchResult[];
};

export const fetchSearches = async (
  conversationId: string
): Promise<fetchedSearchesType[]> => {
  const searches: Search[] = await prisma.search.findMany({
    where: { conversation_id: conversationId },
    orderBy: { created_at: "desc" },
  });

  const groupedSearches = searches.reduce((groups, search) => {
    const key = search.selected_node_id;
    if (!groups[key]) {
      groups[key] = [];
    }
    groups[key].push(search);
    return groups;
  }, {} as Record<string, Search[]>);

  const searchData = Object.values(groupedSearches).map((searches) => {
    const latestSearch = searches[0];
    return {
      conversationId: latestSearch.conversation_id,
      selected_node_id: latestSearch.selected_node_id,
      search_result: processSearchResults(
        JSON.parse(latestSearch.search_result)
      ),
    };
  });
  console.log("fetched searches from db using server-actions.ts");
  return searchData;
};

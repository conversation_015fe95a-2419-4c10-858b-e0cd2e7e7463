'use server'

import { getServerSession } from 'next-auth'
import { authOptions } from '@/app/api/auth/authOptions'
import prismadb from '@/app/libs/prismadb'
import { revalidatePath } from 'next/cache'

/**
 * Direct server action for updating research content
 * Validates user ownership and updates content_text
 */
export async function updateDragTreeNodeContent(
  contentId: string,
  contentText: string
): Promise<{ success: boolean; error?: string }> {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return { success: false, error: 'Unauthorized' }
    }

    // Get the content with nested relations to check ownership
    const existingContent = await prismadb.dragTreeNodeContent.findUnique({
      where: { id: contentId },
      include: {
        drag_tree_node: {
          include: {
            drag_tree: true,
          },
        },
      },
    })

    if (!existingContent) {
      return { success: false, error: 'Content not found' }
    }

    // Validate user ownership
    if (existingContent.drag_tree_node.drag_tree.user_id !== session.user.id) {
      return { success: false, error: 'Unauthorized' }
    }

    // Update the content
    await prismadb.dragTreeNodeContent.update({
      where: { id: contentId },
      data: { content_text: contentText },
    })

    // Revalidate the specific drag tree page
    revalidatePath(`/dragTree/${existingContent.drag_tree_node.drag_tree.id}`)

    return { success: true }
  } catch (error) {
    console.error('Error updating research content:', error)
    return { success: false, error: 'Failed to update content' }
  }
}

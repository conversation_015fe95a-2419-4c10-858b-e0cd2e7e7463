'use server'

import prisma from '@/app/libs/prismadb'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/app/api/auth/authOptions'
import { AIGenerationStatus } from '@prisma/client'
import { validateDragTreeOwnership } from '@/app/libs/validation-utils'

// Import and re-export types from shared location
import type { AIGenerationMeta } from '@/app/types/ai-generation'
export type { AIGenerationMeta }

/**
 * Get AI generations metadata by drag tree ID
 * Returns only lightweight metadata, not the full content for performance
 *
 * @param dragTreeId - The drag tree ID to fetch generations for
 * @param status - Optional status filter (defaults to ACTIVE)
 * @param limit - Optional limit for pagination (defaults to 50)
 * @param offset - Optional offset for pagination (defaults to 0)
 * @returns Array of AI generation metadata
 */
export async function getAIGenerations(
  dragTreeId: string,
  status: AIGenerationStatus = AIGenerationStatus.ACTIVE,
  limit: number = 50,
  offset: number = 0
): Promise<AIGenerationMeta[]> {
  try {
    // Verify user session
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      throw new Error('Unauthorized: User session required')
    }

    const userId = session.user.id

    // Validate dragTree ownership
    const validation = await validateDragTreeOwnership(userId, dragTreeId)
    if (!validation.success) {
      throw new Error('Access denied: You do not own this dragTree')
    }

    // Fetch AI generations metadata (excluding heavy content fields)
    const generations = await prisma.aIGeneration.findMany({
      where: {
        entity_type: 'drag_tree_v1',
        entity_id: dragTreeId,
        user_id: userId,
        status: status,
      },
      select: {
        id: true,
        title: true,
        status: true,
        version: true,
        created_at: true,
        updated_at: true,
        config: true,
        metadata: true,
        // Explicitly exclude heavy content fields
        // content: false,
        // generation_input: false,
        // generation_output: false,
      },
      orderBy: {
        created_at: 'desc',
      },
      take: limit,
      skip: offset,
    })

    // Convert database fields to camelCase for frontend
    return generations.map(gen => ({
      id: gen.id,
      title: gen.title,
      status: gen.status,
      version: gen.version,
      createdAt: gen.created_at,
      updatedAt: gen.updated_at,
      config: gen.config,
      metadata: gen.metadata,
    }))
  } catch (error) {
    console.error('Error fetching AI generations:', error)
    throw new Error('Failed to fetch AI generations')
  }
}

/**
 * Get count of AI generations for a drag tree
 *
 * @param dragTreeId - The drag tree ID to count generations for
 * @param status - Optional status filter (defaults to ACTIVE)
 * @returns Number of AI generations
 */
export async function getAIGenerationsCount(
  dragTreeId: string,
  status: AIGenerationStatus = AIGenerationStatus.ACTIVE
): Promise<number> {
  try {
    // Verify user session
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      throw new Error('Unauthorized: User session required')
    }

    const userId = session.user.id

    // Validate dragTree ownership
    const validation = await validateDragTreeOwnership(userId, dragTreeId)
    if (!validation.success) {
      throw new Error('Access denied: You do not own this dragTree')
    }

    const count = await prisma.aIGeneration.count({
      where: {
        entity_type: 'drag_tree_v1',
        entity_id: dragTreeId,
        user_id: userId,
        status: status,
      },
    })

    return count
  } catch (error) {
    console.error('Error counting AI generations:', error)
    throw new Error('Failed to count AI generations')
  }
}

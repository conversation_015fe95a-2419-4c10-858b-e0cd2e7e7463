'use server'

import prisma from '@/app/libs/prismadb'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/app/api/auth/authOptions'
import { JSONContent } from '@tiptap/core'

// Import and re-export types from shared location
import type {
  AIGenerationUpdateData,
  AIGenerationUpdateResult,
} from '@/app/types/ai-generation'
export type { AIGenerationUpdateData, AIGenerationUpdateResult }

/**
 * Update AI generation content with optimistic locking (used by tiptap editor)
 * This allows users to edit and persist changes to generated content while preventing race conditions
 *
 * @param generationId - The AI generation ID to update
 * @param updateData - The data to update (content, title, metadata)
 * @param expectedVersion - The expected version number for optimistic locking
 * @returns Result object indicating success/failure and new version
 */
export async function updateAIGeneration(
  generationId: string,
  updateData: AIGenerationUpdateData,
  expectedVersion: number
): Promise<AIGenerationUpdateResult> {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return { success: false, error: 'Unauthorized' }
    }

    // --- FIX START: Prevent metadata overwrite ---
    // First, fetch the existing metadata to ensure we don't overwrite it.
    // This prevents Prisma from throwing an error due to JSON type mismatches.
    const existingGeneration = await prisma.aIGeneration.findUnique({
      where: { id: generationId },
      select: { metadata: true },
    })

    // Safely get existing metadata, defaulting to an empty object if null
    const existingMetadata =
      (existingGeneration?.metadata as Record<string, any>) || {}

    // Merge existing metadata with new metadata
    const newMetadata = { ...existingMetadata, ...updateData.metadata }
    // --- FIX END ---

    const userId = session.user.id

    // Prepare partial update
    const data: any = {
      version: { increment: 1 },
      updated_at: new Date(),
    }
    if (updateData.content !== undefined) {
      data.content = updateData.content // This will be the Tiptap JSON string
    }
    if (updateData.title !== undefined) {
      data.title = updateData.title
    }
    if (updateData.metadata !== undefined) {
      data.metadata = newMetadata
    }

    const res = await prisma.aIGeneration.updateMany({
      where: { id: generationId, version: expectedVersion, user_id: userId },
      data,
    })

    if (res.count === 0) {
      return { success: false, error: 'Version conflict' }
    }

    return { success: true, newVersion: expectedVersion + 1 }
  } catch (e) {
    console.error('Error updating AI generation', e)
    return { success: false, error: 'Update failed' }
  }
}

/**
 * Helper function to determine if dragTree ownership validation is needed
 * Only validates for drag_tree_v1 entity types
 */
async function shouldValidateDragTreeOwnership(
  generationId: string
): Promise<boolean> {
  const generation = await prisma.aIGeneration.findUnique({
    where: { id: generationId },
    select: { entity_type: true },
  })
  return generation?.entity_type === 'drag_tree_v1'
}

/**
 * Update AI generation content from tiptap JSON
 * Converts tiptap JSON to markdown and updates the content
 *
 * @param generationId - The AI generation ID to update
 * @param tiptapContent - The tiptap JSON content
 * @param markdownContent - The converted markdown content
 * @param expectedVersion - The expected version number for optimistic locking
 * @returns Result object indicating success/failure and new version
 */
export async function updateAIGenerationFromTiptap(
  generationId: string,
  tiptapJsonString: string,
  markdownContent: string,
  expectedVersion: number
): Promise<AIGenerationUpdateResult> {
  try {
    // Use the general update function with tiptap-specific metadata
    return await updateAIGeneration(
      generationId,
      {
        content: tiptapJsonString,
        metadata: { editedFromTiptap: true },
      },
      expectedVersion
    )
  } catch (error) {
    console.error('Error updating AI generation from tiptap:', error)
    return {
      success: false,
      error: 'Failed to update AI generation from tiptap',
    }
  }
}

/**
 * Update AI generation title
 * Simple utility for title-only updates
 *
 * @param generationId - The AI generation ID to update
 * @param title - The new title
 * @param expectedVersion - The expected version number for optimistic locking
 * @returns Result object indicating success/failure and new version
 */
export async function updateAIGenerationTitle(
  generationId: string,
  title: string,
  expectedVersion: number
): Promise<AIGenerationUpdateResult> {
  try {
    return await updateAIGeneration(generationId, { title }, expectedVersion)
  } catch (error) {
    console.error('Error updating AI generation title:', error)
    return {
      success: false,
      error: 'Failed to update AI generation title',
    }
  }
}

/**
 * Mark AI generation as read in metadata (is_read=true)
 * This is triggered when the user opens an asset for the first time.
 */
export async function markAIGenerationRead(
  generationId: string,
  expectedVersion: number
): Promise<AIGenerationUpdateResult> {
  try {
    // Simple direct update to avoid long interactive transaction
    const updated = await prisma.aIGeneration.updateMany({
      where: {
        id: generationId,
        version: expectedVersion,
      },
      data: {
        metadata: { is_read: true },
        version: { increment: 1 },
        updated_at: new Date(),
      },
    })

    if (updated.count === 0) {
      return {
        success: false,
        error: 'Version conflict or generation not found',
      }
    }

    return { success: true, newVersion: expectedVersion + 1 }
  } catch (error) {
    console.error('Error marking generation read:', error)
    return { success: false, error: 'Failed to mark generation read' }
  }
}

import type { AiStepType, AiMessageRole } from '@prisma/client'

/**
 * Execution step data collected during AI processing
 * This is used to build the complete execution trace
 */
export type ExecutionStepData = {
  stepOrder: number
  type: AiStepType
  metadata: Record<string, any>
  parallelKey?: string
  parentStepId?: string
}

/**
 * Attachment data for file uploads
 */
export type AttachmentData = {
  fileName: string
  fileType: string
  fileSize: number
  url: string
}

/**
 * Message data for persistence
 */
export type MessageData = {
  role: AiMessageRole
  content: string
  steps?: ExecutionStepData[]
  attachments?: AttachmentData[]
}

/**
 * Complete conversation turn data for atomic persistence
 */
export type ConversationTurnData = {
  conversationId: string
  userMessage: MessageData
  assistantMessage: MessageData
}

/**
 * Conversation creation data
 */
export type ConversationCreateData = {
  userId: string
  title?: string
  contextEntityType?: string
  contextEntityId?: string
}

/**
 * Result types for service operations
 */
export type PersistenceResult<T> = {
  success: boolean
  data?: T
  error?: string
}

/**
 * Enhanced error type for better error handling
 */
export type ServiceError = {
  message: string
  code?: string
  details?: Record<string, unknown>
  timestamp: Date
}

/**
 * Enhanced result type with better error handling
 */
export type EnhancedPersistenceResult<T> = {
  success: boolean
  data?: T
  error?: ServiceError
}

/**
 * Conversation with related data
 */
export type ConversationWithMessages = {
  id: string
  userId: string
  title: string | null
  contextEntityType: string | null
  contextEntityId: string | null
  createdAt: Date
  updatedAt: Date
  messages: Array<{
    id: string
    role: AiMessageRole
    content: string
    createdAt: Date
    steps: Array<{
      id: string
      stepOrder: number
      type: AiStepType
      metadata: Record<string, any>
      parallelKey: string | null
      parentStepId: string | null
    }>
    attachments: Array<{
      id: string
      fileName: string
      fileType: string
      fileSize: number
      url: string
    }>
  }>
}

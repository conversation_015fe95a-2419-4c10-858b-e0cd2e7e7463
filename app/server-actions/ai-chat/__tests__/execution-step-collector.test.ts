/**
 * @jest-environment node
 */
import {
  ExecutionStepCollector,
  createExecutionStepCollector,
  parseThinkingContent,
} from '../execution-step-collector'

describe('ExecutionStepCollector', () => {
  let collector: ExecutionStepCollector

  beforeEach(() => {
    collector = createExecutionStepCollector()
    // Enable legacy thinking tags for tests
    process.env.ENABLE_LEGACY_THINKING_TAGS = 'true'
  })

  afterEach(() => {
    // Clean up environment
    delete process.env.ENABLE_LEGACY_THINKING_TAGS
  })

  describe('step collection methods', () => {
    it('should add thought steps correctly', () => {
      collector.addThought('This is a reasoning step')
      collector.addThought('Another thought', { priority: 'high' })

      const steps = collector.getSteps()
      expect(steps).toHaveLength(2)
      expect(steps[0].type).toBe('THOUGHT')
      expect(steps[0].stepOrder).toBe(0)
      expect(steps[0].metadata.reasoning).toBe('This is a reasoning step')
      expect(steps[1].stepOrder).toBe(1)
      expect(steps[1].metadata.priority).toBe('high')
    })

    it('should add tool call steps correctly', () => {
      collector.addToolCall('searchTool', { query: 'test query' })
      collector.addToolCall('dataTool', { param: 'value' }, { context: 'test' })

      const steps = collector.getSteps()
      expect(steps).toHaveLength(2)
      expect(steps[0].type).toBe('TOOL_CALL')
      expect(steps[0].metadata.toolName).toBe('searchTool')
      expect(steps[0].metadata.args).toEqual({ query: 'test query' })
      expect(steps[1].metadata.context).toBe('test')
    })

    it('should add tool result steps correctly', () => {
      collector.addToolResult('searchTool', { results: ['item1', 'item2'] })

      const steps = collector.getSteps()
      expect(steps).toHaveLength(1)
      expect(steps[0].type).toBe('TOOL_RESULT')
      expect(steps[0].metadata.toolName).toBe('searchTool')
      expect(steps[0].metadata.result).toEqual({ results: ['item1', 'item2'] })
    })

    it('should add reasoning summary steps correctly', () => {
      collector.addReasoningSummary('Summary of reasoning process')

      const steps = collector.getSteps()
      expect(steps).toHaveLength(1)
      expect(steps[0].type).toBe('REASONING_SUMMARY')
      expect(steps[0].metadata.summary).toBe('Summary of reasoning process')
    })

    it('should add sub-agent invocation steps correctly', () => {
      collector.addSubAgentInvocation('codeAgent', 'Generate Python code')

      const steps = collector.getSteps()
      expect(steps).toHaveLength(1)
      expect(steps[0].type).toBe('SUB_AGENT_INVOCATION')
      expect(steps[0].metadata.agentName).toBe('codeAgent')
      expect(steps[0].metadata.task).toBe('Generate Python code')
    })

    it('should add parallel steps correctly', () => {
      collector.addParallelStep('group1', 'TOOL_CALL', { tool: 'search' })
      collector.addParallelStep('group1', 'TOOL_CALL', { tool: 'analyze' })
      collector.addParallelStep('group2', 'TOOL_RESULT', { result: 'data' })

      const steps = collector.getSteps()
      expect(steps).toHaveLength(3)
      expect(steps[0].parallelKey).toBe('group1')
      expect(steps[1].parallelKey).toBe('group1')
      expect(steps[2].parallelKey).toBe('group2')
    })

    it('should maintain correct step order', () => {
      collector.addThought('First thought')
      collector.addToolCall('tool1', { param: 'value' })
      collector.addToolResult('tool1', { result: 'success' })
      collector.addReasoningSummary('Final summary')

      const steps = collector.getSteps()
      expect(steps).toHaveLength(4)
      expect(steps[0].stepOrder).toBe(0)
      expect(steps[1].stepOrder).toBe(1)
      expect(steps[2].stepOrder).toBe(2)
      expect(steps[3].stepOrder).toBe(3)
    })
  })

  describe('utility methods', () => {
    beforeEach(() => {
      collector.addThought('Thought 1')
      collector.addToolCall('tool1', { param: 'value' })
      collector.addToolResult('tool1', { result: 'success' })
      collector.addThought('Thought 2')
      collector.addReasoningSummary('Summary')
    })

    it('should get correct step count', () => {
      expect(collector.getStepCount()).toBe(5)
    })

    it('should get steps by type', () => {
      const thoughts = collector.getStepsByType('THOUGHT')
      const toolCalls = collector.getStepsByType('TOOL_CALL')
      const toolResults = collector.getStepsByType('TOOL_RESULT')
      const summaries = collector.getStepsByType('REASONING_SUMMARY')

      expect(thoughts).toHaveLength(2)
      expect(toolCalls).toHaveLength(1)
      expect(toolResults).toHaveLength(1)
      expect(summaries).toHaveLength(1)
    })

    it('should get steps by parallel key', () => {
      collector.addParallelStep('group1', 'TOOL_CALL', { tool: 'search' })
      collector.addParallelStep('group1', 'TOOL_CALL', { tool: 'analyze' })

      const parallelSteps = collector.getStepsByParallelKey('group1')
      expect(parallelSteps).toHaveLength(2)
    })

    it('should clear all steps', () => {
      expect(collector.getStepCount()).toBe(5)
      collector.clear()
      expect(collector.getStepCount()).toBe(0)
      expect(collector.getSteps()).toHaveLength(0)
    })

    it('should generate correct summary', () => {
      collector.addParallelStep('group1', 'TOOL_CALL', { tool: 'search' })
      collector.addParallelStep('group1', 'TOOL_CALL', { tool: 'analyze' })

      const summary = collector.getSummary()
      expect(summary.totalSteps).toBe(7)
      expect(summary.stepsByType.THOUGHT).toBe(2)
      expect(summary.stepsByType.TOOL_CALL).toBe(3)
      expect(summary.stepsByType.TOOL_RESULT).toBe(1)
      expect(summary.stepsByType.REASONING_SUMMARY).toBe(1)
      expect(summary.stepsByType.SUB_AGENT_INVOCATION).toBe(0)
      expect(summary.parallelGroups).toBe(1)
      expect(summary.hasErrors).toBe(false)
    })

    it('should detect errors in summary', () => {
      collector.addToolResult(
        'tool1',
        { result: 'data' },
        { error: 'Something went wrong' }
      )

      const summary = collector.getSummary()
      expect(summary.hasErrors).toBe(true)
    })
  })

  describe('parseThinkingTags', () => {
    it('should parse simple thinking tags', () => {
      const content =
        'Before thinking <thinking>This is my reasoning</thinking> After thinking'
      const result = collector.parseThinkingTags(content)

      expect(result.cleanContent).toBe('Before thinking  After thinking')
      expect(result.steps).toHaveLength(2) // 1 thought + 1 reasoning summary
      expect(result.steps[0].type).toBe('THOUGHT')
      expect(result.steps[0].metadata.reasoning).toBe('This is my reasoning')
      expect(result.steps[1].type).toBe('REASONING_SUMMARY')
    })

    it('should parse thinking tags with time attribute', () => {
      const content = '<thinking time="1500">Timed reasoning</thinking>'
      const result = collector.parseThinkingTags(content)

      expect(result.steps).toHaveLength(2)
      expect(result.steps[0].metadata.thinkingTime).toBe(1500)
    })

    it('should parse tool calls within thinking tags', () => {
      const content = `<thinking>
🔍 Search for information
Let me search for relevant data

📊 Analyze data
Now analyzing the results
      </thinking>`
      const result = collector.parseThinkingTags(content)

      expect(result.steps.length).toBeGreaterThan(2)
      expect(result.steps.some(step => step.type === 'TOOL_CALL')).toBe(true)
    })

    it('should handle multiple thinking tags', () => {
      const content = `
        <thinking>First reasoning</thinking>
        Some content
        <thinking>Second reasoning</thinking>
      `
      const result = collector.parseThinkingTags(content)

      expect(result.steps.length).toBeGreaterThan(2)
      expect(result.cleanContent).not.toContain('<thinking>')
    })

    it('should handle empty thinking tags', () => {
      const content = 'Before <thinking></thinking> After'
      const result = collector.parseThinkingTags(content)

      expect(result.cleanContent).toBe('Before  After')
      // Empty thinking tags still create a thought step with empty reasoning
      expect(result.steps).toHaveLength(2) // 1 thought + 1 reasoning summary
    })

    it('should handle content without thinking tags', () => {
      const content = 'Regular content without thinking tags'
      const result = collector.parseThinkingTags(content)

      expect(result.cleanContent).toBe(content)
      expect(result.steps).toHaveLength(0)
    })

    it('should disable thinking parser when ENABLE_LEGACY_THINKING_TAGS is not set', () => {
      // Temporarily disable the flag
      delete process.env.ENABLE_LEGACY_THINKING_TAGS

      const content =
        '<thinking>This should be ignored</thinking>Regular content'
      const result = collector.parseThinkingTags(content)

      expect(result.cleanContent).toBe(content) // Content unchanged
      expect(result.steps).toHaveLength(0) // No steps extracted

      // Restore flag for other tests
      process.env.ENABLE_LEGACY_THINKING_TAGS = 'true'
    })
  })

  describe('standalone utility functions', () => {
    it('should create new collector instance', () => {
      const newCollector = createExecutionStepCollector()
      expect(newCollector).toBeInstanceOf(ExecutionStepCollector)
      expect(newCollector.getStepCount()).toBe(0)
    })

    it('should parse thinking content with utility function', () => {
      const content = '<thinking>Utility function test</thinking>'
      const result = parseThinkingContent(content)

      expect(result.cleanContent).toBe('')
      expect(result.steps).toHaveLength(2)
      expect(result.steps[0].type).toBe('THOUGHT')
      expect(result.steps[0].metadata.reasoning).toBe('Utility function test')
    })
  })
})

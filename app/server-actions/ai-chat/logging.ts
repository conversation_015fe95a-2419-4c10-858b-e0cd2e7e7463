/**
 * Structured logging utilities for AI chat services
 * Prevents PII leakage and provides consistent logging format
 */

export type LogLevel = 'info' | 'warn' | 'error' | 'debug'

export type LogContext = {
  userId?: string
  conversationId?: string
  messageId?: string
  stepId?: string
  operation?: string
  duration?: number
  metadata?: Record<string, unknown>
}

export type LogEntry = {
  level: LogLevel
  message: string
  context: LogContext
  timestamp: Date
  error?: {
    name: string
    message: string
    stack?: string
    code?: string
  }
}

/**
 * Creates a structured log entry with proper error handling
 */
function createLogEntry(
  level: LogLevel,
  message: string,
  context: LogContext = {},
  error?: unknown
): LogEntry {
  const entry: LogEntry = {
    level,
    message,
    context: {
      ...context,
      // Sanitize sensitive data
      userId: context.userId ? `user_${context.userId.slice(-6)}` : undefined,
      conversationId: context.conversationId
        ? `conv_${context.conversationId.slice(-6)}`
        : undefined,
      messageId: context.messageId
        ? `msg_${context.messageId.slice(-6)}`
        : undefined,
      stepId: context.stepId ? `step_${context.stepId.slice(-6)}` : undefined,
    },
    timestamp: new Date(),
  }

  if (error) {
    if (error instanceof Error) {
      entry.error = {
        name: error.name,
        message: error.message,
        stack: error.stack,
        code: (error as any).code,
      }
    } else {
      entry.error = {
        name: 'UnknownError',
        message: String(error),
      }
    }
  }

  return entry
}

/**
 * Structured logger for AI chat services
 */
export class AiChatLogger {
  private serviceName: string

  constructor(serviceName: string) {
    this.serviceName = serviceName
  }

  info(message: string, context: LogContext = {}): void {
    const entry = createLogEntry('info', message, {
      ...context,
      service: this.serviceName,
    } as LogContext & { service: string })
    console.log(JSON.stringify(entry))
  }

  warn(message: string, context: LogContext = {}): void {
    const entry = createLogEntry('warn', message, {
      ...context,
      service: this.serviceName,
    } as LogContext & { service: string })
    console.warn(JSON.stringify(entry))
  }

  error(message: string, error?: unknown, context: LogContext = {}): void {
    const entry = createLogEntry(
      'error',
      message,
      {
        ...context,
        service: this.serviceName,
      } as LogContext & { service: string },
      error
    )
    console.error(JSON.stringify(entry))
  }

  debug(message: string, context: LogContext = {}): void {
    const entry = createLogEntry('debug', message, {
      ...context,
      service: this.serviceName,
    } as LogContext & { service: string })
    console.debug(JSON.stringify(entry))
  }

  /**
   * Logs operation timing
   */
  timing(operation: string, duration: number, context: LogContext = {}): void {
    this.info(`Operation completed: ${operation}`, {
      ...context,
      operation,
      duration,
    })
  }

  /**
   * Logs validation errors with context
   */
  validationError(
    field: string,
    value: unknown,
    reason: string,
    context: LogContext = {}
  ): void {
    this.error(`Validation failed for ${field}: ${reason}`, undefined, {
      ...context,
      validationField: field,
      validationReason: reason,
      // Don't log the actual value to prevent PII leakage
    } as LogContext & { validationField: string; validationReason: string })
  }

  /**
   * Logs database operation results
   */
  dbOperation(
    operation: string,
    success: boolean,
    duration?: number,
    context: LogContext = {}
  ): void {
    const level = success ? 'info' : 'error'
    const message = `Database ${operation} ${success ? 'succeeded' : 'failed'}`

    const entry = createLogEntry(level, message, {
      ...context,
      operation,
      duration,
      service: this.serviceName,
    } as LogContext & { operation: string; duration: number; service: string })

    if (success) {
      console.log(JSON.stringify(entry))
    } else {
      console.error(JSON.stringify(entry))
    }
  }
}

/**
 * Default logger instances for different services
 */
export const persistenceLogger = new AiChatLogger('ai-chat-persistence')
export const validationLogger = new AiChatLogger('ai-chat-validation')
export const executionLogger = new AiChatLogger('ai-chat-execution')

/**
 * Utility function to safely stringify objects for logging
 */
export function safeStringify(obj: unknown): string {
  try {
    return JSON.stringify(obj, (key, value) => {
      // Remove potentially sensitive fields
      if (
        key.toLowerCase().includes('password') ||
        key.toLowerCase().includes('token') ||
        key.toLowerCase().includes('secret') ||
        key.toLowerCase().includes('key')
      ) {
        return '[REDACTED]'
      }
      return value
    })
  } catch {
    return '[CIRCULAR_REFERENCE]'
  }
}

/**
 * Performance monitoring utility
 */
export function withTiming<T>(
  operation: string,
  fn: () => Promise<T>,
  logger: AiChatLogger,
  context: LogContext = {}
): Promise<T> {
  const start = Date.now()

  return fn().then(
    result => {
      const duration = Date.now() - start
      logger.timing(operation, duration, context)
      return result
    },
    error => {
      const duration = Date.now() - start
      logger.error(`Operation failed: ${operation}`, error, {
        ...context,
        operation,
        duration,
      })
      throw error
    }
  )
}

"use server";

import {
  fetchedIssueTreeType,
  fetchedFeedbacksType,
  fetchedSubtreesType,
  fetchedSearchesType,
  fetchedRagsType,
} from "./index";
import { fetchNotebooks } from "./notebook";
import { fetchIssueTree } from "./issue-tree";
import { fetchSubtrees } from "./subtree";
import { fetchAllFeedbacks } from "./feedback";
import { fetchSearches } from "./search";
import { fetchRags } from "./rag";
import { NotebookStatus } from "@prisma/client";

type FetchedNotebook = {
  id: string;
  status: NotebookStatus;
  title: string;
  updated_at: Date;
};

export type FetchedIssueTreeData = {
  issueTree: fetchedIssueTreeType | null;
  feedbacks: fetchedFeedbacksType[];
  subtrees: fetchedSubtreesType[];
  searches: fetchedSearchesType[];
  rags: fetchedRagsType[];
  notebooks: FetchedNotebook[];
};

export async function fetchAllIssueTreeData(
  conversationId: string
): Promise<FetchedIssueTreeData> {
  const [issueTree, subtrees, feedbacks, searches, rags, notebooks] =
    await Promise.all([
      fetchIssueTree(conversationId),
      fetchSubtrees(conversationId),
      fetchAllFeedbacks(conversationId),
      fetchSearches(conversationId),
      fetchRags(conversationId),
      fetchNotebooks(conversationId),
    ]);

  return {
    issueTree,
    subtrees,
    feedbacks,
    searches,
    rags,
    notebooks,
  };
}

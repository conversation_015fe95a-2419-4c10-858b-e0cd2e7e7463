'use server'

import { revalidatePath } from 'next/cache'
import prisma from '@/app/libs/prismadb'

export type UpdateUserMetadataInput = {
  userId: string
  metadata: Record<string, any>
  mergeMode?: 'replace' | 'merge'
}

export type GetUserMetadataInput = {
  userId: string
  keys?: string[]
}

/**
 * Update user metadata with flexible merge or replace modes
 * @param input - User ID, metadata object, and merge mode
 * @returns Success/error response with updated user data
 */
export async function updateUserMetadata(input: UpdateUserMetadataInput) {
  try {
    const { userId, metadata, mergeMode = 'merge' } = input

    // First get the current user to access existing metadata
    const currentUser = await prisma.user.findUnique({
      where: { id: userId },
      select: { metadata: true },
    })

    if (!currentUser) {
      return { success: false, error: 'User not found' }
    }

    let finalMetadata: Record<string, any>

    if (mergeMode === 'replace') {
      // Replace entire metadata
      finalMetadata = metadata
    } else {
      // Merge with existing metadata (deep merge for nested objects)
      const existingMetadata =
        (currentUser.metadata as Record<string, any>) || {}
      finalMetadata = { ...existingMetadata, ...metadata }
    }

    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: {
        metadata: finalMetadata,
        updated_at: new Date(),
      },
      select: {
        id: true,
        metadata: true,
        updated_at: true,
      },
    })

    revalidatePath('/dragTree')
    return { success: true, data: updatedUser }
  } catch (error) {
    console.error('Error updating user metadata:', error)
    return { success: false, error: 'Failed to update user metadata' }
  }
}

/**
 * Get user metadata, optionally filtered by specific keys
 * @param input - User ID and optional keys to filter
 * @returns Success/error response with user metadata
 */
export async function getUserMetadata(input: GetUserMetadataInput) {
  try {
    const { userId, keys } = input

    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        metadata: true,
      },
    })

    if (!user) {
      return { success: false, error: 'User not found' }
    }

    const metadata = (user.metadata as Record<string, any>) || {}

    // If specific keys are requested, filter the metadata
    if (keys && keys.length > 0) {
      const filteredMetadata = keys.reduce(
        (acc, key) => {
          if (key in metadata) {
            acc[key] = metadata[key]
          }
          return acc
        },
        {} as Record<string, any>
      )

      return {
        success: true,
        data: { id: user.id, metadata: filteredMetadata },
      }
    }

    return { success: true, data: { id: user.id, metadata } }
  } catch (error) {
    console.error('Error getting user metadata:', error)
    return { success: false, error: 'Failed to get user metadata' }
  }
}

/**
 * Mark tutorial as completed for a user
 * @param userId - User ID
 * @returns Success/error response
 */
export async function markTutorialCompleted(userId: string) {
  try {
    const createdAt = new Date()

    const tutorialMetadata = {
      tutorial: {
        is_completed: true,
        is_skipped: false,
        created_at: createdAt,
      },
    }

    const result = await updateUserMetadata({
      userId,
      metadata: tutorialMetadata,
      mergeMode: 'merge',
    })

    return result
  } catch (error) {
    console.error('Error marking tutorial as completed:', error)
    return { success: false, error: 'Failed to mark tutorial as completed' }
  }
}

/**
 * Mark tutorial as skipped for a user
 * @param userId - User ID
 * @returns Success/error response
 */
export async function markTutorialSkipped(userId: string) {
  try {
    const createdAt = new Date()

    const tutorialMetadata = {
      tutorial: {
        is_completed: false,
        is_skipped: true,
        created_at: createdAt,
      },
    }

    const result = await updateUserMetadata({
      userId,
      metadata: tutorialMetadata,
      mergeMode: 'merge',
    })

    return result
  } catch (error) {
    console.error('Error marking tutorial as skipped:', error)
    return { success: false, error: 'Failed to mark tutorial as skipped' }
  }
}

/**
 * Check tutorial status for a user
 * @param userId - User ID
 * @returns Success/error response with tutorial status
 */
export async function checkTutorialStatus(userId: string) {
  try {
    const result = await getUserMetadata({
      userId,
      keys: ['tutorial'],
    })

    if (!result.success) {
      return result
    }

    const tutorialData = result.data?.metadata?.tutorial
    const isCompleted = tutorialData?.is_completed === true
    const isSkipped = tutorialData?.is_skipped === true
    const shouldShow = !isCompleted && !isSkipped // Show if neither completed nor skipped

    return {
      success: true,
      data: {
        isCompleted,
        isSkipped,
        shouldShow,
        createdAt: tutorialData?.created_at || null,
      },
    }
  } catch (error) {
    console.error('Error checking tutorial status:', error)
    return { success: false, error: 'Failed to check tutorial status' }
  }
}

/**
 * @deprecated Use checkTutorialStatus instead
 * Check if user has completed the tutorial
 */
export async function checkTutorialCompleted(userId: string) {
  const result = await checkTutorialStatus(userId)
  if (!result.success) return result

  // Type guard to ensure we have the tutorial status data
  if (!result.data || !('isCompleted' in result.data)) {
    return { success: false, error: 'Invalid tutorial data structure' }
  }

  return {
    success: true,
    data: {
      isCompleted: result.data.isCompleted || false,
      completionDate: result.data.createdAt || null, // Use createdAt as completionDate for backward compatibility
    },
  }
}

/**
 * Test function to verify tutorial metadata persistence (remove after testing)
 * @param userId - User ID to test
 */
export async function testTutorialPersistence(userId: string) {
  try {
    console.log('🧪 [testTutorialPersistence] Testing for user:', userId)

    // 1. Check initial state
    const initialState = await checkTutorialStatus(userId)
    console.log('🧪 Initial state:', initialState)

    // 2. Test completion
    const markCompletedResult = await markTutorialCompleted(userId)
    console.log('🧪 Mark completed result:', markCompletedResult)

    const completedState = await checkTutorialStatus(userId)
    console.log('🧪 Completed state:', completedState)

    // 3. Test skipping (overrides completion)
    const markSkippedResult = await markTutorialSkipped(userId)
    console.log('🧪 Mark skipped result:', markSkippedResult)

    const skippedState = await checkTutorialStatus(userId)
    console.log('🧪 Skipped state:', skippedState)

    console.log('🧪 Final metadata structure examples:')
    console.log(
      'Completed:',
      JSON.stringify(
        {
          tutorial: {
            is_completed: true,
            is_skipped: false,
            created_at: new Date(),
          },
        },
        null,
        2
      )
    )
    console.log(
      'Skipped:',
      JSON.stringify(
        {
          tutorial: {
            is_completed: false,
            is_skipped: true,
            created_at: new Date(),
          },
        },
        null,
        2
      )
    )

    return {
      success: true,
      data: {
        initialState,
        markCompletedResult,
        completedState,
        markSkippedResult,
        skippedState,
      },
    }
  } catch (error) {
    console.error('🧪 Test failed:', error)
    return { success: false, error: 'Test failed' }
  }
}

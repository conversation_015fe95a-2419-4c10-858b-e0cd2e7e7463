"use server";

import { NextResponse } from "next/server";
import prisma from "@/app/libs/prismadb";
import { OpenAIUsageType } from "@prisma/client";
import { encode } from "@nem035/gpt-3-encoder";

export type LogOpenAIUsageType = {
  open_ai_usage_type: OpenAIUsageType;
  model_name: string;
  input_text: string;
  output_text: string;
  userId: string;
  conversationId: string;
  input_token_est?: number;
  output_token_est?: number;
};

export const logOpenAIUsage_serverAction = async (
  request: LogOpenAIUsageType
) => {
  try {
    const {
      open_ai_usage_type,
      model_name,
      input_text,
      output_text,
      userId,
      conversationId,
      input_token_est,
      output_token_est,
    } = request;

    const calculateTokens = (text: string): number => {
      return encode(text).length;
    };

    await prisma.openAIUsage.create({
      data: {
        open_ai_usage_type,
        model_name,
        input_text,
        output_text,
        input_token_est: input_token_est ?? calculateTokens(input_text),
        output_token_est: output_token_est ?? calculateTokens(output_text),
        user_id: userId,
        conversation_id: conversationId,
      },
    });

    return NextResponse.json({ message: "OpenAI usage logged successfully" });
  } catch (error) {
    console.error("Error logging OpenAI usage:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
};

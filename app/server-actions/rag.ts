"use server";

import prisma from "@/app/libs/prismadb";
import { RAGResponse } from "@prisma/client";

export type fetchedRagsType = Pick<
  RAGResponse,
  "conversation_id" | "selected_node_id" | "generation_output"
>;

export const fetchRags = async (
  conversationId: string
): Promise<fetchedRagsType[]> => {
  const rags: RAGResponse[] = await prisma.rAGResponse.findMany({
    where: { conversation_id: conversationId },
    orderBy: { created_at: "desc" },
  });

  const groupedRags = rags.reduce((groups, rag) => {
    const key = rag.selected_node_id;
    if (!groups[key]) {
      groups[key] = [];
    }
    groups[key].push(rag);
    return groups;
  }, {} as Record<string, RAGResponse[]>);

  const ragData = Object.values(groupedRags).map((rags) => {
    const latestRag = rags[0];
    return {
      conversation_id: latestRag.conversation_id,
      selected_node_id: latestRag.selected_node_id,
      generation_output: latestRag.generation_output,
    };
  });
  console.log("fetched rags from db using server-actions.ts");
  return ragData;
};

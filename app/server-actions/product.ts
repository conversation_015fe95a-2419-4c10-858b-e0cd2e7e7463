"use server";

import prisma from "@/app/libs/prismadb";
import { Product, Price } from "@prisma/client";

export const fetchProduct = async (): Promise<Product[]> => {
  const products = await prisma.product.findMany({
    where: {
      active: true,
    },
  });
  return products;
};

export const fetchPrice = async (): Promise<Price[]> => {
  const prices = await prisma.price.findMany({
    where: {
      active: true,
    },
  });
  return prices;
};

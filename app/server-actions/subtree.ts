"use server";

import prisma from "@/app/libs/prismadb";
import { SubtreeStatus, Subtree } from "@prisma/client";

export type fetchedSubtreesType = Pick<
  Subtree,
  | "id"
  | "conversation_id"
  | "issue_tree_id"
  | "selected_node_id"
  | "nodes"
  | "edges"
  | "generation_output"
>;

export const fetchSubtrees = async (
  conversationId: string
): Promise<fetchedSubtreesType[]> => {
  const subtrees = await prisma.subtree.findMany({
    where: {
      conversation_id: conversationId,
      status: SubtreeStatus.ACTIVE,
    },
    select: {
      id: true,
      conversation_id: true,
      issue_tree_id: true,
      selected_node_id: true,
      nodes: true,
      edges: true,
      generation_output: true,
    },
  });
  console.log("fetched subtrees from db using server-actions.ts");
  return subtrees;
};

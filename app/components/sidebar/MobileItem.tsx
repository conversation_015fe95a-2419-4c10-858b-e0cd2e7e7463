import Link from "next/link";
import clsx from "clsx";

interface MobileItemProps {
    href: string; // The URL to navigate to when the item is clicked
    icon: any; // The icon to display for the item
    active?: boolean; // Whether the item is currently active
    onClick?: () => void; // A function to call when the item is clicked
}

const MobileItem: React.FC<MobileItemProps> = ({
    href,
    icon: Icon,
    active,
    onClick,
}) => {
    // Call the onClick function if it exists
    const handleClick = () => {
        if (onClick) {
            return onClick();
        }
    };

    // Render a Link component with the appropriate styles and classes
    return (
        <Link
            onClick={handleClick}
            href={href}
            className={clsx(
                `
                group
                flex
                gap-x-3
                text-sm
                leading-6
                font-semibold
                w-full
                justify-center
                p-4
                text-gray-500
                hover:text-black
                hover:bg-gray-100
            `,
                active && "bg-gray-100 text-black"
            )}
        >
            <Icon className="h-6 w-6" />
        </Link>
    );
};

export default MobileItem;

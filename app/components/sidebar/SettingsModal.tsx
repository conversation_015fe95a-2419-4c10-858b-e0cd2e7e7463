"use client";

import React, { useState } from "react";
import { useRouter } from "next/navigation";
import { User } from "@prisma/client";
import { createStripePortal } from "@/lib/stripe/server";

import Modal from "../modals/Modal";
import Image from "next/image";
import { toast } from "react-hot-toast";
import { Button } from "@/components/ui/button";
import mixpanel from "@/app/libs/mixpanel";

type SettingsModalProps = {
  isOpen?: boolean;
  onClose: () => void;
  currentUser: User;
};

const SettingsModal: React.FC<SettingsModalProps> = ({
  isOpen,
  onClose,
  currentUser,
}) => {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);

  const showStripePortal = currentUser?.subscription_customer_id;

  const handleStripePortalRequest = async () => {
    if (!currentUser?.subscription_customer_id) {
      toast.error("You need to subscribe first to connect to your portal!");
      return;
    } else {
      {
        mixpanel.track("click_manage_subscription");
        setIsLoading(true);
        const redirectUrl = await createStripePortal(currentUser);
        setIsLoading(false);
        if (typeof redirectUrl === "string") {
          toast.success("Redirecting to Stripe...");
          return router.push(redirectUrl);
        } else if (redirectUrl instanceof Error) {
          toast.error(redirectUrl.message);
        }
      }
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose}>
      <div className="space-y-12">
        <div className="border-b border-gray-900/10 pb-12">
          <h2 className="text-base font-semibold leading-7 text-gray-900">
            Profile
          </h2>

          <div className="mt-10 flex flex-col gap-y-8">
            {/* Name */}
            <div>
              <label
                htmlFor="name"
                className="block text-sm font-bold leading-6 text-gray-900"
              >
                Name
              </label>
              <h4>{currentUser?.name || "<User Name>"}</h4>
            </div>
            {/* Email */}
            <div>
              <label
                htmlFor="email"
                className="block text-sm font-bold leading-6 text-gray-900"
              >
                Email
              </label>
              <h4>{currentUser?.email || "<User Email>"}</h4>
            </div>
            {/* Photo */}
            <div>
              <label
                htmlFor="photo"
                className="block text-sm font-bold leading-6 text-gray-900"
              >
                Photo
              </label>
              <div className="mt-2 flex items-center gap-x-3">
                <Image
                  width="48"
                  height="48"
                  className="rounded-full"
                  src={currentUser?.image || "/images/placeholder.jpg"}
                  alt="Avatar"
                />
              </div>
            </div>
            {/* Subscription */}
            {showStripePortal && (
              <>
                <label
                  htmlFor="subscription"
                  className="block text-sm font-bold leading-6 text-gray-900"
                >
                  Subscription
                </label>
                <h4>
                  Subscription ends at:{" "}
                  {currentUser?.subscription_end_date?.toISOString() ||
                    currentUser?.subscription_tier ||
                    "No subscription"}
                </h4>
                <>
                  {isLoading ? (
                    <Button className="rounded-full" disabled>
                      Loading...
                    </Button>
                  ) : (
                    <Button
                      className="rounded-full"
                      onClick={handleStripePortalRequest}
                    >
                      Manage your Stripe subscription
                    </Button>
                  )}
                </>
              </>
            )}
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default SettingsModal;

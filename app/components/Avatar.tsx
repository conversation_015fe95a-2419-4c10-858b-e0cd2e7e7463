"use client";

import { User } from "@prisma/client";

import Image from "next/image";
import { isUserSubscribing } from "@/lib/utils";

interface AvatarProps {
  user?: User;
}

const Avatar: React.FC<AvatarProps> = ({ user }) => {
  const isSubscribed = isUserSubscribing(user || null);

  return (
    <div className="relative">
      <div className="relative inline-block rounded-full overflow-hidden h-9 w-9 md:h-11 md:w-11">
        <Image
          fill
          src={user?.image || "/images/robot.png"}
          alt="Avatar"
          sizes="100px"
        />
      </div>
      {isSubscribed && (
        <div className="absolute -top-2 -right-2 bg-yellow-400 rounded-full w-6 h-6 flex items-center justify-center text-lg shadow-md transform rotate-45">
          👑
        </div>
      )}
    </div>
  );
};

export default Avatar;

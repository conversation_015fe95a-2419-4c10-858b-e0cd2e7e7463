'use client'

import React, { useCallback, useEffect, useState } from 'react'
import { JSONContent } from '@tiptap/react'
import { cn } from '@/lib/utils'
import { motion, AnimatePresence } from 'framer-motion'
import {
  useBaseTiptapEditor,
  processContentForEditor,
} from './BaseTiptapEditor'
import { EditorBubbleMenu, TableBubbleMenu } from './components'
import { EditorContent as TiptapEditorContent } from '@tiptap/react'
import QuickResearchDropdownButton from '@/app/(conv)/dragTree/[dragTreeId]/components/shared/QuickResearchDropdownButton'

export type TiptapQuickResearchEditorProps = {
  content: string
  onContentChange?: (content: string) => void
  onJSONChange?: (content: JSONContent) => void
  isReadOnly?: boolean
  isStreaming?: boolean
  placeholder?: string
  className?: string
  showBubbleMenu?: boolean
  onClick?: (event: React.MouseEvent) => void
  debounceMs?: number
  autoFocus?: boolean
  onEditorReady?: (editor: any) => void
  /**
   * Compact mode is used for embedded displays (e.g. tree outline cards).
   * It applies smaller typography and removes any max-width so the editor
   * never overflows the parent card.
   */
  compact?: boolean
  /**
   * Optional props for showing quick research button
   */
  questionText?: string
  questionNodeId?: string
  showResearchButton?: boolean
}

// Status display for save/edit states
type EditStatus = 'editing' | 'saving' | 'saved' | 'idle'

export const TiptapQuickResearchEditor: React.FC<
  TiptapQuickResearchEditorProps
> = ({
  content,
  onContentChange,
  onJSONChange,
  isReadOnly = false,
  isStreaming = false,
  placeholder = 'Research content',
  className = '',
  showBubbleMenu = true,
  onClick,
  debounceMs = 1000,
  autoFocus = false,
  onEditorReady,
  compact = false,
  questionText,
  questionNodeId,
  showResearchButton = false,
}) => {
  const [editStatus, setEditStatus] = useState<EditStatus>('idle')
  const [isEditable, setIsEditable] = useState<boolean>(
    !isReadOnly && !isStreaming
  )

  // Handle content updates with proper debouncing and status tracking
  const handleContentUpdate = useCallback(
    (jsonContent: JSONContent) => {
      setEditStatus('saving')

      // Always save as JSON when user edits in Tiptap
      // This upgrades markdown content to rich text format automatically
      const jsonString = JSON.stringify(jsonContent)
      onContentChange?.(jsonString)
      onJSONChange?.(jsonContent)

      // Show saved status briefly
      setTimeout(() => {
        setEditStatus('saved')
        setTimeout(() => {
          setEditStatus('idle')
        }, 2000)
      }, 500)
    },
    [content, onContentChange, onJSONChange]
  )

  const { editor, setContent, setEditable } = useBaseTiptapEditor({
    content,
    placeholder,
    editable: isEditable,
    onUpdate: handleContentUpdate,
    // Always persist JSON from the editor. If the initial content was plain
    // markdown we intentionally upgrade it to the rich-text JSON format to
    // avoid costly markdown ↔︎ JSON conversions on every keystroke.
    onTextUpdate: undefined,
    debounceMs,
    autofocus: autoFocus ? 'start' : false,
    immediatelyRender: false,
  })

  // Call onEditorReady when editor is available
  useEffect(() => {
    if (editor && onEditorReady) {
      onEditorReady(editor)
    }
  }, [editor, onEditorReady])

  // Update editable state based on props
  useEffect(() => {
    const shouldBeEditable = !isReadOnly && !isStreaming
    if (isEditable !== shouldBeEditable) {
      setIsEditable(shouldBeEditable)
      setEditable(shouldBeEditable)
    }
  }, [isReadOnly, isStreaming, isEditable, setEditable])

  // Update content when prop changes (e.g., during streaming)
  useEffect(() => {
    if (editor && content !== undefined) {
      const currentContent = editor.getJSON()
      const newProcessedContent = processContentForEditor(content)

      // Only update if content actually changed and we're not currently editing
      if (
        JSON.stringify(currentContent) !==
          JSON.stringify(newProcessedContent) &&
        (isStreaming || !isEditable)
      ) {
        setContent(content)
      }
    }
  }, [content, editor, isStreaming, isEditable, setContent])

  const handleClick = (event: React.MouseEvent) => {
    // Allow text selection and editing within the editor
    if (!isEditable && !isStreaming) {
      setIsEditable(true)
      setEditable(true)
      setEditStatus('editing')

      // Give the editor a tick to switch into editable mode before focusing.
      // This avoids a race condition where focus is called while contenteditable
      // is still disabled.
      setTimeout(() => {
        if (editor && !editor.isDestroyed) {
          editor.commands.focus('end')
        }
      }, 50)
    }

    onClick?.(event)
  }

  const handleEditorClick = (event: React.MouseEvent) => {
    // Prevent event bubbling to parent when clicking inside editor
    event.stopPropagation()

    if (!isEditable && !isStreaming) {
      setIsEditable(true)
      setEditable(true)
      setEditStatus('editing')

      // Focus the editor after a small delay
      setTimeout(() => {
        if (editor && !editor.isDestroyed) {
          editor.commands.focus()
        }
      }, 100)
    }
  }

  return (
    <div
      className={cn(
        `relative w-full transition-all duration-300 max-w-full`,
        {
          'cursor-pointer': !isEditable && !isReadOnly && !isStreaming,
          'cursor-text': isEditable,
        },
        className
      )}
      onClick={handleClick}
    >
      {/* Editor Content */}
      <div
        className={cn(
          `${
            compact ? 'min-h-[100px] max-h-[100px]' : 'min-h-[180px]'
          } w-full transition-all duration-300 rounded-md border ${
            compact ? 'max-w-full' : 'overflow-hidden'
          }`,
          {
            'bg-white border-blue-200 focus-within:border-blue-400':
              isEditable && !isStreaming,
            'bg-gray-50 border-gray-200 hover:bg-gray-100 cursor-pointer':
              !isEditable && !isStreaming,
            'bg-gray-50 border-gray-200': isStreaming,
          }
        )}
        onClick={handleEditorClick}
      >
        {showBubbleMenu && isEditable && !isStreaming && editor && (
          <>
            <EditorBubbleMenu editor={editor} />
            <TableBubbleMenu editor={editor} />
          </>
        )}

        <div
          className={cn(compact ? 'px-2 py-0.5' : 'px-3 py-2')}
          style={{
            // Ensure inner content can't exceed parent width
            maxWidth: '100%',
          }}
        >
          <TiptapEditorContent
            editor={editor}
            className={cn(
              'prose prose-sm leading-tight w-full',
              '[&>*]:my-0.5 [&_p]:my-0.5 [&_h1]:my-1 [&_h2]:my-1 [&_h3]:my-1 [&_ul]:my-0.5 [&_ol]:my-0.5 [&_li]:my-0',
              '[&_h1]:text-base [&_h2]:text-sm [&_h3]:text-sm',
              '[&_.ProseMirror]:w-full [&_.ProseMirror]:max-w-full [&_.ProseMirror]:overflow-x-hidden',
              '[&_.ProseMirror]:min-w-0 [&_.ProseMirror]:box-border [&_.ProseMirror]:word-wrap [&_.ProseMirror]:break-words',
              '[&_.ProseMirror]:hyphens-auto [&_.ProseMirror_*]:max-w-full [&_.ProseMirror_*]:word-break-break-word',
              compact
                ? '[&_.ProseMirror]:max-h-[88px] [&_.ProseMirror]:overflow-y-auto'
                : '[&_.ProseMirror]:overflow-y-auto',
              {
                'prose-gray': !isEditable || isStreaming,
                'text-xs': compact,
              }
            )}
            style={{
              fontSize: compact ? '0.75rem' : '0.875rem',
              lineHeight: compact ? '1.25' : '1.4',
              maxWidth: '100%',
              width: '100%',
              wordWrap: 'break-word',
              overflowWrap: 'break-word',
              hyphens: 'auto',
              wordBreak: 'break-word',
              ...(compact ? { maxHeight: '88px', overflow: 'auto' } : {}),
            }}
          />
        </div>
      </div>

      {/* Status Footer */}
      <div className="flex items-center justify-between text-xs text-gray-400 h-5 pr-2 mt-1">
        {/* Research Button */}
        <div className="flex-shrink-0">
          {showResearchButton && questionText && questionNodeId && (
            <QuickResearchDropdownButton
              questionText={questionText}
              questionNodeId={questionNodeId}
              variant="treeview"
              className="scale-75 origin-left"
            />
          )}
        </div>

        {/* Status Messages */}
        <div className="flex-1 flex justify-end">
          <AnimatePresence mode="wait">
            {editStatus === 'saving' && (
              <motion.div
                key="saving"
                initial={{ opacity: 0, y: 5 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -5 }}
                className="flex items-center"
              >
                <span className="mr-1.5 w-1.5 h-1.5 rounded-full bg-amber-500 animate-pulse"></span>
                Saving...
              </motion.div>
            )}
            {editStatus === 'saved' && (
              <motion.div
                key="saved"
                initial={{ opacity: 0, y: 5 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -5 }}
                className="text-green-600"
              >
                Saved
              </motion.div>
            )}
            {editStatus === 'idle' && !isEditable && !isStreaming && (
              <motion.div
                key="edit"
                initial={{ opacity: 0, y: 5 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -5 }}
              >
                Click to edit with rich text
              </motion.div>
            )}
            {isStreaming && (
              <motion.div
                key="streaming"
                initial={{ opacity: 0, y: 5 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -5 }}
                className="flex items-center text-blue-600"
              >
                <span className="mr-1.5 w-1.5 h-1.5 rounded-full bg-blue-500 animate-pulse"></span>
                Streaming...
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>
    </div>
  )
}

export default TiptapQuickResearchEditor

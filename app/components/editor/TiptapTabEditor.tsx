'use client'

import React, { useCallback, useEffect, useState } from 'react'
import { JSONContent } from '@tiptap/react'
import { cn } from '@/lib/utils'
import { motion, AnimatePresence } from 'framer-motion'
import {
  useBaseTiptapEditor,
  processContentForEditor,
} from './BaseTiptapEditor'
import { EditorBubbleMenu, TableBubbleMenu } from './components'
import { EditorContent as TiptapEditorContent } from '@tiptap/react'

export type TiptapTabEditorProps = {
  content: string
  onContentChange?: (content: string) => void
  onJSONChange?: (content: JSONContent) => void
  isReadOnly?: boolean
  isStreaming?: boolean
  placeholder?: string
  className?: string
  debounceMs?: number
  autoFocus?: boolean
}

// Status display for save/edit states
type EditStatus = 'editing' | 'saving' | 'saved' | 'idle'

export const TiptapTabEditor: React.FC<TiptapTabEditorProps> = ({
  content,
  onContentChange,
  onJSONChange,
  isReadOnly = false,
  isStreaming = false,
  placeholder = 'Research content',
  className = '',
  debounceMs = 1000,
  autoFocus = false,
}) => {
  const [editStatus, setEditStatus] = useState<EditStatus>('idle')
  const [isEditable, setIsEditable] = useState<boolean>(
    !isReadOnly && !isStreaming
  )

  // Handle content updates with proper debouncing and status tracking
  const handleContentUpdate = useCallback(
    (jsonContent: JSONContent) => {
      setEditStatus('saving')

      // Always save as JSON when user edits in Tiptap
      const jsonString = JSON.stringify(jsonContent)
      onContentChange?.(jsonString)
      onJSONChange?.(jsonContent)

      // Show saved status briefly
      setTimeout(() => {
        setEditStatus('saved')
        setTimeout(() => {
          setEditStatus('idle')
        }, 2000)
      }, 500)
    },
    [content, onContentChange, onJSONChange]
  )

  const { editor, setContent, setEditable } = useBaseTiptapEditor({
    content,
    placeholder,
    editable: isEditable,
    onUpdate: handleContentUpdate,
    onTextUpdate: undefined,
    debounceMs,
    autofocus: autoFocus ? 'start' : false,
    immediatelyRender: false,
  })

  // Update editable state based on props
  useEffect(() => {
    const shouldBeEditable = !isReadOnly && !isStreaming
    if (isEditable !== shouldBeEditable) {
      setIsEditable(shouldBeEditable)
      setEditable(shouldBeEditable)
    }
  }, [isReadOnly, isStreaming, isEditable, setEditable])

  // Update content when prop changes (e.g., during streaming)
  useEffect(() => {
    if (editor && content !== undefined) {
      const currentContent = editor.getJSON()
      const newProcessedContent = processContentForEditor(content)

      if (
        JSON.stringify(currentContent) !== JSON.stringify(newProcessedContent)
      ) {
        // Sync editor with external content updates (e.g., lazy-loaded data)
        setContent(content)
      }
    }
  }, [content, editor, isStreaming, isEditable, setContent])

  // Apply direct styles to ProseMirror for full height behavior
  useEffect(() => {
    if (editor) {
      const proseMirrorElement = editor.view.dom as HTMLElement
      if (proseMirrorElement) {
        proseMirrorElement.style.flex = '1'
        proseMirrorElement.style.minHeight = '0'
        proseMirrorElement.style.overflowY = 'auto'
        proseMirrorElement.style.height = '100%'
      }
    }
  }, [editor])

  const handleClick = (event: React.MouseEvent) => {
    if (!isEditable && !isStreaming) {
      setIsEditable(true)
      setEditable(true)
      setEditStatus('editing')
      setTimeout(() => {
        if (editor && !editor.isDestroyed) {
          editor.commands.focus('end')
        }
      }, 50)
    }
  }

  const handleEditorClick = (event: React.MouseEvent) => {
    event.stopPropagation()
    if (!isEditable && !isStreaming) {
      setIsEditable(true)
      setEditable(true)
      setEditStatus('editing')
      setTimeout(() => {
        if (editor && !editor.isDestroyed) {
          editor.commands.focus()
        }
      }, 100)
    }
  }

  return (
    <div
      className={cn(
        `relative w-full transition-all duration-300 flex flex-col h-full min-h-0`,
        {
          'cursor-pointer': !isEditable && !isReadOnly && !isStreaming,
          'cursor-text': isEditable,
        },
        className
      )}
      onClick={handleClick}
    >
      {/* Editor Content */}
      <div
        className={cn(
          'flex-1 min-h-0 flex flex-col w-full transition-all duration-300 rounded-md border',
          {
            'bg-white border-blue-200 focus-within:border-blue-400':
              isEditable && !isStreaming,
            'bg-gray-50 border-gray-200 hover:bg-gray-100 cursor-pointer':
              !isEditable && !isStreaming,
            'bg-gray-50 border-gray-200': isStreaming,
          }
        )}
        onClick={handleEditorClick}
      >
        {isEditable && !isStreaming && editor && (
          <>
            <EditorBubbleMenu editor={editor} />
            <TableBubbleMenu editor={editor} />
          </>
        )}
        <div className="px-3 py-2 flex-1 min-h-0" style={{ maxWidth: '100%' }}>
          <TiptapEditorContent
            editor={editor}
            className="prose prose-sm leading-tight w-full"
            style={{
              fontSize: '0.875rem',
              lineHeight: '1.4',
              maxWidth: '100%',
              width: '100%',
              wordWrap: 'break-word',
              overflowWrap: 'break-word',
              hyphens: 'auto',
              wordBreak: 'break-word',
              height: '100%',
            }}
          />
        </div>
      </div>

      {/* Status Footer */}
      <div className="flex items-center justify-end text-xs text-gray-400 h-5 pr-2 mt-1">
        <AnimatePresence mode="wait">
          {editStatus === 'saving' && (
            <motion.div
              key="saving"
              initial={{ opacity: 0, y: 5 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -5 }}
              className="flex items-center"
            >
              <span className="mr-1.5 w-1.5 h-1.5 rounded-full bg-amber-500 animate-pulse"></span>
              Saving...
            </motion.div>
          )}
          {editStatus === 'saved' && (
            <motion.div
              key="saved"
              initial={{ opacity: 0, y: 5 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -5 }}
              className="text-green-600"
            >
              Saved
            </motion.div>
          )}
          {editStatus === 'idle' && !isEditable && !isStreaming && (
            <motion.div
              key="edit"
              initial={{ opacity: 0, y: 5 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -5 }}
            >
              Click to edit with rich text
            </motion.div>
          )}
          {isStreaming && (
            <motion.div
              key="streaming"
              initial={{ opacity: 0, y: 5 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -5 }}
              className="flex items-center text-blue-600"
            >
              <span className="mr-1.5 w-1.5 h-1.5 rounded-full bg-blue-500 animate-pulse"></span>
              Streaming...
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  )
}

export default TiptapTabEditor

'use client'

import { B<PERSON>bleMenu, BubbleMenuProps, Editor } from '@tiptap/react'
import { FC } from 'react'
import {
  Trash2,
  <PERSON>umns,
  Rows,
  ArrowUpFromLine,
  ArrowDownToLine,
  ArrowLeftToLine,
  ArrowRightFromLine,
  Split,
  Table,
} from 'lucide-react'
import { cn } from '@/lib/utils'

export interface BubbleMenuItem {
  name: string
  isActive: () => boolean
  command: () => void
  icon: React.ElementType
  color?: string
}

type TableBubbleMenuProps = Omit<BubbleMenuProps, 'children'>

const TableBubbleMenu: FC<TableBubbleMenuProps> = props => {
  if (!props.editor) {
    return null
  }
  const editor = props.editor as Editor

  const items: BubbleMenuItem[] = [
    {
      name: 'Insert Row Above',
      command: () => editor.chain().focus().addRowBefore().run(),
      isActive: () => false,
      icon: ArrowUpFromLine,
      color: 'text-blue-500',
    },
    {
      name: 'Insert Row Below',
      command: () => editor.chain().focus().addRowAfter().run(),
      isActive: () => false,
      icon: ArrowDownToLine,
      color: 'text-blue-500',
    },
    {
      name: 'Delete Row',
      command: () => editor.chain().focus().deleteRow().run(),
      isActive: () => false,
      icon: Rows,
      color: 'text-red-500',
    },
    {
      name: 'Insert Column Before',
      command: () => editor.chain().focus().addColumnBefore().run(),
      isActive: () => false,
      icon: ArrowLeftToLine,
      color: 'text-blue-500',
    },
    {
      name: 'Insert Column After',
      command: () => editor.chain().focus().addColumnAfter().run(),
      isActive: () => false,
      icon: ArrowRightFromLine,
      color: 'text-blue-500',
    },
    {
      name: 'Delete Column',
      command: () => editor.chain().focus().deleteColumn().run(),
      isActive: () => false,
      icon: Columns,
      color: 'text-red-500',
    },
    {
      name: 'Delete Table',
      command: () => editor.chain().focus().deleteTable().run(),
      isActive: () => false,
      icon: Trash2,
      color: 'text-red-500',
    },
  ]

  const bubbleMenuProps: Omit<BubbleMenuProps, 'children'> = {
    ...props,
    shouldShow: ({ editor }) => editor.isActive('table'),
    tippyOptions: {
      placement: 'bottom-start',
      moveTransition: 'transform 0.15s ease-out',
      appendTo: 'parent',
    },
  }

  return (
    <BubbleMenu
      {...bubbleMenuProps}
      className="flex w-fit divide-x divide-stone-200 rounded-md border border-stone-200 bg-white shadow-xl"
    >
      <div className="flex">
        {items.map((item, index) => (
          <button
            key={index}
            onClick={item.command}
            className="p-2 text-stone-600 hover:bg-stone-100 active:bg-stone-200"
            title={item.name}
          >
            <item.icon
              className={cn('h-4 w-4', item.color, {
                'text-blue-500': item.isActive(),
              })}
            />
          </button>
        ))}
      </div>
    </BubbleMenu>
  )
}

export default TableBubbleMenu

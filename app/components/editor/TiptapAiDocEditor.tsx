import React, { use<PERSON>allback, useEffect, useState } from 'react'
import { JSONContent } from '@tiptap/react'
import { cn } from '@/lib/utils'
import { motion, AnimatePresence } from 'framer-motion'
import {
  useBaseTiptapEditor,
  processContentForEditor,
} from './BaseTiptapEditor'
import { EditorBubbleMenu, TableBubbleMenu } from './components'
import { EditorContent as TiptapEditorContent } from '@tiptap/react'

export type EditStatus = 'editing' | 'saving' | 'saved' | 'idle'

export type TiptapAiDocEditorProps = {
  /**
   * Current document content as **markdown or Tiptap JSON string**.
   * The editor will convert markdown to rich-text JSON automatically.
   */
  content: string
  /** Callback invoked with full Tiptap JSON whenever the user edits. */
  onJSONChange?: (content: JSONContent) => void
  /** Callback invoked with markdown version (if you need plain text). */
  onMarkdownChange?: (markdown: string) => void
  /** Render in read-only mode (e.g. when streaming). */
  isReadOnly?: boolean
  /** True while AI content is still streaming – disables editing. */
  isStreaming?: boolean
  /** Placeholder shown when empty. */
  placeholder?: string
  /** Additional className wrapper. */
  className?: string
  /** Debounce ms for save callbacks – defaults to 1s. */
  debounceMs?: number
  /** Autofocus editor. */
  autoFocus?: boolean
  onStatusChange?: (status: EditStatus) => void
  /** Show bubble menu formatting toolbar. */
  showBubbleMenu?: boolean
}

/**
 * Dedicated rich-text editor for AI-generated documents.
 * – Provides inline Saving/ Saved indicators
 * – Debounces server persistence via parent callbacks
 * – No research-specific UI, purely a doc editor
 */
const TiptapAiDocEditor: React.FC<TiptapAiDocEditorProps> = ({
  content,
  onJSONChange,
  onMarkdownChange,
  isReadOnly = false,
  isStreaming = false,
  placeholder = 'Edit generated content...',
  className = '',
  debounceMs = 1000,
  autoFocus = false,
  showBubbleMenu = true,
  onStatusChange,
}) => {
  const [editStatus, setEditStatus] = useState<EditStatus>('idle')
  const [isEditable, setIsEditable] = useState<boolean>(
    !isReadOnly && !isStreaming
  )

  // Debounced content handler
  useEffect(() => {
    onStatusChange?.(editStatus)
  }, [editStatus, onStatusChange])

  const handleContentUpdate = useCallback(
    (jsonContent: JSONContent) => {
      setEditStatus('saving')

      onJSONChange?.(jsonContent)

      if (onMarkdownChange) {
        // Convert JSON to markdown using Tiptap markdown extension
        // We rely on editor.storage.markdown.getMarkdown via useBaseTiptapEditor
      }

      // Show Saved indicator briefly after debounce period
      setTimeout(() => {
        setEditStatus('saved')
        setTimeout(() => setEditStatus('idle'), 2000)
      }, 500)
    },
    [onJSONChange, onMarkdownChange]
  )

  const { editor, setContent, setEditable } = useBaseTiptapEditor({
    content,
    placeholder,
    editable: isEditable,
    onUpdate: handleContentUpdate,
    // Only JSON callback needed here; markdown handled by parent if desired
    onTextUpdate: undefined,
    debounceMs,
    autofocus: autoFocus ? 'start' : false,
    immediatelyRender: false,
  })

  // Update editable mode when props change
  useEffect(() => {
    const shouldBeEditable = !isReadOnly && !isStreaming
    if (shouldBeEditable !== isEditable) {
      setIsEditable(shouldBeEditable)
      setEditable(shouldBeEditable)
    }
  }, [isReadOnly, isStreaming, isEditable, setEditable])

  // Keep editor content in sync when external changes arrive (e.g. streaming)
  useEffect(() => {
    if (editor && content !== undefined) {
      const currentContent = editor.getJSON()
      const newProcessed = processContentForEditor(content)
      if (
        JSON.stringify(currentContent) !== JSON.stringify(newProcessed) &&
        (isStreaming || !isEditable)
      ) {
        setContent(content)
      }
    }
  }, [content, editor, isStreaming, isEditable, setContent])

  // Click handler – enter edit mode automatically
  const handleWrapperClick = () => {
    if (!isEditable && !isStreaming) {
      setIsEditable(true)
      setEditable(true)
      setEditStatus('editing')
      setTimeout(() => {
        if (editor && !editor.isDestroyed) editor.commands.focus('end')
      }, 50)
    }
  }

  return (
    <div
      className={cn('relative w-full', className, {
        'cursor-pointer': !isEditable && !isReadOnly && !isStreaming,
        'cursor-text': isEditable,
      })}
      onClick={handleWrapperClick}
    >
      {/* Editor Container */}
      <div
        className={cn('min-h-[180px] w-full rounded-md border transition-all', {
          'bg-white border-blue-200 focus-within:border-blue-400':
            isEditable && !isStreaming,
          'bg-gray-50 border-gray-200 hover:bg-gray-100':
            !isEditable && !isStreaming,
          'bg-gray-50 border-gray-200': isStreaming,
        })}
      >
        {showBubbleMenu && isEditable && !isStreaming && editor && (
          <>
            <EditorBubbleMenu editor={editor} />
            <TableBubbleMenu editor={editor} />
          </>
        )}

        <div className="px-3 py-2">
          <TiptapEditorContent
            editor={editor}
            className={cn(
              'prose prose-sm dark:prose-invert w-full',
              '[&>*]:my-0.5',
              {
                'prose-gray': !isEditable || isStreaming,
              }
            )}
            style={{ maxWidth: '100%' }}
          />
        </div>
      </div>

      {/* Status Footer */}
      <div className="flex justify-end text-xs text-gray-400 h-5 pr-2 mt-1">
        <AnimatePresence mode="wait">
          {editStatus === 'saving' && (
            <motion.div
              key="saving"
              initial={{ opacity: 0, y: 5 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -5 }}
              className="flex items-center"
            >
              <span className="mr-1.5 w-1.5 h-1.5 rounded-full bg-amber-500 animate-pulse" />
              Saving...
            </motion.div>
          )}
          {editStatus === 'saved' && (
            <motion.div
              key="saved"
              initial={{ opacity: 0, y: 5 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -5 }}
              className="text-green-600"
            >
              Saved
            </motion.div>
          )}
          {editStatus === 'idle' && !isEditable && !isStreaming && (
            <motion.div
              key="idle"
              initial={{ opacity: 0, y: 5 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -5 }}
            >
              Click to edit
            </motion.div>
          )}
          {isStreaming && (
            <motion.div
              key="streaming"
              initial={{ opacity: 0, y: 5 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -5 }}
              className="flex items-center text-blue-600"
            >
              <span className="mr-1.5 w-1.5 h-1.5 rounded-full bg-blue-500 animate-pulse" />
              Streaming...
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  )
}

export default TiptapAiDocEditor

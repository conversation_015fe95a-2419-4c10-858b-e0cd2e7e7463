'use client'

import { mergeAttributes, Node, textblockTypeInputRule } from '@tiptap/core'
import { ReactNodeViewRenderer } from '@tiptap/react'
import MermaidBlockComponent from '@/app/components/editor/components/MermaidBlockComponent'

// New clean mermaid extension without hacky data access
export default Node.create({
  name: 'mermaidBlock',

  group: 'block',

  // Block-level element that cannot contain content
  atom: true,

  // Block element behavior
  isolating: true,

  addAttributes() {
    return {
      // Store mermaid code directly in attrs for clean data access
      code: {
        default: '',
        parseHTML: element => {
          const raw = element.getAttribute('data-mermaid-code') || ''
          try {
            return decodeURIComponent(raw)
          } catch {
            return raw
          }
        },
        renderHTML: attributes => ({
          'data-mermaid-code': encodeURIComponent(attributes.code || ''),
        }),
      },
    }
  },

  parseHTML() {
    return [
      {
        tag: "div[data-type='mermaid-block']",
      },
      // Parse from markdown code blocks with mermaid language
      {
        tag: 'pre',
        getAttrs: element => {
          const codeElement = element.querySelector('code.language-mermaid')
          if (codeElement) {
            return {
              code: codeElement.textContent || '',
            }
          }
          return false
        },
      },
      // Also parse from code blocks directly (for when markdown parser creates them)
      {
        tag: 'code',
        getAttrs: element => {
          if (element.classList.contains('language-mermaid')) {
            return {
              code: element.textContent || '',
            }
          }
          return false
        },
      },
    ]
  },

  renderHTML({ HTMLAttributes, node }) {
    return [
      'div',
      mergeAttributes(
        {
          'data-type': 'mermaid-block',
          'data-mermaid-code': encodeURIComponent(node.attrs.code || ''),
        },
        HTMLAttributes
      ),
    ]
  },

  addNodeView() {
    return ReactNodeViewRenderer(MermaidBlockComponent)
  },

  // Support markdown serialization and parsing
  addStorage() {
    return {
      markdown: {
        serialize: (state: any, node: any) => {
          // Output as markdown code block with mermaid language
          const code = node.attrs.code || ''
          state.write('```mermaid\n')
          state.text(code, false)
          state.write('\n```')
          state.closeBlock(node)
        },
        parse: {
          // This doesn't get called automatically, but we can use it for reference
          setup: (markdownit: any) => {
            // Override fence rendering for mermaid blocks
            const defaultFence =
              markdownit.renderer.rules.fence ||
              function (
                tokens: any,
                idx: any,
                options: any,
                env: any,
                renderer: any
              ) {
                return renderer.renderToken(tokens, idx, options)
              }

            markdownit.renderer.rules.fence = function (
              tokens: any,
              idx: any,
              options: any,
              env: any,
              renderer: any
            ) {
              const token = tokens[idx]
              const info = token.info ? token.info.trim() : ''

              if (info === 'mermaid') {
                // Return a div that our parseHTML will catch
                const code = token.content.trim()
                return `<div data-type="mermaid-block" data-mermaid-code="${encodeURIComponent(code)}"></div>`
              }

              // Use default fence rendering for other code blocks
              return defaultFence(tokens, idx, options, env, renderer)
            }
          },
        },
      },
    }
  },

  addInputRules() {
    return [
      // Rule to transform ```mermaid code blocks into mermaid nodes
      textblockTypeInputRule({
        find: /^```mermaid\s*$/,
        type: this.type,
        getAttributes: () => ({ code: '' }),
      }),
    ]
  },

  addPasteRules() {
    return [
      {
        find: /```mermaid\n([\s\S]*?)\n```/g,
        handler: ({ state, range, match }) => {
          const [, code] = match
          const { tr } = state
          const start = range.from
          const end = range.to

          tr.replaceWith(start, end, this.type.create({ code: code.trim() }))
        },
      },
    ]
  },
})

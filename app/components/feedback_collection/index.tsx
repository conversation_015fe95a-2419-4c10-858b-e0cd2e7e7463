import { DialogContent, DialogTitle } from "@/components/ui/dialog";
import React, { useState } from "react";
import { Rating } from "@smastrom/react-rating";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { Button } from "@/components/ui/button";
import toast from "react-hot-toast";
import mixpanel from "@/app/libs/mixpanel";
import {
  createFeedback,
  createFeedbackType,
} from "@/app/server-actions/feedback";

type RatingKeys = "smoothness" | "quality" | "effort";
type NullableRating = Record<RatingKeys, number | null>;

const questionTextMap: Record<RatingKeys, string> = {
  smoothness: "Overall Flow; Smoothness of Journey",
  quality: "Relevance and Clarity of Questions",
  effort:
    "Did we overwhelm you with the task load? [one: overwhelming; five: reasonable]",
};

interface FeedbackCollectionProps {
  conversationId: string;
  setShowFeedback: (showFeedback: boolean) => void;
}

const FeedbackCollection: React.FC<FeedbackCollectionProps> = ({
  conversationId,
  setShowFeedback,
}) => {
  const [ratings, setRatings] = useState<NullableRating>({
    smoothness: null,
    quality: null,
    effort: null,
  });
  const [feedback, setFeedback] = useState<string>("");
  const [canContact, setCanContact] = useState<boolean>(false);

  const handleRating = (question: RatingKeys, rate: number) => {
    setRatings((prevRatings) => ({
      ...prevRatings,
      [question]: rate,
    }));
  };

  const handleFinishClick = async () => {
    if (isFeedbackComplete) {
      const outputObject = {
        ...ratings,
        canContact: canContact,
        feedback: feedback,
        questionTextMap: questionTextMap,
      };

      mixpanel.track("finish_feedback_click");

      const createFeedbackData: createFeedbackType = {
        conversationId,
        outputObject,
        canContact,
      };
      try {
        await createFeedback(createFeedbackData);
        setShowFeedback(false);
      } catch (error) {
        toast.error("Something went wrong. Please try again.");
      }
    } else {
      toast.error("Please complete all ratings before submitting");
    }
  };

  // Check if all ratings are provided
  const isFeedbackComplete = Object.values(ratings).every(
    (rate) => rate !== null && rate !== 0
  );

  return (
    <DialogContent className="lg:max-w-[900px] sm:max-w-[800px] p-6 overflow-hidden">
      <DialogTitle className="flex justify-center">
        Short Feedback Survey before notebook generations
      </DialogTitle>

      <div className="flex flex-row self-center p-4 mx-12 justify-between">
        <div className="flex flex-col max-w-sm p-4 mx-auto bg-gray-200 rounded-lg shadow-lg flex-grow">
          <div className="flex flex-col justify-between flex-grow">
            {(["smoothness", "quality", "effort"] as RatingKeys[]).map(
              (question, idx) => (
                <div key={idx} className="flex flex-col mb-4">
                  <label className="mb-2">{questionTextMap[question]}</label>
                  <Rating
                    value={ratings[question] ?? 0}
                    onChange={(rate: number) => handleRating(question, rate)}
                  />
                </div>
              )
            )}
            <Textarea
              className="w-full h-16 text-sm p-1 rounded-md transition-shadow overflow-auto bg-green-50 border-green-300"
              placeholder="Anything you want to share"
              value={feedback}
              onChange={(e) => setFeedback(e.target.value)}
            />
            <div className="flex items-center space-x-2 py-2 flex-shrink-0">
              <Checkbox
                id="terms"
                checked={canContact}
                onCheckedChange={(e) => {
                  if (typeof e === "boolean") {
                    setCanContact(e);
                  } else if (e === "indeterminate") {
                    setCanContact(false);
                  }
                }}
              />

              <label
                htmlFor="terms"
                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
              >
                Is it okay to contact you for more feedback?
              </label>
            </div>
            <Button
              disabled={!isFeedbackComplete}
              className={`self-center p-4 text-white rounded m-4 ${
                isFeedbackComplete
                  ? "bg-green-400 hover:bg-green-500"
                  : "bg-gray-400 cursor-not-allowed"
              } max-w-xl`}
              onClick={handleFinishClick}
            >
              Submit Feedback
            </Button>
          </div>
        </div>
      </div>
    </DialogContent>
  );
};

export default FeedbackCollection;

import { Button } from "@/components/ui/button";
import React, { useEffect, useRef, useState } from "react";

interface SuggestedRefinedQuestionsProps {
  suggestions: string[];
  onSuggestionClick: (suggestion: string) => void;
  isLoading?: boolean;
}

// Function to bold the text inside the first pair of square brackets
const formatSuggestion = (suggestion: string) => {
  const match = suggestion.match(/\[(.*?)\]/);
  if (match) {
    const boldText = match[1];
    return (
      <>
        <strong>{boldText}</strong>
        {suggestion.replace(match[0], "")}
      </>
    );
  }
  return suggestion;
};

const SuggestedRefinedQuestions: React.FC<SuggestedRefinedQuestionsProps> = ({
  suggestions,
  onSuggestionClick,
  isLoading,
}) => {
  const bgColorClasses = [
    "bg-red-100",
    "bg-green-100",
    "bg-blue-100",
    "bg-yellow-100",
    "bg-purple-100",
    "bg-pink-100",
  ];

  const textRefs = useRef<(HTMLDivElement | null)[]>([]);
  const [expandedItems, setExpandedItems] = useState<boolean[]>([]);
  const [needsExpansion, setNeedsExpansion] = useState<boolean[]>([]);

  useEffect(() => {
    const checkNeedsExpansion = () => {
      const newNeedsExpansion = textRefs.current.map((ref) => {
        if (ref) {
          return ref.scrollHeight > ref.clientHeight;
        }
        return false;
      });
      setNeedsExpansion(newNeedsExpansion);
    };

    checkNeedsExpansion();
    window.addEventListener("resize", checkNeedsExpansion);

    return () => {
      window.removeEventListener("resize", checkNeedsExpansion);
    };
  }, [suggestions]);

  const toggleExpand = (index: number) => {
    setExpandedItems((prev) => {
      const newState = [...prev];
      newState[index] = !newState[index];
      return newState;
    });
  };

  return (
    <div className="rounded-lg p-4 w-full">
      <h1 className="text-xl">
        Choose the one that best describes your intention
      </h1>
      <h2 className="px-4 text-sm">
        * You can continue to refine after selection
      </h2>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {suggestions.map((suggestion, i) => (
          <div
            key={i}
            className={`flex flex-col md:flex-row items-start ${
              bgColorClasses[i % bgColorClasses.length]
            } hover:bg-opacity-75 rounded p-3 ${
              i === 0 ? "col-span-1 md:col-span-2" : ""
            }`}
          >
            <div className="flex-1 min-w-0 mr-2 relative mb-2 md:mb-0">
              <div
                ref={(el) => {
                  textRefs.current[i] = el;
                }}
                className={`overflow-hidden leading-tight pr-2 ${
                  !expandedItems[i] ? "line-clamp-3 md:line-clamp-5" : ""
                }`}
              >
                <p className="pr-2">{formatSuggestion(suggestion)}</p>
              </div>
              {needsExpansion[i] && (
                <button
                  onClick={() => toggleExpand(i)}
                  className="text-xs text-blue-500 hover:text-blue-700 mt-1"
                >
                  {expandedItems[i] ? "Show less" : "Show more"}
                </button>
              )}
            </div>
            <Button
              className={`text-white rounded-xl w-full md:w-auto ${
                i === 0
                  ? "bg-blue-300 hover:bg-blue-400"
                  : "bg-blue-500 hover:bg-blue-600"
              }`}
              onClick={() => onSuggestionClick(suggestion)}
              disabled={isLoading}
            >
              {i === 0 ? "Use original input" : "Use this"}
            </Button>
          </div>
        ))}
      </div>
    </div>
  );
};

export default SuggestedRefinedQuestions;

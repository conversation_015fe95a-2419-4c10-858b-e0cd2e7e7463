import { Textarea } from "@/components/ui/textarea";
import { minChars } from "@/app/configs";
import { useState, useEffect } from "react";

type DescriptionInputProps = {
  description: string;
  setDescription: (value: string) => void;
  isDisabled: boolean;
  setCanStartParaphrase: (value: boolean) => void;
  showExamples: boolean;
};

const DescriptionInput: React.FC<DescriptionInputProps> = ({
  description,
  setDescription,
  isDisabled,
  setCanStartParaphrase,
  showExamples,
}) => {
  const [isFocused, setIsFocused] = useState<boolean>(false);
  const [isMounted, setIsMounted] = useState<boolean>(false);
  const charCount = description.length;

  useEffect(() => {
    setIsMounted(true);
  }, []);

  const handleDescriptionChange = (
    e: React.ChangeEvent<HTMLTextAreaElement>
  ) => {
    setDescription(e.target.value);
    setCanStartParaphrase(true);
    console.log("handleDescriptionChange", e.target.value);
  };

  if (!isMounted) {
    return null; // or a loading placeholder
  }

  return (
    <div className="w-full mb-4">
      <div
        className={`relative transition-all duration-300 ease-in-out ${
          isFocused ? "transform scale-102" : ""
        }`}
      >
        <Textarea
          id="description"
          className={`w-full p-4 text-lg border-2 rounded-lg transition-all duration-300 ease-in-out
            ${isFocused ? "border-blue-400 shadow-lg" : "border-gray-300"}
            ${isDisabled ? "bg-gray-100" : "bg-white"}
            focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-transparent`}
          placeholder="Clear intention to get more focused and actionable insights. Providing more context is always welcome."
          disabled={isDisabled}
          value={description}
          onChange={handleDescriptionChange}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          maxLength={2000}
          minLength={10}
          rows={4}
        />
        <div
          className={`absolute bottom-2 right-2 text-sm transition-opacity duration-300
          ${isFocused || charCount > 0 ? "opacity-100" : "opacity-0"}`}
        >
          {charCount < minChars ? (
            <span className="text-red-500">{`${charCount} / ${minChars} min chars`}</span>
          ) : (
            <span className="text-green-500">✓ Min chars reached</span>
          )}
        </div>
      </div>
      {showExamples && (
        <Textarea
          id="example"
          className="w-full mt-2 p-2 text-sm rounded-lg bg-gray-50 border border-gray-200"
          placeholder={
            "For example:\n- As a yoga teacher in SF (who/where), I want to open a studio (what) to teach yoga classes (why).\n- Research the efficient ways to reduce game load time on low end phones\n- Design go-to market strategy for new eco-friendly apparel product in Asia"
          }
          disabled={true}
          readOnly={true}
          rows={4}
        />
      )}
    </div>
  );
};

export default DescriptionInput;

'use client'

// Polyfill browser APIs for older environments (e.g., Safari <15)
import '@/app/polyfills'

import { Dispatch, ReactNode, SetStateAction, createContext } from 'react'
import { Analytics } from '@vercel/analytics/react'
import { displayFontMapper, defaultFontMapper } from '@/styles/fonts'
import useLocalStorage from '@/app/hooks/useLocalStorage'
import { cn } from '@/lib/utils'
import PostHogUserIdentifier from '@/app/components/PostHogUserIdentifier'
import '@smastrom/react-rating/style.css'

export const AppContext = createContext<{
  font: string
  setFont: Dispatch<SetStateAction<string>>
}>({
  font: 'Default',
  setFont: () => {},
})

export default function Providers({ children }: { children: ReactNode }) {
  const [font, setFont] = useLocalStorage<'Default' | 'Serif' | 'Mono'>(
    'clarify__font',
    'Default'
  )

  return (
    <AppContext.Provider
      value={{
        font,
        setFont: setFont as Dispatch<SetStateAction<string>>,
      }}
    >
      <div className={cn(displayFontMapper[font], defaultFontMapper[font])}>
        {children}
      </div>
      <Analytics />
      <PostHogUserIdentifier />
    </AppContext.Provider>
  )
}

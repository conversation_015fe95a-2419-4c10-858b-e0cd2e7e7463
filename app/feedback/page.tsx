// @ts-nocheck // Canny code doesn't have types
"use client";

import React, { useEffect, useState } from "react";
import axios from "axios";
import toast from "react-hot-toast";

const BoardToken = "ee4d4066-f0cb-5561-0eff-a67452c251f9";

const Feedback = () => {
  const [ssoToken, setSsoToken] = useState(null);
  const [showToast, setShowToast] = useState(false);

  // First useEffect to fetch ssoToken
  useEffect(() => {
    axios
      .get(`/api/canny`)
      .then((res) => {
        console.log(res, "res");
        setSsoToken(res.data.ssoToken);
      })
      .catch((err) => {
        console.error(err);
      });
  }, []); // Empty dependency array so it runs only on mount

  // Second useEffect to render Canny widget
  useEffect(() => {
    if (ssoToken) {
      (function (w, d, i, s) {
        function l() {
          if (!d.getElementById(i)) {
            var f = d.getElementsByTagName(s)[0],
              e = d.createElement(s);
            (e.type = "text/javascript"),
              (e.async = !0),
              (e.src = "https://canny.io/sdk.js"),
              f.parentNode.insertBefore(e, f);
          }
        }
        if ("function" != typeof w.Canny) {
          var c = function () {
            c.q.push(arguments);
          };
          (c.q = []),
            (w.Canny = c),
            "complete" === d.readyState
              ? l()
              : w.attachEvent
              ? w.attachEvent("onload", l)
              : w.addEventListener("load", l, !1);
        }
      })(window, document, "canny-jssdk", "script");

      Canny("render", {
        boardToken: BoardToken,
        basePath: "/feedback",
        ssoToken: ssoToken,
        theme: "light",
      });
      setShowToast(true);
    }
  }, [ssoToken]);

  // Do this to ensure toast is shown only once, otherwise those
  // useEffects may render the component multiple times, lead to
  // multiple toasts
  useEffect(() => {
    if (showToast) {
      toast.success("Feel free to provide feedback!");
      setShowToast(false); // Reset it so it doesn't trigger again
    }
  }, [showToast]);

  return <div className="px-6 py-4" data-canny />;
};

export default Feedback;

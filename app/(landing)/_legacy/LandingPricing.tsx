import { PricingCardsContainer } from "@/app/subscription/PricingCardsContainer";
import { fetchProduct, fetchPrice } from "@/app/server-actions";
import { maxActiveConversations } from "@/app/configs";

const Pricing = async () => {
  // Data for pricing cards
  const products = await fetchProduct();
  const prices = await fetchPrice();
  const showPricing = products.length > 0 && prices.length > 0;
  const cardData = [
    {
      image: "https://i.imgur.com/pJNFEHR.png",
      title: "Free User",
      price_id: "",
      price: "-",
      features: [
        `✅ Up to ${maxActiveConversations} conversations`,
        "✅ Search capability",
        "✅ Well-curated prompt starters",
      ],
      show_button: false,
    },
    ...products.map((product) => {
      const productPrice = prices.find(
        (price) => price.product_id === product.id
      );
      // Safely access and parse the metadata
      let featuresArray: string[] = [];
      if (product.metadata && typeof product.metadata === "string") {
        try {
          const metadataObj = JSON.parse(product.metadata);
          if (
            metadataObj.features &&
            typeof metadataObj.features === "string"
          ) {
            featuresArray = metadataObj.features.split("||");
          }
        } catch (error) {
          console.error("Error parsing metadata:", error);
        }
      }

      return {
        image: "https://i.imgur.com/Ql4jRdB.png",
        title: product.name,
        price_id: productPrice && productPrice.id ? productPrice.id : "",
        price:
          productPrice && productPrice.unit_amount
            ? `$${(Number(productPrice.unit_amount) / 100).toFixed(2)}`
            : "-",
        features: featuresArray,
        show_button: false,
      };
    }),
  ];

  return (
    <>
      <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold mb-10 text-white text-center">
        Pricing [Coming Soon]
      </h2>
      {showPricing && (
        <div className="py-20 bg-gray-300 px-8">
          <PricingCardsContainer
            cardData={cardData}
            isSubscribed={false}
            onSubscribe={() => {}}
          />
        </div>
      )}
    </>
  );
};

export default Pricing;

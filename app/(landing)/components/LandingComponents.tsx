import Image from 'next/image'
import Link from 'next/link'
import { <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Zap } from 'lucide-react'
import { motion } from 'framer-motion'
import AuthForm from './AuthForm'
import React from 'react'

// Define types for our data
type HowItWorksItem = {
  title: string
  description: string
  icon: React.ReactNode
  image: string
}

type BenefitItem = {
  title: string
  description: string
  icon: string
}

type FAQItem = {
  question: string
  answer: React.ReactNode
}

// Move data here
const howItWorksData: HowItWorksItem[] = [
  {
    title: 'Turn Ambiguity into Clarity',
    description:
      'Start with any vague idea or problem, and Clarify AI will refine and clarify it, ensuring we’re aligned on the challenge ahead.',
    icon: <Zap className="h-8 w-8 mb-4 mx-auto text-blue-400" />,
    image: '/images/landing_v2_rephrase_screenshot.png',
  },
  {
    title: 'Auto-Structure Your Thinking',
    description:
      'We instantly organize your problem into a structured issue tree, creating a clear path forward and allowing you to focus on solving the problem.',
    icon: <Check className="h-8 w-8 mb-4 mx-auto text-blue-400" />,
    image: '/images/landing_v3_clarify_screenshot.png',
  },
  {
    title: 'Enhance with Targeted Research',
    description:
      'Add your own insights, or let Clarify AI conduct targeted research to deliver the information you need, saving you valuable time and effort.',
    icon: <Clock className="h-8 w-8 mb-4 mx-auto text-blue-400" />,
    image: '/images/landing_v2_research_screenshot.png',
  },
  {
    title: 'Generate Actionable Notebooks',
    description:
      'Easily integrate your context with curated prompts to generate clear, actionable documents that convey your ideas and vision.',
    icon: <Sparkles className="h-8 w-8 mb-4 mx-auto text-blue-400" />,
    image: '/images/landing_v3_notebook_screenshot.png',
  },
]

const benefitsData: BenefitItem[] = [
  {
    title: 'Clarity in Minutes',
    description:
      'Turn vague ideas into clear concepts and notebooks in minutes, keeping up momentum. Save hours by avoiding unfocused research and unnecessary meetings.',
    icon: '⏱️',
  },
  {
    title: 'Effortless Structuring',
    description:
      'Our REAL user feedback: "I could do it better if I spent a few hours, but achieving this with such little effort is amazing!"',
    icon: '🪄',
  },
  {
    title: 'Solid Start',
    description:
      'Start strong with a structured foundation for your next steps—whether it’s documentation, brainstorming, collaboration, or enhancing other AI tools with rich context.',
    icon: '🏗️',
  },
]

const faqData: FAQItem[] = [
  {
    question: 'Are you just another ChatGPT wrapper?',
    answer: (
      <p>
        Yes. Many SaaS platforms can be described as wrappers around AWS. The
        more pertinent questions would be: What value does your product/tool
        bring? How long would it take to achieve the same level of solution
        using ChatGPT directly?
      </p>
    ),
  },
  {
    question: 'How will you handle my data?',
    answer: (
      <p>
        We prioritize your data privacy and have no interest in misusing it. Our
        main focus is on experimenting with efficient ways to structure
        difficult problems. We can guarantee that no external parties will have
        access to your responses without your explicit permission. Additionally,
        your input data will only be utilized for product improvement or
        debugging purposes, and nothing else. We will not use your input as
        training data.
      </p>
    ),
  },
  {
    question: 'What is the motivation behind Clarify AI again?',
    answer: (
      <p>
        Thinking clearly and comprehensively is a challenging skill to acquire,
        especially for unfamiliar topics. Even if you are capable, it requires a
        high cognitive load to do so. For the first time in human history,
        intelligence becomes commodity, we think it is worth a try to develop a
        better tool to assist thinking when abundant intelligence is available.
      </p>
    ),
  },
  {
    question: 'Why not use ChatGPT/Claude directly?',
    answer: (
      <>
        <p>
          Yes, you can. If you just want to search or ask straightforward
          questions [you know what you want], you should definitely use Google
          or ChatGPT/Claude directly.
        </p>
        <br />
        <p>
          We initiated this because we are amazed by its capabilities and
          frustrated by its limitations:
        </p>
        <br />
        <p>
          1. Providing context to the chatbot can be quite annoying and hard to
          organize.
        </p>
        <br />
        <p>
          2. Language models can be narrow-minded, often getting stuck in a
          direction, especially when dealing with complex problems.
        </p>
        <br />
        <p>
          Without addressing these issues, using ChatGPT can be an uphill
          battle, and it is more challenging to see an instant productivity jump
          because you need to learn how to write prompts and provide contexts.
        </p>
      </>
    ),
  },
]

export const Header = () => (
  <header className="fixed top-0 left-0 right-0 z-50 bg-gray-900/80 backdrop-blur-md shadow-sm">
    <div className="container mx-auto px-4 py-4 flex justify-between items-center">
      <motion.div
        style={{ display: 'flex', alignItems: 'center', gap: '0.75rem' }}
        initial={{ opacity: 0, x: -20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Image
          src="/images/thinkgraph-icon.svg"
          alt="ThinkGraph"
          width={32}
          height={32}
          className="rounded-lg"
        />
        <span className="text-xl font-bold text-white">ThinkGraph</span>
      </motion.div>
      <motion.div
        initial={{ opacity: 0, x: 20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        <AuthForm />
      </motion.div>
    </div>
  </header>
)

export const Hero = ({
  youtubeEmbedUrlId,
  scrollY,
}: {
  youtubeEmbedUrlId: string
  scrollY: number
}) => (
  <section className="container mx-auto px-4 py-8 md:py-16 text-center relative">
    <motion.h1
      className="text-4xl sm:text-5xl md:text-6xl font-bold mb-8 bg-clip-text text-transparent bg-gradient-to-r from-blue-400 to-purple-400 leading-tight"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.8 }}
    >
      Have an MBB consultant on your side
    </motion.h1>
    <motion.p
      className="text-xl mb-8 text-gray-300"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.8, delay: 0.2 }}
    >
      Ever struggled to organize your thoughts or felt stuck overanalyzing every
      detail?
    </motion.p>

    <motion.div
      className="max-w-2xl mx-auto mb-4"
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.8, delay: 0.6 }}
    >
      <div className="relative pb-[50%] h-0 overflow-hidden rounded-lg shadow-lg">
        <iframe
          src={`https://www.youtube.com/embed/${youtubeEmbedUrlId}`}
          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
          allowFullScreen
          className="absolute top-0 left-0 w-full h-full"
        ></iframe>
      </div>
    </motion.div>

    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.8, delay: 0.8 }}
    >
      <AuthForm />
      <motion.p
        className="text-xl my-4 text-gray-300"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, delay: 0.4 }}
      >
        From confusion to clarity, with AI assistance
      </motion.p>
      <p className="text-sm text-gray-400">
        Time To Value In Minutes, Not Days
      </p>
    </motion.div>

    <motion.div
      className="absolute top-0 left-0 w-full h-full pointer-events-none"
      style={{
        background: `radial-gradient(circle at 50% ${
          50 + scrollY * 0.1
        }%, rgba(59, 130, 246, 0.3) 0%, transparent 50%)`,
      }}
    />
  </section>
)

export const HowItWorks = () => (
  <section className="py-20 bg-gray-800/50 backdrop-blur-sm">
    <div className="container mx-auto px-4 text-center">
      <motion.h2
        className="text-3xl font-bold mb-12 bg-clip-text text-transparent bg-gradient-to-r from-blue-400 to-purple-400"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8 }}
      >
        How It Works 🤔
      </motion.h2>
      <div className="grid md:grid-cols-2 gap-8 max-w-5xl mx-auto">
        {howItWorksData.map((step, index) => (
          <motion.div
            key={index}
            className="bg-gray-700 p-6 rounded-lg shadow-md transition-all duration-300 hover:shadow-lg hover:scale-105 flex flex-col h-full"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: index * 0.2 }}
          >
            {step.icon}
            <h3 className="text-xl font-semibold mb-2 text-white">
              {step.title}
            </h3>
            <div className="relative w-full pb-[56.25%] mb-4 flex-grow">
              <Image
                src={step.image}
                alt={`Illustration for ${step.title}`}
                fill
                style={{ objectFit: 'cover' }}
                className="rounded-lg"
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
              />
            </div>
            <p className="text-gray-300">{step.description}</p>
          </motion.div>
        ))}
      </div>
    </div>
  </section>
)

export const Benefits = () => (
  <section className="container mx-auto px-4 py-20">
    <motion.h2
      className="text-3xl font-bold mb-12 text-center bg-clip-text text-transparent bg-gradient-to-r from-blue-400 to-purple-400"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.8 }}
    >
      Benefits
    </motion.h2>
    <div className="grid md:grid-cols-3 gap-8">
      {benefitsData.map((benefit, index) => (
        <motion.div
          key={index}
          className="bg-gray-700 p-6 rounded-lg shadow-md transition-all duration-300 hover:shadow-lg hover:scale-105"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: index * 0.2 }}
        >
          <div className="text-4xl mb-4">{benefit.icon}</div>
          <h3 className="text-xl font-semibold mb-2 text-white">
            {benefit.title}
          </h3>
          <p className="text-gray-300">{benefit.description}</p>
        </motion.div>
      ))}
    </div>
  </section>
)

export const FAQ = () => (
  <section className="py-20 bg-gray-800/50 backdrop-blur-sm">
    <div className="container mx-auto px-4">
      <motion.h2
        className="text-3xl font-bold mb-8 text-center bg-clip-text text-transparent bg-gradient-to-r from-blue-400 to-purple-400"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8 }}
      >
        FAQ
      </motion.h2>
      <div className="max-w-2xl mx-auto space-y-4">
        {faqData.map((item, index) => (
          <motion.details
            key={index}
            className="bg-gray-700 p-4 rounded-lg shadow-md group"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: index * 0.1 }}
          >
            <summary className="font-semibold cursor-pointer text-white">
              {item.question}
            </summary>
            <div className="mt-2 text-gray-300">{item.answer}</div>
          </motion.details>
        ))}
      </div>
    </div>
  </section>
)

export const Footer = () => (
  <footer className="bg-gray-900/80 backdrop-blur-sm py-8">
    <div className="container mx-auto px-4">
      <div className="flex flex-col md:flex-row justify-between items-center">
        <div className="mb-4 md:mb-0">
          <p className="text-sm text-gray-400">© 2024 Copyright: Clarify AI</p>
        </div>
        <div className="flex space-x-4">
          <Link
            href="/tos"
            className="text-sm text-gray-400 hover:text-blue-400 transition-colors duration-300"
          >
            Terms of Service
          </Link>
          <Link
            href="/privacy"
            className="text-sm text-gray-400 hover:text-blue-400 transition-colors duration-300"
          >
            Privacy Policy
          </Link>
        </div>
      </div>
      <div className="mt-4 text-center">
        <a
          href="mailto:<EMAIL>"
          className="text-sm text-gray-400 hover:text-blue-400 transition-colors duration-300"
        >
          <EMAIL>
        </a>
      </div>
    </div>
  </footer>
)

export const StaticLogoCloud = () => {
  return (
    <div className="w-full py-12">
      <div className="flex flex-row w-full items-center justify-center px-4 md:px-8">
        <div className="text-xl sm:text-xl md:text-xl font-medium text-white">
          ❤️ Supported by:
        </div>
        <Image
          src="/images/MS_Startups_FH_lockup_hrz_OnDrk_RGB.png"
          alt="Clarify AI Hero Image"
          width={384}
          height={216}
          style={{ width: 'auto', height: 'auto' }}
        />
      </div>
    </div>
  )
}

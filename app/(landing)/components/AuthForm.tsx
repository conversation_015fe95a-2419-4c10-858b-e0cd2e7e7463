"use client";

import { signIn, useSession } from "next-auth/react";
import { useEffect, useState } from "react";
import { FcGoogle } from "react-icons/fc";
import { useRouter } from "next/navigation";
import { toast } from "react-hot-toast";
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  <PERSON><PERSON>Header,
  <PERSON>alogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { isLocalOrDevEnv } from "@/lib/utils";
import LoadingPage from "@/app/components/LoadingPage";

const LOCAL_ACCESS_CODE = "+852";

const AuthForm = () => {
  const session = useSession();
  const router = useRouter();
  const [accessCode, setAccessCode] = useState<string>("");
  const [isAccessGranted, setIsAccessGranted] = useState<boolean>(
    !isLocalOrDevEnv()
  );
  const [isLoading, setIsLoading] = useState<boolean>(false);

  useEffect(() => {
    if (session?.status === "loading") {
      setIsLoading(true);
    } else if (session?.status === "authenticated") {
      router.push("/conversations");
    } else {
      setIsLoading(false);
    }
  }, [session?.status, router]);

  const socialAction = async (action: string) => {
    setIsLoading(true);
    try {
      const callback = await signIn(action, { redirect: false });

      if (callback?.error) {
        toast.error("Invalid credentials!");
      }

      if (callback?.ok) {
        // Remove the redirect here
        toast.success("Signed in successfully!");
      }
    } catch (error) {
      console.error("Error during sign-in:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleAccessCodeSubmit = () => {
    if (accessCode === LOCAL_ACCESS_CODE) {
      setIsAccessGranted(true);
      toast.success("Access granted!");
    } else {
      toast.error("Invalid access code!");
    }
  };

  if (isLoading) {
    return <LoadingPage />;
  }

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button
          variant="outline"
          className="bg-white text-gray-900 hover:bg-gray-200 hover:text-black transition-colors duration-300"
        >
          Try It Now
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px] bg-gray-800 text-white">
        <DialogHeader>
          <DialogTitle className="text-2xl font-bold text-center">
            Welcome to Clarify AI
          </DialogTitle>
        </DialogHeader>
        <div className="mt-8">
          <div className="bg-gray-700 shadow px-4 py-8 sm:rounded-lg sm:px-10">
            <div className="md:hidden mb-6">
              <h2 className="text-red-400 font-bold mb-2">IMPORTANT:</h2>
              <h2 className="text-lg mb-2">
                <strong>Laptop</strong> for the best experience!
              </h2>
              <h2 className="text-lg">Mobile is simply too small :-(</h2>
            </div>
            <div className="mt-6">
              {isLocalOrDevEnv() && !isAccessGranted ? (
                <div>
                  <Input
                    type="text"
                    placeholder="Dev env, pls enter access code to proceed..."
                    value={accessCode}
                    onChange={(e) => setAccessCode(e.target.value)}
                    className="mb-2 text-gray-900"
                  />
                  <Button onClick={handleAccessCodeSubmit} className="w-full">
                    Submit
                  </Button>
                </div>
              ) : (
                <Button
                  onClick={() => socialAction("google")}
                  className="w-full bg-white text-gray-900 hover:bg-gray-200 transition-colors duration-300 flex items-center justify-center"
                >
                  <FcGoogle className="mr-2" />
                  Continue with Google
                </Button>
              )}
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default AuthForm;

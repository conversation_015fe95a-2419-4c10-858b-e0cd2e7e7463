import { useState, useEffect } from 'react'
import { isPostHogReady } from '@/app/libs/posthog'

type PostHogStatus = 'loading' | 'ready' | 'failed' | 'unavailable'

/**
 * React hook for PostHog status and initialization
 * Handles dynamic initialization scenarios
 */
export const usePostHog = () => {
  const [status, setStatus] = useState<PostHogStatus>('loading')

  useEffect(() => {
    const checkPostHogStatus = () => {
      if (typeof window === 'undefined') {
        setStatus('unavailable')
        return
      }

      if (!process.env.NEXT_PUBLIC_POSTHOG_KEY) {
        setStatus('unavailable')
        return
      }

      const initialized = isPostHogReady()
      if (initialized) {
        setStatus('ready')
      } else {
        setStatus('failed')

        // Retry after a delay
        const retryTimeout = setTimeout(() => {
          const retryResult = isPostHogReady()
          setStatus(retryResult ? 'ready' : 'failed')
        }, 2000)

        return () => clearTimeout(retryTimeout)
      }
    }

    checkPostHogStatus()

    // Listen for PostHog events if available
    if (typeof window !== 'undefined') {
      const handlePostHogReady = () => setStatus('ready')
      window.addEventListener('posthog-ready', handlePostHogReady)

      return () => {
        window.removeEventListener('posthog-ready', handlePostHogReady)
      }
    }
  }, [])

  return {
    status,
    isReady: status === 'ready',
    isLoading: status === 'loading',
    hasFailed: status === 'failed',
    isUnavailable: status === 'unavailable',
  }
}

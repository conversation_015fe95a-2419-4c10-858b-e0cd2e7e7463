import { useParams } from "next/navigation";
import { useMemo } from "react";

// This hook returns the conversationId and isOpen state based on the current URL params.
const useConversation = () => {
  const params = useParams();

  // Get the conversationId from the URL params and set it to an empty string if it doesn't exist.
  const conversationId = useMemo(() => {
    if (!params?.conversationId) {
      return "";
    }

    return params.conversationId as string;
  }, [params?.conversationId]);

  // Determine if the conversation is open based on whether or not a conversationId exists.
  const isOpen = useMemo(() => !!conversationId, [conversationId]);

  // Return the isOpen and conversationId values as an object.
  return useMemo(
    () => ({
      isOpen,
      conversationId,
    }),
    [isOpen, conversationId]
  );
};

export default useConversation;

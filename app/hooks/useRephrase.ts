import { useEffect } from "react";
import { useChat } from "ai/react";
import mixpanel from "@/app/libs/mixpanel";
import { RephraseRequestType } from "@/app/types/api";

type UseRephraseProps = {
  description: string;
  userId: string;
  onRephraseSuccess: (rephrased: string[]) => void;
  lastDescription: string;
};

const extractNumberedSuggestions = (text: string): string[] => {
  const lines = text.split("\n");
  const numberedLines = lines.filter((line) => /^\d+\./.test(line.trim()));
  return numberedLines.map((line) => line.replace(/^\d+\.\s*/, "").trim());
};

const useRephrase = ({
  description,
  userId,
  onRephraseSuccess,
  lastDescription,
}: UseRephraseProps) => {
  const requestBody: RephraseRequestType = {
    userId,
    description,
  };

  const { messages, append, isLoading } = useChat({
    api: "/api/rephrase",
    body: requestBody,
  });

  useEffect(() => {
    if (messages.length > 0) {
      const lastMessage = messages[messages.length - 1]?.content;
      if (lastMessage) {
        const parsed = [
          lastDescription,
          ...extractNumberedSuggestions(lastMessage),
        ];
        onRephraseSuccess(parsed);
      }
    }
  }, [messages, onRephraseSuccess, description, lastDescription]);

  const handleRephrase = () => {
    mixpanel.track("start_rephrase_click");
    append({ role: "system", content: "start rephrasing questions" });
  };

  return {
    handleRephrase,
    isLoading,
  };
};

export default useRephrase;

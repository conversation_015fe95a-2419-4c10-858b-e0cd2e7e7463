// Legacy hook: We did a big refactor to handle the tree parsing. Originally we want to separate the functions into different hooks
// One for fetch, one for manage. So the main component can just use it without worrying about the logic
// But this separation seems not a good choices because we have many useEffect and nested useEffect
// It becomes super difficult to manage the state than simply one long file.
// Most logics are not needed anymore, but we keep it here for reference

import useIssueTreeStore, {
  issueTreeStoreStatus,
} from "@/app/stores/issuetree_store";
import { useCallback } from "react";
import { Node } from "@/app/types";
var isEqual = require("lodash.isequal");
// // // // // // // // // // //
// useManageIssueTree hook
// // // // // // // // // // //
// separate custom hook to manage the tedious state update logic
export const useOnNodeChange = () => {
  const issueTreeStore = useIssueTreeStore();

  // onNodesChange mainly only monitor a specific changes event ["position"] related to dragging
  // only update state store if position changed [ie: drag]
  // If not specified specific event, our nodes object and changes object are alway different
  // that leads to inf loop
  const onNodesChange = useCallback(
    (changes: any) => {
      if (issueTreeStore.nodes.length === 0) return;
      // After the issue tree is completed, we don't want to update the state
      // previous bug: drag the node update the state [leafNode becomes unlocked]
      if (issueTreeStore.status === issueTreeStoreStatus.Completed) return;
      if (changes[0].dragging && changes[0].type === "position") {
        const nodeIdToUpdate = changes[0].id;
        const newPosition = changes[0].position;

        // Find the node that needs to be updated
        const nodeToUpdate = issueTreeStore.nodes.find(
          (node: Node) => node.id === nodeIdToUpdate
        );

        // If the node is found and its position is different, then update it
        if (nodeToUpdate && !isEqual(newPosition, nodeToUpdate.position)) {
          // Clone the node and update its position
          const updatedNode = { ...nodeToUpdate, position: newPosition };

          // Create a new array of nodes with the updated node
          const updatedNodes = issueTreeStore.nodes.map((node: Node) =>
            node.id === nodeIdToUpdate ? updatedNode : node
          );

          // Update the state
          issueTreeStore.setNodes(updatedNodes, issueTreeStoreStatus.Ready);
        }
      }
    },
    // depends on the store to refresh useCallback state
    // avoid it uses the old state and fail the draging feature
    [issueTreeStore]
  );

  // return { markdownText, setMarkdownText, onNodesChange, ...issueTreeStore };
  return { onNodesChange };
};

// In case API is complete [ACTIVE], but initial parse
// is not saved st nodes is empty, initialize it with raw_markdown
// to parse again if nodes is empty
// const [markdownText, setMarkdownText] = useState<string>(
//   !fetchedIssueTree?.nodes ? fetchedIssueTree?.raw_markdown || "" : ""
// );

// based on fetchedIssueTree, populate the state
// ACTIVE status mean nodes are ready to use
// useEffect(() => {
//   console.log("useEffect: populate the state", fetchedIssueTree);
//   if (fetchedIssueTree) {
//     // issueTreeStore.reset();
//     issueTreeStore.setId(fetchedIssueTree.id);

//     if (
//       fetchedIssueTree.status === IssueTreeStatus.ACTIVE ||
//       fetchedIssueTree.status === IssueTreeStatus.COMPLETED
//     ) {
//       // Nodes
//       if (typeof fetchedIssueTree?.nodes === "string") {
//         // Normal flow, ready to read, populate and set ready
//         if (fetchedIssueTree.nodes.length > 0) {
//           if (fetchedIssueTree.status === IssueTreeStatus.ACTIVE) {
//             issueTreeStore.setNodes(
//               JSON.parse(fetchedIssueTree.nodes) as MyNode[],
//               issueTreeStoreStatus.Ready
//             );
//           } else {
//             // If it is completed, set status to completed
//             issueTreeStore.setNodes(
//               JSON.parse(fetchedIssueTree.nodes) as MyNode[],
//               issueTreeStoreStatus.Completed
//             );
//           }
// In case API is complete [ACTIVE], but initial parse
// is not saved st nodes is empty, setMarkdownText and
// parse again
//         } else if (
//           fetchedIssueTree.raw_markdown &&
//           fetchedIssueTree.raw_markdown.length > 0
//         ) {
//           // Set markdownText as raw_markdown here
//           setMarkdownText(fetchedIssueTree.raw_markdown);
//         }
//       }
//       // Edges
//       if (typeof fetchedIssueTree?.edges === "string") {
//         if (fetchedIssueTree.edges.length > 0) {
//           issueTreeStore.setEdges(
//             JSON.parse(fetchedIssueTree.edges) as MyEdge[]
//           );
//         }
//       }
//       // summaryText
//       if (typeof fetchedIssueTree?.summary_context === "string") {
//         if (fetchedIssueTree.summary_context.length > 0) {
//           issueTreeStore.setSummaryText(
//             fetchedIssueTree.summary_context as string
//           );
//         }
//       }

//       // originalAskText
//       if (
//         typeof fetchedIssueTree?.config === "object" &&
//         fetchedIssueTree?.config !== null
//       ) {
//         const config = fetchedIssueTree.config as Record<string, any>; // Use a broad type assertion
//         const originalAsk = config["original_ask"]; // Access using bracket notation

//         if (typeof originalAsk === "string" && originalAsk.length > 0) {
//           issueTreeStore.setOriginalAskText(originalAsk);
//         }
//       }
//     }
//   }

//   issueTreeStore.setSearchesAndRags(fetchedSearches, fetchedRags);
//   console.log("useEffect: end populate the state", issueTreeStore);
// }, [fetchedIssueTree, fetchedSearches, fetchedRags]);

// // When it is streaming, update the markdown text and parse
// useEffect(() => {
//   if (
//     fetchedIssueTree &&
//     fetchedIssueTree.status === IssueTreeStatus.INITIALIZED
//   ) {
//     const lastMessage = messages[messages.length - 1];
//     if (lastMessage && lastMessage.role === "assistant") {
//       setMarkdownText(lastMessage.content);
//     }
//   }
// }, [messages]);

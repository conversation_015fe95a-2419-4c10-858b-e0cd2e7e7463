"use client";

import { useMemo } from "react";
import { usePathname, useRouter } from "next/navigation";
import { HiChat } from "react-icons/hi";
import { HiArrowLeftOnRectangle } from "react-icons/hi2";
import { MdFeedback } from "react-icons/md";
import { signOut } from "next-auth/react";
import useConversation from "./useConversation";
import mixpanel from "@/app/libs/mixpanel";

const useRoutes = () => {
  const pathname = usePathname();
  const { conversationId } = useConversation();
  const router = useRouter();

  const handleSignOut = () => {
    signOut({ redirect: false }).then(() => {
      router.push("/");
      router.refresh();
    });
  };

  const routes = useMemo(
    () => [
      {
        label: "Chat",
        href: "/conversations",
        icon: HiChat,
        active: pathname === "/conversations" || !!conversationId,
      },
      {
        label: "Feedback",
        href: "/feedback",
        icon: MdFeedback,
        onClick: () => mixpanel.track("click_sidebar_feedback"),
        active: pathname === "/feedback",
      },
      {
        label: "Logout",
        onClick: handleSignOut,
        href: "#",
        icon: HiArrowLeftOnRectangle,
      },
    ],
    [pathname, conversationId, router]
  );

  return routes;
};

export default useRoutes;

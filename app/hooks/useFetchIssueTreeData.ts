import { useState, useCallback, useEffect } from "react";
import { IssueTreeStatus } from "@prisma/client";
import {
  fetchedIssueTreeType,
  fetchedFeedbacksType,
  fetchedRagsType,
  fetchedSearchesType,
  fetchedSubtreesType,
} from "@/app/server-actions";
import useIssueTreeStore, {
  issueTreeStoreStatus,
} from "@/app/stores/issuetree_store";
import { Node, Edge } from "@/app/types";
import markdownToReactFlow from "@/app/(conv)/conversations/[conversationId]/issue_tree/markdownToReactFlow";
import { addSuffixAfterNumber } from "@/lib/utils";
import { getDagreLayoutedElements } from "@/app/(conv)/conversations/[conversationId]/issue_tree/dagreAutoLayout";
import useNotebookStore from "@/app/stores/notebook_store";
import { fetchAllIssueTreeData } from "@/app/server-actions/agg_fetch";

type IssueTreeData = {
  issueTree: fetchedIssueTreeType | null;
  feedbacks: fetchedFeedbacksType[];
  subtrees: fetchedSubtreesType[];
  searches: fetchedSearchesType[];
  rags: fetchedRagsType[];
};

export const useFetchIssueTreeData = (
  conversationId: string,
  userId: string
) => {
  const [isLoading, setLoading] = useState<boolean>(true);
  const [data, setData] = useState<IssueTreeData>({
    issueTree: null,
    feedbacks: [],
    subtrees: [],
    searches: [],
    rags: [],
  });

  const issueTreeStore = useIssueTreeStore();
  const notebookStore = useNotebookStore();
  const reload = useCallback(async () => {
    try {
      // Set state to show Skeleton in IssueTree component
      setLoading(true);

      // Fetch all related data
      const fetchedIssueTreeData = await fetchAllIssueTreeData(conversationId);
      const {
        issueTree: newIssueTree,
        subtrees: newSubtrees,
        feedbacks: newFeedbacks,
        searches: newSearches,
        rags: newRags,
        notebooks: newNotebooks,
      } = fetchedIssueTreeData;

      // Reset the store to avoid switching between different issue trees
      issueTreeStore.reset();

      // Set id
      issueTreeStore.setId(newIssueTree?.id || "");

      // Set nodes, edges and status
      // Normal flow, nodes and edges are not empty
      if (
        newIssueTree?.nodes !== null &&
        newIssueTree?.nodes.length !== 0 &&
        typeof newIssueTree?.nodes === "string" &&
        typeof newIssueTree?.edges === "string"
      ) {
        // For active issue tree, set status to Ready
        // It should be basically all the cases as of now
        if (newIssueTree?.status === IssueTreeStatus.ACTIVE) {
          issueTreeStore.setNodes(
            JSON.parse(newIssueTree.nodes) as Node[],
            issueTreeStoreStatus.Ready
          );
        } else {
          // Mark all other cases as Completed, which are some previous conversations
          issueTreeStore.setNodes(
            JSON.parse(newIssueTree.nodes) as Node[],
            issueTreeStoreStatus.Completed
          );
        }
        // Set edges for both cases
        issueTreeStore.setEdges(JSON.parse(newIssueTree.edges) as Edge[]);
      } else if ((newIssueTree?.raw_markdown || "").length > 0) {
        // If nodes and edges are empty, but raw_markdown is not empty
        // ie: users generate the issue tree, didn't change anything, and then reload the page
        // When we generate the issue tree, we don't save nodes and edges, we save when user first edit
        // So, nodes and edges are empty, and we need to parse raw_markdown again

        // parse raw_markdown [generated markdown]
        const { nodes: initialNodes, edges: initialEdges } =
          markdownToReactFlow(
            // Add suffix after number to remind user those are AI generated number
            addSuffixAfterNumber(
              newIssueTree?.raw_markdown || "",
              "[EXAMPLE NUMBER]"
            )
          );

        // Adjust layout
        const newLayoutedElements = getDagreLayoutedElements(
          initialNodes,
          initialEdges
        );

        // Set nodes and edges
        issueTreeStore.setNodes(
          newLayoutedElements.nodes,
          issueTreeStoreStatus.Ready
        );
        issueTreeStore.setEdges(newLayoutedElements.edges);
      }

      // Set originalAskText
      if (
        typeof newIssueTree?.config === "object" &&
        newIssueTree?.config !== null
      ) {
        const config = newIssueTree.config as Record<string, any>; // Use a broad type assertion
        const originalAsk = config["original_ask"]; // Access using bracket notation

        if (typeof originalAsk === "string" && originalAsk.length > 0) {
          issueTreeStore.setOriginalAskText(originalAsk);
        }
      }

      // Set Searches and Rags
      issueTreeStore.setSearchesAndRags(newSearches, newRags);

      // Set Notebooks
      notebookStore.setNotebooks(newNotebooks);
      notebookStore.setShowFeedbackCollection(newFeedbacks.length === 0);

      // Set data
      setData({
        issueTree: newIssueTree,
        feedbacks: newFeedbacks,
        subtrees: newSubtrees,
        searches: newSearches,
        rags: newRags,
      });
    } catch (error) {
      console.error("Failed to reload issue tree data:", error);
    } finally {
      // Set loading to false, so that Skeleton will not be shown
      setLoading(false);
    }
  }, [conversationId, userId]);

  useEffect(() => {
    reload();
  }, [reload]);

  return {
    issueTree: data.issueTree,
    isLoading,
    feedbacks: data.feedbacks,
    subtrees: data.subtrees,
    searches: data.searches,
    rags: data.rags,
    reload,
  };
};

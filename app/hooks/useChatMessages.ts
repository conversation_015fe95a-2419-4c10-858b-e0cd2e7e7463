import { useMemo } from "react";
import { useChat } from "ai/react";

type UseChatMessagesProps = {
  api: string;
  body: any;
};

type UseChatMessagesReturn = {
  lastMessage: string | undefined;
  append: (message: { content: string; role: string }) => void;
  isLoading: boolean;
};

export const useChatMessages = ({
  api,
  body,
}: UseChatMessagesProps): UseChatMessagesReturn => {
  const {
    messages,
    append: appendRaw,
    isLoading,
  } = useChat({
    api,
    body,
  });
  const append = (message: { content: string; role: string }) => {
    appendRaw(message as any);
  };

  const lastMessage = useMemo(
    () => messages[messages.length - 1]?.content,
    [messages]
  );

  return { lastMessage, append, isLoading };
};

"use client";

import Button from "@/app/components/Button";
import { Textarea } from "@/components/ui/textarea";
import { useState, useEffect } from "react";
import toast from "react-hot-toast";
import { useChat } from "ai/react";
import { numberOfLinksToUse } from "@/app/configs";
import { linksFromSearchResults } from "@/app/users/components/utils";
import { SearchResult } from "@/app/api/rag/utils";

export default function RagPage() {
  const currentUser = {
    id: "clk69i72m00000kgdkuwwdp22",
    email: "<EMAIL>",
    status: "ACTIVE",
  };

  const generateRequestData = {
    currentUser: currentUser,
    conversationId: "clvbutcum00130k82594p37du",
    issueTreeId: "clvbutdnu00150k82icym725y",
    selectedNodeId: "L4:6",
    // originalAsk:
    //   "[User Needs] As a software engineer, I want to become proficient at LeetCode, so that I can improve my coding skills and increase my chances of landing my desired job in the tech industry.",
    // nodeQuestion:
    //   "What specific coding skills and techniques are targeted for improvement through LeetCode practice?",
    originalAsk:
      "[Risk Assessment] As a product manager, I want to assess potential challenges such as market competition, integration complexities, and regulatory requirements in the airline industry when developing and selling the revenue management system to mitigate risks and ensure successful adoption by clients.",
    nodeQuestion:
      "What technologies or innovations have the main competitors in the airline industry integrated into their revenue management systems to stay competitive, and how has this impacted their market positioning?",
  };

  const [searchResults, setSearchResults] = useState<string>("");

  const { messages, append, input, handleInputChange, isLoading, data } =
    useChat({
      api: "/api/rag/assistant",
      onError(error: any) {
        console.error("Error:", error);
        toast.error("An error occurred. Please try again.");
      },
      body: generateRequestData,
    });

  const handleClick = () => {
    append({ role: "user", content: "Start!" });
  };

  useEffect(() => {
    if (
      data &&
      Array.isArray(data) &&
      data.length > 0 &&
      typeof data[0] === "object" &&
      data[0] !== null &&
      "searchResults" in data[0]
    ) {
      const llmContext = linksFromSearchResults(
        data[0]["searchResults"] as SearchResult[],
        numberOfLinksToUse
      );
      setSearchResults(llmContext);
    }
  }, [data]);

  const latestMessage = searchResults
    ? messages[messages.length - 1]?.content + "\n\nContext:\n" + searchResults
    : messages[messages.length - 1]?.content || "";

  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-4 space-y-4">
      <Textarea
        value={input}
        placeholder={"Enter your prompt here..."}
        className="text-base rounded-md mb-4"
        onChange={handleInputChange}
        rows={5}
      />
      <Button onClick={handleClick} disabled={isLoading}>
        {isLoading ? "Generating..." : "Submit"}
      </Button>
      <div className="w-full max-w-2xl">
        <h3 className="text-lg font-semibold mb-2">Search Results:</h3>
        <Textarea
          value={searchResults}
          placeholder={"Search Results"}
          className="rounded-md mb-4"
          readOnly
          rows={5}
        />
        <h3 className="text-lg font-semibold mb-2">Assistant Response:</h3>
        <Textarea
          value={latestMessage}
          placeholder={"Assistant Response"}
          className="rounded-md"
          readOnly
          rows={10}
        />
      </div>
    </div>
  );
}

import { SearchResult } from "@/app/api/rag/utils";
// Function to create a formatted string from the resultMap
export const llmContextFromSearchResults = (
  searchResults: SearchResult[],
  k: number
): string => {
  let resultString = "";
  let count = 0; // Counter to ensure we do not exceed k citations

  for (const res of searchResults) {
    if (count >= k) break;
    if (!res.scripted_number || !res.extra_snippets) continue;
    resultString += `${res.scripted_number}:\n${res.extra_snippets.join(
      "\n"
    )}\n\n`;
    count++;
  }

  return resultString.trim(); // Remove any trailing new lines
};

export const linksFromSearchResults = (
  searchResults: SearchResult[],
  k: number
): string => {
  let resultString = "";
  let count = 0; // Counter to ensure we do not exceed k citations

  for (const result of searchResults) {
    if (count >= k) break; // Stop processing if k results have been added
    if (!result.scripted_number) continue; // Skip results without a scripted number

    // Append the scripted number, title, and URL to the result string
    resultString += `${result.scripted_number} ${result.title}\n${result.url}\n\n`;
    count++;
  }

  return resultString;
};

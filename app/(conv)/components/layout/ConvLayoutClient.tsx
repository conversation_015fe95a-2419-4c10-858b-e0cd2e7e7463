'use client'

import React, { useState } from 'react'
import { useSession } from 'next-auth/react'
import Sidebar from '@/app/(conv)/dragTree/[dragTreeId]/components/layout/Sidebar'
import LoadingPage from '@/app/components/LoadingPage'
import type { DragTreeData } from '@/app/libs/sidebar-events'
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip'

type ConvLayoutClientProps = {
  children: React.ReactNode
  /** Prefetched drag trees for sidebar (optional) */
  initialDragTrees?: DragTreeData[]
}

const ConvLayoutClient: React.FC<ConvLayoutClientProps> = ({
  children,
  initialDragTrees = [],
}) => {
  const { data: session, status } = useSession()
  const [isSidebarOpen, setIsSidebarOpen] = useState<boolean>(false)

  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen)
  }

  const closeSidebar = () => {
    setIsSidebarOpen(false)
  }

  // Show loading while session is being fetched
  if (status === 'loading') {
    return <LoadingPage />
  }

  // Only logged-in users can access this page
  if (status === 'unauthenticated' || !session?.user) {
    return (
      <div className="h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-800 mb-4">
            Access Denied
          </h1>
          <p className="text-gray-600">Please log in to access this page.</p>
        </div>
      </div>
    )
  }

  return (
    <TooltipProvider>
      <div className="h-screen flex bg-gray-50">
        {/* Collapsible Sidebar - shared for all conv pages */}
        <Sidebar
          isOpen={isSidebarOpen}
          onClose={closeSidebar}
          session={session}
          initialDragTrees={initialDragTrees}
        />

        {/* Subtle 1px gray bar with expand slider when sidebar is closed */}
        {!isSidebarOpen && (
          <Tooltip>
            <TooltipTrigger asChild>
              <div className="fixed inset-y-0 left-0 z-[55]">
                <div
                  onClick={toggleSidebar}
                  className="w-2 h-full bg-gray-600 hover:bg-gray-700 cursor-pointer group transition-colors"
                >
                  <div className="absolute left-2 top-1/2 transform -translate-y-1/2 opacity-70 group-hover:opacity-100 transition-opacity">
                    <div className="w-6 h-12 bg-gray-600 rounded-r-xl flex items-center justify-center shadow-lg">
                      <div className="w-1 h-6 bg-white rounded"></div>
                    </div>
                  </div>
                </div>
              </div>
            </TooltipTrigger>
            <TooltipContent side="right">
              <p>Click to open sidebar</p>
            </TooltipContent>
          </Tooltip>
        )}

        {/* Main Content */}
        <main
          className={`flex-1 transition-all duration-300 ease-in-out max-w-full overflow-hidden ${
            isSidebarOpen ? 'ml-80' : 'ml-2'
          }`}
        >
          {children}
        </main>

        {/* Overlay for mobile */}
        {isSidebarOpen && (
          <div
            className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
            onClick={closeSidebar}
          />
        )}
      </div>
    </TooltipProvider>
  )
}

export default ConvLayoutClient

import { useEffect, useState, useRef } from 'react'
import type { TreeNode } from '@/app/types'

export type WorkerDiagramResult = {
  nodes: any[]
  edges: any[]
}

/**
 * Converts a `TreeNode` into React Flow nodes/edges in a Web Worker so that
 * expensive traversal is off-main-thread. The hook returns an always defined
 * object (empty arrays while computing) so callers can use it without null
 * checks.
 */
export const useTreeToReactFlowWorker = (
  tree: TreeNode | null | undefined
): WorkerDiagramResult => {
  const [result, setResult] = useState<WorkerDiagramResult>({
    nodes: [],
    edges: [],
  })
  const workerRef = useRef<Worker | null>(null)

  useEffect(() => {
    if (!tree) {
      setResult({ nodes: [], edges: [] })
      return
    }

    // Ensure we only run in the browser
    if (typeof window === 'undefined') return

    // Dynamically import / spin up a new worker each time the tree changes.
    // Using a fresh worker avoids having to implement tree diffing logic and
    // guarantees that heavy work is cleaned up when the component unmounts.
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore – TS cannot infer the type for the dynamic URL import.
    const worker: Worker = new Worker(
      new URL('../workers/treeNodeToReactFlow.worker.ts', import.meta.url),
      { type: 'module' }
    )
    workerRef.current = worker

    worker.postMessage(tree)

    worker.onmessage = e => {
      setResult(e.data as WorkerDiagramResult)
      // Finished – terminate to free up resources.
      worker.terminate()
      workerRef.current = null
    }

    worker.onerror = err => {
      console.error('[useTreeToReactFlowWorker] Worker error', err)
      worker.terminate()
      workerRef.current = null
      setResult({ nodes: [], edges: [] })
    }

    return () => {
      // Clean up if the component unmounts before the worker finishes.
      if (workerRef.current) {
        workerRef.current.terminate()
        workerRef.current = null
      }
    }
  }, [tree?.id])

  return result
}

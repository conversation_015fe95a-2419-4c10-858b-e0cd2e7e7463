import ELK from 'elkjs/lib/elk.bundled.js'
import { Node, Edge, MarkerType } from 'reactflow'
// import type { XYPosition } from 'reactflow' // Currently unused

type NodeDimensions = {
  width: number
  height: number
}

const elk = new ELK()

// Layout constants available for future use
// const _RADIAL_LAYOUT_CONSTANTS = {
//   baseWidth: 200,
//   baseHeight: 60,
//   lineHeight: 20,
//   widthMultiplierForExample: 2.5,
//   minLeafNodeHeight: 80,
//   paddingWidth: 40,
//   paddingHeight: 20,
// }

/**
 * Calculates dimensions for a node based on its content and type
 * Reuses the same logic as dagre layout for consistency
 */
const calculateNodeDimensions = (node: Node): NodeDimensions => {
  const label = node.data.label || ''
  const isQuestionNode = node.type === 'customQuestionNode'

  // Make question nodes more compact in radial layout
  if (isQuestionNode) {
    const width = Math.min(220 + label.length * 1.8, 300) // Question nodes sizing
    const height = Math.min(70 + label.split('\\n').length * 15, 90) // Question node height
    return { width, height }
  }

  // Category nodes should be appropriately sized for the increased spacing
  const width = Math.min(240 + label.length * 2, 320) // Slightly larger category nodes
  const height = Math.min(80 + label.split('\\n').length * 18, 100) // Increased height for better proportion
  return { width, height }
}

// Helper function available for future use
// const _getPosition = (x: number, y: number): XYPosition => ({ x, y })

/**
 * Applies ELK.js radial layout to nodes and edges
 * Returns a promise that resolves to layouted elements
 */
export const getRadialLayoutedElements = async (
  nodes: Node[],
  edges: Edge[]
): Promise<{ nodes: Node[]; edges: Edge[] }> => {
  const elkOptions = {
    'elk.algorithm': 'org.eclipse.elk.radial',
    'elk.spacing.nodeNode': '60', // Increased spacing between nodes for better separation
    'elk.radial.radius': '120', // Increased base radius for better category node spacing
    'elk.radial.compaction': 'NONE', // Disable compaction to allow custom positioning
    'elk.layered.spacing.nodeNodeBetweenLayers': '80', // More spacing between node levels
    'elk.spacing.componentComponent': '50', // Increased spacing between components
    'elk.radial.centerOnRoot': 'true', // Center the layout on the root node
    'elk.spacing.edgeNode': '10', // Spacing between edges and nodes
  }

  const elkNodesInput = nodes.map(node => {
    const { width, height } = calculateNodeDimensions(node)
    return {
      id: node.id,
      width,
      height,
    }
  })

  const graph = {
    id: 'root',
    layoutOptions: elkOptions,
    children: elkNodesInput,
    edges: edges.map(e => ({
      id: e.id,
      sources: [e.source],
      targets: [e.target],
    })),
  } as any

  try {
    const result = await elk.layout(graph)

    const posMap: Record<string, { x: number; y: number }> = {}
    result.children?.forEach(c => {
      const x = c.x ?? 0
      const y = c.y ?? 0
      posMap[c.id] = {
        x: isNaN(x) || !isFinite(x) ? 0 : x,
        y: isNaN(y) || !isFinite(y) ? 0 : y,
      }
    })

    const layoutedNodes: Node[] = nodes.map(n => ({
      ...n,
      position: posMap[n.id] ?? n.position,
    }))

    // --- NEW: Re-center the radial layout ---
    // Identify the root node (level 1 or without a parentId) to centre the layout
    const rootNode = layoutedNodes.find(
      n => n.data?.level === 1 || !n.data?.parentId
    )

    const rootX = (rootNode?.position?.x ?? 0) as number
    const rootY = (rootNode?.position?.y ?? 0) as number

    // Shift every node so that the root node ends up at (0,0)
    const centredNodes: Node[] = layoutedNodes.map(node => ({
      ...node,
      position: {
        x: (node.position as { x: number; y: number }).x - rootX,
        y: (node.position as { x: number; y: number }).y - rootY,
      },
    }))

    // Create a map of nodes by their IDs for quick lookup of positions (after centering)
    const nodePositionMap = new Map(
      centredNodes.map(node => [node.id, node.position])
    )

    // Calculate dynamic handles for edges based on relative node positions
    const layoutedEdges: Edge[] = edges.map(edge => {
      const sourceNodePos = nodePositionMap.get(edge.source)
      const targetNodePos = nodePositionMap.get(edge.target)

      if (!sourceNodePos || !targetNodePos) return edge

      const dx = targetNodePos.x - sourceNodePos.x
      const dy = targetNodePos.y - sourceNodePos.y

      // Determine the primary axis of connection (horizontal or vertical)
      const isHorizontal = Math.abs(dx) > Math.abs(dy)

      // Assign handles based on relative positions
      const sourceHandle = isHorizontal
        ? dx > 0
          ? 'right'
          : 'left'
        : dy > 0
          ? 'bottom'
          : 'top'
      const targetHandle = isHorizontal
        ? dx > 0
          ? 'left'
          : 'right'
        : dy > 0
          ? 'top'
          : 'bottom'

      return {
        ...edge,
        sourceHandle,
        targetHandle,
        // Preserve edge styling
        type: edge.type || 'smoothstep',
        style: edge.style || {
          stroke: '#64748b',
          strokeWidth: 2,
        },
        markerEnd: edge.markerEnd || {
          type: MarkerType.ArrowClosed,
          color: '#64748b',
        },
      }
    })

    return { nodes: centredNodes, edges: layoutedEdges }
  } catch (error) {
    console.error('ELK layout failed:', error)
    return { nodes, edges }
  }
}

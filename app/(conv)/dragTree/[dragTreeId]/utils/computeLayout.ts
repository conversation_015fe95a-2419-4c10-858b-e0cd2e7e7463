import { getDagreLayoutedElements } from './dagreAutoLayout'
import { getRadialLayoutedElements } from './radialAutoLayout'
import type { Node, Edge } from 'reactflow'

export type LayoutMode = 'linear' | 'radial'

let _layoutWorker: Worker | undefined

// Use dynamic import with "?worker" query so Next.js / Webpack treats the module as a Web Worker bundle.
// This prevents the problematic "_Worker is not a constructor" rewrite that can happen when the same module
// is compiled for both SSR & browser environments.
const getOrCreateWorker = async (): Promise<Worker | undefined> => {
  if (typeof window === 'undefined' || typeof Worker === 'undefined') {
    return undefined // SSR or very old browsers – fall back to main thread
  }

  if (_layoutWorker) return _layoutWorker

  try {
    // @ts-ignore - '?worker' import is resolved by Next.js/webpack at build time
    const WorkerConstructor: any = (
      await import('../workers/layoutWorker.ts?worker')
    ).default

    _layoutWorker = new WorkerConstructor()
  } catch (err) {
    console.warn(
      '[computeLayout] Failed to instantiate worker, falling back to main thread',
      err
    )
    _layoutWorker = undefined
  }

  return _layoutWorker
}

export const computeLayout = (
  layoutMode: LayoutMode,
  nodes: Node[],
  edges: Edge[]
): Promise<{ nodes: Node[]; edges: Edge[] }> => {
  return new Promise(async (resolve, reject) => {
    const worker = await getOrCreateWorker()

    // If worker is not available, compute on the main thread as before
    if (!worker) {
      try {
        let result
        if (layoutMode === 'radial') {
          result = await getRadialLayoutedElements(nodes, edges)
        } else {
          result = getDagreLayoutedElements(nodes, edges)
        }
        resolve(result)
      } catch (error) {
        reject(error)
      }
      return
    }

    const messageId = `${Date.now()}-${Math.random()}`

    const handleMessage = async (e: MessageEvent) => {
      if (e.data?.id !== messageId) return
      worker.removeEventListener('message', handleMessage)
      if (e.data?.error) {
        // Worker failed – fallback to main-thread computation
        worker.removeEventListener('message', handleMessage)
        try {
          let fallbackResult
          if (layoutMode === 'radial') {
            fallbackResult = await getRadialLayoutedElements(nodes, edges)
          } else {
            fallbackResult = getDagreLayoutedElements(nodes, edges)
          }
          resolve(fallbackResult)
        } catch (fallbackError) {
          reject(fallbackError)
        }
      } else {
        resolve({ nodes: e.data.nodes, edges: e.data.edges })
      }
    }

    worker.addEventListener('message', handleMessage)

    worker.postMessage({ id: messageId, nodes, edges, layoutMode })
  })
}

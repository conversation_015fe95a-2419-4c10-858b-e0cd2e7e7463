import React, { useState, useCallback } from 'react'
import { NodeContentProps } from '@/app/(conv)/dragTree/[dragTreeId]/types'
import { DEFAULT_NODE_LABELS } from '@/app/(conv)/dragTree/[dragTreeId]/constants/node-types'
import { cn } from '@/lib/utils'

/**
 * Node Content Component
 *
 * Handles the display and editing of node labels.
 * Memoized to prevent unnecessary re-renders.
 */
const NodeContent: React.FC<NodeContentProps> = ({
  node,
  editingNode,
  onEdit,
  setEditingNode,
  isCurrentNodeLoading = false,
  targetNodeId,
}) => {
  const [editText, setEditText] = useState<string>('')
  const isEditing = editingNode === node.id

  // Initialize edit text when editing starts
  React.useEffect(() => {
    if (isEditing && !editText) {
      setEditText(node.label)
    } else if (!isEditing) {
      setEditText('')
    }
  }, [isEditing, node.label, editText])

  const handleSave = useCallback(() => {
    if (editText.trim()) {
      onEdit(node.id, editText.trim())
    }
    setEditingNode(null)
  }, [editText, node.id, onEdit, setEditingNode])

  const handleCancel = useCallback(() => {
    setEditText('')
    setEditingNode(null)
  }, [setEditingNode])

  const handleKeyDown = useCallback(
    (e: React.KeyboardEvent) => {
      if (e.key === 'Enter') {
        e.preventDefault()
        handleSave()
      } else if (e.key === 'Escape') {
        e.preventDefault()
        handleCancel()
      }
    },
    [handleSave, handleCancel]
  )

  // Determine if this is a "New Question" placeholder
  const isNewQuestion = node.label === DEFAULT_NODE_LABELS.NEW_QUESTION

  if (isEditing) {
    return (
      <input
        type="text"
        value={editText}
        onChange={e => setEditText(e.target.value)}
        onKeyDown={handleKeyDown}
        onBlur={handleSave}
        className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
        autoFocus
        onClick={e => e.stopPropagation()}
      />
    )
  }

  return (
    <div
      className={cn('px-2 py-1 text-sm transition-all duration-200', {
        // Loading state
        'animate-pulse bg-blue-50 text-blue-700': isCurrentNodeLoading,

        // Target highlight (navigation from flow diagram)
        'bg-green-100 text-green-800 font-medium':
          targetNodeId === node.id && !isCurrentNodeLoading,

        // Node type styling
        'font-semibold text-gray-900':
          node.type === 'category' &&
          !isCurrentNodeLoading &&
          targetNodeId !== node.id,
        'text-gray-700':
          node.type === 'question' &&
          !isCurrentNodeLoading &&
          targetNodeId !== node.id,

        // New question placeholder styling
        'text-gray-400 italic':
          isNewQuestion && !isCurrentNodeLoading && targetNodeId !== node.id,
      })}
    >
      {isCurrentNodeLoading ? <span>Generating...</span> : node.label}
    </div>
  )
}

// Memoize to prevent unnecessary re-renders when props haven't changed
export default React.memo(NodeContent, (prevProps, nextProps) => {
  return (
    prevProps.node.id === nextProps.node.id &&
    prevProps.node.label === nextProps.node.label &&
    prevProps.node.type === nextProps.node.type &&
    prevProps.editingNode === nextProps.editingNode &&
    prevProps.isCurrentNodeLoading === nextProps.isCurrentNodeLoading &&
    prevProps.targetNodeId === nextProps.targetNodeId
  )
})

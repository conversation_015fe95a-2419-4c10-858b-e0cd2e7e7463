import React, { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Plus, Trash2, Edit2 } from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { NodeActionsProps } from '@/app/(conv)/dragTree/[dragTreeId]/types'
import GuidanceDialog from '@/app/(conv)/dragTree/[dragTreeId]/components/HierarchicalOutline/components/GuidanceDialog'
import {
  TreeNodeType,
  DEFAULT_NODE_LABELS,
} from '@/app/(conv)/dragTree/[dragTreeId]/constants/node-types'
import { cn } from '@/lib/utils'

const NodeActions: React.FC<NodeActionsProps> = ({
  node,
  onAdd,
  onEdit,
  onDelete,
  onGenerateSimilarQuestions,
  onGenerateSimilarCategories,
  isLoading,
  isHovered,
  isExpanding,
  lastExpansionTime,
}) => {
  const [isGuidanceDialogOpen, setIsGuidanceDialogOpen] =
    useState<boolean>(false)
  const [guidanceType, setGuidanceType] = useState<
    'questions' | 'categories' | null
  >(null)

  const handleAIGeneration = (
    type: 'questions' | 'categories',
    e: React.MouseEvent
  ) => {
    e.preventDefault()
    e.stopPropagation()

    // Prevent dialog opening during expansion operations
    if (isExpanding) {
      console.warn('⚠️ Prevented dialog opening during expansion')
      return
    }

    // Also prevent if expansion happened very recently (within 600ms)
    if (Date.now() - lastExpansionTime < 600) {
      console.warn('⚠️ Prevented dialog opening - expansion too recent')
      return
    }

    console.log(`${type} generation clicked`)
    setGuidanceType(type)
    setIsGuidanceDialogOpen(true)
  }

  const handleGuidanceGenerate = (guidance: string) => {
    if (guidanceType === 'questions' && onGenerateSimilarQuestions) {
      onGenerateSimilarQuestions(node.id, guidance)
    } else if (guidanceType === 'categories' && onGenerateSimilarCategories) {
      onGenerateSimilarCategories(node.id, guidance)
    }
  }

  const handleGuidanceCancel = () => {
    setIsGuidanceDialogOpen(false)
    setGuidanceType(null)
  }

  return (
    <>
      <div
        className={cn(
          'flex gap-1.5 sm:gap-2 transition-opacity duration-200 opacity-100 sm:opacity-100',
          {
            'sm:opacity-75': !isHovered, // Only apply reduced opacity on desktop when not hovered
          }
        )}
      >
        {/* Manual add buttons in dropdown + AI generation buttons standalone */}
        {node.type === TreeNodeType.CATEGORY && (
          <>
            {/* Dropdown for manual add operations */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="outline"
                  size="icon"
                  disabled={isLoading}
                  className={cn(
                    'h-7 w-7 sm:h-6 sm:w-6 p-0 rounded-full bg-gradient-to-r from-emerald-50 to-teal-50 border-2 border-emerald-200 shadow-sm transition-all duration-200 touch-manipulation',
                    {
                      'opacity-50 cursor-not-allowed': isLoading,
                      'from-emerald-100 to-teal-100 border-emerald-300 shadow-md transform scale-105':
                        !isLoading && isHovered,
                    }
                  )}
                  title="Add Content"
                >
                  <Plus className="h-3.5 w-3.5 sm:h-3 sm:w-3 text-emerald-600" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="rounded-xl border-0 shadow-xl bg-white/95 backdrop-blur-sm">
                <DropdownMenuItem
                  onClick={() => onAdd(node.id, TreeNodeType.CATEGORY)}
                  className="rounded-lg"
                >
                  {DEFAULT_NODE_LABELS.NEW_CATEGORY}
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => onAdd(node.id, TreeNodeType.QUESTION)}
                  className="rounded-lg"
                >
                  {DEFAULT_NODE_LABELS.NEW_QUESTION}
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>

            {/* AI generation buttons standalone */}
            {onGenerateSimilarQuestions && (
              <Button
                variant="outline"
                size="icon"
                disabled={isLoading || isExpanding}
                onClick={e => handleAIGeneration('questions', e)}
                className={cn(
                  'h-7 w-7 sm:h-6 sm:w-6 p-0 rounded-full bg-gradient-to-r from-cyan-50 to-blue-50 border-2 border-cyan-200 shadow-sm transition-all duration-200 touch-manipulation',
                  {
                    'opacity-50 cursor-not-allowed': isLoading,
                    'from-cyan-100 to-blue-100 border-cyan-300 shadow-md transform scale-105':
                      !isLoading && isHovered,
                  }
                )}
                title="Generate Similar Questions"
              >
                <span className="text-xs font-bold text-cyan-600">Q+</span>
              </Button>
            )}

            {onGenerateSimilarCategories && (
              <Button
                variant="outline"
                size="icon"
                disabled={isLoading || isExpanding}
                onClick={e => handleAIGeneration('categories', e)}
                className={cn(
                  'h-7 w-7 sm:h-6 sm:w-6 p-0 rounded-full bg-gradient-to-r from-orange-50 to-amber-50 border-2 border-orange-200 shadow-sm transition-all duration-200 touch-manipulation',
                  {
                    'opacity-50 cursor-not-allowed': isLoading,
                    'from-orange-100 to-amber-100 border-orange-300 shadow-md transform scale-105':
                      !isLoading && isHovered,
                  }
                )}
                title="Generate Similar Categories"
              >
                <span className="text-xs font-bold text-orange-600">C+</span>
              </Button>
            )}
          </>
        )}

        <Button
          variant="outline"
          size="icon"
          disabled={isLoading}
          onClick={onEdit}
          className={cn(
            'h-7 w-7 sm:h-6 sm:w-6 p-0 rounded-full bg-gradient-to-r from-blue-50 to-indigo-50 border-2 border-blue-200 shadow-sm transition-all duration-200 touch-manipulation',
            {
              'opacity-50 cursor-not-allowed': isLoading,
              'from-blue-100 to-indigo-100 border-blue-300 shadow-md transform scale-105':
                !isLoading && isHovered,
            }
          )}
          title="Edit"
        >
          <Edit2 className="h-3.5 w-3.5 sm:h-3 sm:w-3 text-blue-600" />
        </Button>

        {/* Hide delete button for root node */}
        {node.id !== 'root' && (
          <Button
            variant="outline"
            size="icon"
            disabled={isLoading}
            onClick={onDelete}
            className={cn(
              'h-7 w-7 sm:h-6 sm:w-6 p-0 rounded-full bg-gradient-to-r from-rose-50 to-red-50 border-2 border-rose-200 shadow-sm transition-all duration-200 touch-manipulation',
              {
                'opacity-50 cursor-not-allowed': isLoading,
                'from-rose-100 to-red-100 border-rose-300 shadow-md transform scale-105':
                  !isLoading && isHovered,
              }
            )}
            title="Delete"
          >
            <Trash2 className="h-3.5 w-3.5 sm:h-3 sm:w-3 text-rose-600" />
          </Button>
        )}
      </div>

      {/* Guidance dialog for AI generation */}
      <GuidanceDialog
        isOpen={isGuidanceDialogOpen}
        guidanceType={guidanceType}
        node={node}
        onGenerate={handleGuidanceGenerate}
        onCancel={handleGuidanceCancel}
        isExpanding={isExpanding}
      />
    </>
  )
}

export default NodeActions

import React, { useMemo, useState } from 'react'
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragOverlay,
} from '@dnd-kit/core'
import { SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable'
import { GripVertical } from 'lucide-react'
import { TreeNode } from '@/app/types'
import { OutlineViewProps } from '@/app/(conv)/dragTree/[dragTreeId]/types'
import { SortableOutlineItem } from './SortableOutlineItem'
import OutlineView from '@/app/(conv)/dragTree/[dragTreeId]/components/HierarchicalOutline/OutlineView'
import { cn } from '@/lib/utils'

// Exclude props that are not needed for the child list
type SortableChildListProps = Omit<OutlineViewProps, 'node'> & {
  parentNode: TreeNode
  isParentCollapsed: boolean
}

const SortableChildList: React.FC<SortableChildListProps> = ({
  parentNode,
  isParentCollapsed,
  onAdd,
  onDelete,
  onEdit,
  onReorder,
  editingNode,
  setEditingNode,
  collapsedNodes,
  setCollapsedNodes,
  targetNodeId,
  onGenerateSimilarQuestions,
  onGenerateSimilarCategories,
  isLoading,
  loadingNodeId,
  isGloballyExpanded,
}) => {
  const [activeId, setActiveId] = useState<string | null>(null)

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8, // Increased for touch precision
        delay: 150, // Longer delay for mobile to prevent accidental drags
        tolerance: 10, // Higher tolerance for finger movement
      },
    }),
    useSensor(KeyboardSensor)
  )

  const activeItem = useMemo(() => {
    if (!activeId) return null
    return parentNode.children.find(child => child.id === activeId)
  }, [activeId, parentNode.children])

  if (parentNode.children.length === 0) {
    return null
  }

  return (
    <DndContext
      sensors={sensors}
      collisionDetection={closestCenter}
      onDragStart={event => setActiveId(event.active.id as string)}
      onDragEnd={event => {
        const { active, over } = event
        if (active && over && active.id !== over.id) {
          const oldIndex = parentNode.children.findIndex(
            c => c.id === active.id
          )
          const newIndex = parentNode.children.findIndex(c => c.id === over.id)
          if (oldIndex !== -1 && newIndex !== -1) {
            onReorder(parentNode.id, oldIndex, newIndex)
          }
        }
        setActiveId(null)
      }}
      onDragCancel={() => setActiveId(null)}
    >
      <SortableContext
        items={parentNode.children.map(child => child.id)}
        strategy={verticalListSortingStrategy}
      >
        <div
          className={cn(
            'pl-4 sm:pl-6 pb-2 relative transition-all duration-500 ease-in-out',
            {
              'overflow-hidden max-h-0 opacity-0 transform scale-y-95 -translate-y-2':
                isParentCollapsed,
              'overflow-visible max-h-none opacity-100 transform scale-y-100 translate-y-0':
                !isParentCollapsed,
            }
          )}
          style={{
            transformOrigin: 'top',
          }}
        >
          {/* Modern connecting line with animation */}
          <div
            className={cn(
              'absolute left-3 top-0 bottom-4 w-0.5 bg-gradient-to-b from-slate-300 to-transparent transition-all duration-500',
              {
                'opacity-0 scale-y-0': isParentCollapsed,
                'opacity-100 scale-y-100': !isParentCollapsed,
              }
            )}
          ></div>
          {parentNode.children.map((child, index) => (
            <div
              key={child.id}
              className={cn(
                'relative transition-all duration-500 ease-in-out',
                {
                  'opacity-0 transform translate-y-[-10px]': isParentCollapsed,
                  'opacity-100 transform translate-y-0': !isParentCollapsed,
                }
              )}
              style={{
                transitionDelay: isParentCollapsed ? '0ms' : `${index * 50}ms`,
              }}
            >
              {/* Modern branch connector */}
              <div
                className={cn(
                  'absolute left-3 top-6 w-4 h-0.5 bg-slate-300 transition-all duration-300',
                  {
                    'opacity-0 scale-x-0': isParentCollapsed,
                    'opacity-100 scale-x-100': !isParentCollapsed,
                  }
                )}
              ></div>
              <div
                className={cn('transition-all duration-200', {
                  'ring-1 ring-blue-400 bg-blue-50/20 rounded-lg':
                    activeId && activeId !== child.id,
                })}
              >
                <SortableOutlineItem id={child.id}>
                  <OutlineView
                    node={child}
                    onAdd={onAdd}
                    onDelete={onDelete}
                    onEdit={onEdit}
                    onReorder={onReorder}
                    editingNode={editingNode}
                    setEditingNode={setEditingNode}
                    collapsedNodes={collapsedNodes}
                    setCollapsedNodes={setCollapsedNodes}
                    targetNodeId={targetNodeId}
                    onGenerateSimilarQuestions={onGenerateSimilarQuestions}
                    onGenerateSimilarCategories={onGenerateSimilarCategories}
                    isLoading={isLoading}
                    loadingNodeId={loadingNodeId}
                    isGloballyExpanded={isGloballyExpanded}
                  />
                </SortableOutlineItem>
              </div>
            </div>
          ))}
        </div>
      </SortableContext>

      <DragOverlay
        dropAnimation={{
          duration: 200,
          easing: 'cubic-bezier(0.18, 0.67, 0.6, 1.22)',
        }}
      >
        {activeItem ? (
          <div
            style={{
              transform: 'translate(8px, 8px)', // Position slightly offset from cursor for better visibility
              pointerEvents: 'none',
              cursor: 'grabbing',
            }}
            className="relative"
          >
            {/* Main drag card - larger for mobile visibility */}
            <div className="p-3 sm:p-2 bg-white border-2 border-blue-500 rounded-lg shadow-xl max-w-xs sm:max-w-sm backdrop-blur-sm ring-2 ring-blue-200">
              <div className="flex items-center gap-2">
                <GripVertical className="h-5 w-5 sm:h-4 sm:w-4 text-blue-600 flex-shrink-0" />
                <span className="font-medium text-slate-800 text-sm sm:text-sm truncate">
                  {activeItem.label}
                </span>
              </div>
              <div className="text-xs text-blue-600 mt-1 font-medium">
                {activeItem.type === 'category' ? 'Category' : 'Question'}
              </div>
            </div>

            {/* Visual indicator for drop target */}
            <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-blue-500 rounded-full border-2 border-white shadow-lg animate-pulse"></div>
          </div>
        ) : null}
      </DragOverlay>
    </DndContext>
  )
}

export default React.memo(SortableChildList)

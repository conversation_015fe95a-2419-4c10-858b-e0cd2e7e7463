'use client'

import { useMemo, useRef, useEffect, useState } from 'react'
import { useDragTreeStore } from '@/app/stores/dragtree_store'
import { useTreeToReactFlowWorker } from '@/app/(conv)/dragTree/[dragTreeId]/hooks/useTreeToReactFlowWorker'
import { computeLayout } from '@/app/(conv)/dragTree/[dragTreeId]/utils/computeLayout'
import markdownToTreeNode from '@/app/(conv)/dragTree/[dragTreeId]/utils/markdownToTreeNode'
import { addSuffixAfterNumber } from '@/lib/utils'
import { isDevelopmentOrCi } from '@/lib/environment'
import type { LayoutMode } from '@/app/(conv)/dragTree/[dragTreeId]/types'

type UseDiagramDataProps = {
  isStreaming: boolean
  streamingContent: string
  layoutMode?: LayoutMode
}

type UseDiagramDataReturn = {
  nodes: any[]
  edges: any[]
  displayTreeData: any
}

/**
 * Custom hook to manage diagram data processing
 * Handles streaming content parsing, tree-to-flow conversion, and layout
 * Optimized to prevent unnecessary conversions
 */
export const useDiagramData = ({
  isStreaming,
  streamingContent,
  layoutMode = 'linear',
}: UseDiagramDataProps): UseDiagramDataReturn => {
  const treeData = useDragTreeStore(state => state.frontendTreeStructure)
  const lastTreeId = useRef<string | null>(null)
  const conversionCount = useRef(0)

  /**
   * For streaming: parse markdown in real-time for diagram only
   * Falls back to current tree data if parsing fails during streaming
   * Memoized with tree ID for more efficient comparison
   */
  const displayTreeData = useMemo(() => {
    if (isStreaming && streamingContent) {
      try {
        return markdownToTreeNode(
          addSuffixAfterNumber(streamingContent, '[EXAMPLE NUMBER]')
        )
      } catch (error) {
        console.warn('Failed to parse streaming content:', error)
        return treeData
      }
    }
    return treeData
  }, [isStreaming, streamingContent, treeData?.id]) // Use tree ID for efficient comparison

  /**
   * Convert tree data to diagram format in a Web Worker. The hook returns
   * `{ nodes, edges }` which will be empty arrays while the worker is running.
   */
  const diagramData = useTreeToReactFlowWorker(displayTreeData)

  // Simple dev-time diagnostics (retain previous behaviour)
  useEffect(() => {
    if (!displayTreeData) return

    if (lastTreeId.current !== displayTreeData.id) {
      lastTreeId.current = displayTreeData.id
      conversionCount.current = 1
    } else {
      conversionCount.current += 1
    }

    if (isDevelopmentOrCi()) {
      console.log(
        `🔄 [useDiagramData] Worker conversion #${conversionCount.current} for tree`,
        displayTreeData.id
      )
    }
  }, [diagramData.nodes.length, displayTreeData?.id])

  /**
   * Apply layout algorithm to nodes and edges based on layout mode
   * Only recalculate when diagram data or layout mode changes
   */
  const [layoutedElements, setLayoutedElements] = useState<{
    nodes: any[]
    edges: any[]
  }>({ nodes: [], edges: [] })

  useEffect(() => {
    const applyLayout = async () => {
      if (diagramData.nodes.length === 0) {
        setLayoutedElements({ nodes: [], edges: [] })
        return
      }

      try {
        const result = await computeLayout(
          layoutMode,
          diagramData.nodes,
          diagramData.edges
        )
        setLayoutedElements(result)
      } catch (error) {
        console.error('Layout failed:', error)
        // Fallback: simply return un-layouted diagram data
        setLayoutedElements({
          nodes: diagramData.nodes,
          edges: diagramData.edges,
        })
      }
    }

    applyLayout()
  }, [diagramData.nodes.length, diagramData.edges.length, layoutMode])

  const { nodes, edges } = layoutedElements

  return {
    nodes,
    edges,
    displayTreeData,
  }
}

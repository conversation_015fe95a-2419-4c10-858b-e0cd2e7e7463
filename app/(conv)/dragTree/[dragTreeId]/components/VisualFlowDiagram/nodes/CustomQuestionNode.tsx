import React from 'react'
import { NodeProps, <PERSON>le, Position, useReactFlow } from 'reactflow'
import { useNavigationStore } from '@/app/stores/navigation_store'
import { useDragTreeStore } from '@/app/stores/dragtree_store'
import QuickResearchDropdownButton from '@/app/(conv)/dragTree/[dragTreeId]/components/shared/QuickResearchDropdownButton'
import { DEFAULT_NODE_LABELS } from '@/app/(conv)/dragTree/[dragTreeId]/constants/node-types'
import { DragTreeNodeContentStatus } from '@prisma/client'
import { cn } from '@/lib/utils'

const handleStyle = {
  width: 10,
  height: 10,
  background: '#a8b3cf',
  border: '2px solid white',
}

/**
 * Custom Question Node component for React Flow
 * Displays question nodes with gradient styling and targeting effects
 */
const CustomQuestionNode: React.FC<NodeProps> = ({ data, id }) => {
  const { targetNodeId, navigateToTreeNodeFromReactFlow } = useNavigationStore()
  const { fitView } = useReactFlow()
  const isTargeted = targetNodeId === id

  // Access research data from store
  const nodeContentMap = useDragTreeStore(state => state.nodeContent.get(id))
  const researchedNodeIds = useDragTreeStore(state => state.researchedNodeIds)

  // Check if this is a new node that hasn't been renamed
  const isNewNode =
    data.label === DEFAULT_NODE_LABELS.NEW_CATEGORY ||
    data.label === DEFAULT_NODE_LABELS.NEW_QUESTION

  const hasContent = nodeContentMap && nodeContentMap.size > 0
  const isResearched = researchedNodeIds.has(id)

  // A node is considered "researched" if it's in the researched set,
  // or if it has any content (even if still processing).
  const hasBeenResearched = isResearched || hasContent

  // Handle node click to navigate to outline and focus in React Flow
  const handleNodeClick = (e: React.MouseEvent) => {
    // Prevent click if clicking on the research button
    if ((e.target as HTMLElement).closest('button')) {
      return
    }

    console.log(`🖱️ React Flow Question Node clicked: ${id}`)

    // Focus on the clicked node in React Flow
    fitView({
      nodes: [{ id }],
      duration: 500,
      padding: 0.3,
      maxZoom: 1.2,
    })

    // Navigate to tree outline
    navigateToTreeNodeFromReactFlow(id)
  }

  return (
    <div
      className={cn(
        'p-4 rounded-2xl border-2 shadow-lg relative transition-all duration-300 min-w-[320px] max-w-[500px] cursor-pointer hover:scale-105 hover:shadow-xl',
        {
          // New nodes - keep rose colors
          'bg-gradient-to-br from-rose-100 via-pink-50 to-orange-100 border-rose-300 shadow-rose-200/60 hover:shadow-rose-300/80':
            isNewNode,
          // Nodes with research - green gradient (targeted)
          'bg-gradient-to-br from-emerald-100 via-teal-100 to-cyan-100 border-emerald-400 ring-2 ring-emerald-300 hover:from-emerald-200 hover:via-teal-200 hover:to-cyan-200':
            !isNewNode && isTargeted && hasBeenResearched,
          // Nodes with research - green gradient (normal)
          'bg-gradient-to-br from-emerald-50 via-teal-50 to-cyan-50 border-emerald-300 shadow-emerald-200/50 hover:border-emerald-400 hover:shadow-emerald-300/70 hover:from-emerald-100 hover:via-teal-100 hover:to-cyan-100':
            !isNewNode && !isTargeted && hasBeenResearched,
          // Nodes without research - red gradient (targeted)
          'bg-gradient-to-br from-red-100 via-rose-100 to-red-100 border-red-400 ring-2 ring-red-300 hover:from-red-200 hover:via-rose-200 hover:to-red-200':
            !isNewNode && isTargeted && !hasBeenResearched,
          // Nodes without research - red gradient (normal)
          'bg-gradient-to-br from-red-50 via-rose-50 to-red-50 border-red-300 shadow-red-200/50 hover:border-red-400 hover:shadow-red-300/70 hover:from-red-100 hover:via-rose-100 hover:to-red-100':
            !isNewNode && !isTargeted && !hasBeenResearched,
        }
      )}
      onClick={handleNodeClick}
    >
      {/* Handles for all four positions */}
      <Handle
        type="source"
        position={Position.Top}
        id="top"
        style={handleStyle}
      />
      <Handle
        type="source"
        position={Position.Right}
        id="right"
        style={handleStyle}
      />
      <Handle
        type="source"
        position={Position.Bottom}
        id="bottom"
        style={handleStyle}
      />
      <Handle
        type="source"
        position={Position.Left}
        id="left"
        style={handleStyle}
      />
      <Handle
        type="target"
        position={Position.Top}
        id="top"
        style={handleStyle}
      />
      <Handle
        type="target"
        position={Position.Right}
        id="right"
        style={handleStyle}
      />
      <Handle
        type="target"
        position={Position.Bottom}
        id="bottom"
        style={handleStyle}
      />
      <Handle
        type="target"
        position={Position.Left}
        id="left"
        style={handleStyle}
      />

      {/* Node Content - QUESTION STYLE */}
      <div className="flex flex-col gap-2 items-start text-left">
        <div className="flex items-center justify-between">
          <span className="text-xs font-mono text-blue-600 bg-blue-50 px-2.5 py-1 rounded-lg border border-blue-200 font-medium">
            {id}
          </span>

          {/* Research Button */}
          {data.label !== DEFAULT_NODE_LABELS.NEW_QUESTION && (
            <QuickResearchDropdownButton
              questionText={data.label}
              questionNodeId={id}
              variant="reactflow"
              className={cn({
                'text-rose-600 hover:text-rose-700': isNewNode,
                'text-blue-600 hover:text-blue-700': !isNewNode && isTargeted,
                'text-slate-600 hover:text-slate-700':
                  !isNewNode && !isTargeted,
              })}
            />
          )}
        </div>

        <span
          className={cn(
            'font-semibold text-sm leading-relaxed break-words hyphens-auto transition-colors duration-200',
            {
              'text-rose-700': isNewNode,
              'text-blue-700': !isNewNode && isTargeted,
              'text-slate-700': !isNewNode && !isTargeted,
            }
          )}
        >
          {data.label}
        </span>
      </div>
    </div>
  )
}

// Memoize to prevent unnecessary re-renders when data hasn't changed
export default React.memo(CustomQuestionNode)

'use client'
import React from 'react'
import { Button } from '@/components/ui/button'
import { LayoutGrid, Target } from 'lucide-react'
import { cn } from '@/lib/utils'

type LayoutModeToggleProps = {
  layoutMode: 'linear' | 'radial'
  setLayoutMode: (mode: 'linear' | 'radial') => void
}

export const LayoutModeToggle: React.FC<LayoutModeToggleProps> = React.memo(
  function LayoutModeToggle({ layoutMode, setLayoutMode }) {
    return (
      <div className="absolute top-4 left-4 z-10">
        <div className="flex items-center bg-white/80 backdrop-blur-sm border border-gray-200/50 rounded-lg p-1 shadow-sm">
          <Button
            variant={layoutMode === 'linear' ? 'default' : 'ghost'}
            size="sm"
            onClick={() => setLayoutMode('linear')}
            className={cn(
              'h-8 px-3 text-xs font-medium transition-all duration-200',
              layoutMode === 'linear'
                ? 'bg-blue-500 text-white shadow-sm'
                : 'text-gray-600 hover:text-gray-800 hover:bg-gray-100/50'
            )}
          >
            <LayoutGrid className="w-3 h-3 mr-1.5" />
            Hierarchical
          </Button>
          <Button
            variant={layoutMode === 'radial' ? 'default' : 'ghost'}
            size="sm"
            onClick={() => setLayoutMode('radial')}
            className={cn(
              'h-8 px-3 text-xs font-medium transition-all duration-200',
              layoutMode === 'radial'
                ? 'bg-purple-500 text-white shadow-sm'
                : 'text-gray-600 hover:text-gray-800 hover:bg-gray-100/50'
            )}
          >
            <Target className="w-3 h-3 mr-1.5" />
            Circular
          </Button>
        </div>
      </div>
    )
  }
)

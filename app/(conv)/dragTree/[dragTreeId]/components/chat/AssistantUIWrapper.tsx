'use client'

import React, { useState, useRef, useEffect, useCallback } from 'react'
import { useChatRuntime } from '@assistant-ui/react-ai-sdk'
import {
  AssistantRuntimeProvider,
  MessagePrimitive,
  useMessage,
  ThreadPrimitive,
  ComposerPrimitive,
} from '@assistant-ui/react'
import type { UIChatMessage } from '@/app/api/aipane/chat/types'
import { ReasoningTimeline } from './ReasoningTimeline'
import {
  FiCopy,
  FiChevronDown,
  FiChevronUp,
  FiSearch,
  FiLoader,
  FiArrowDown,
} from 'react-icons/fi'
import toast from 'react-hot-toast'

/**
 * Extended message type compatible with ChatTabContent
 */
type ExtendedMessage = {
  id: string
  role: 'user' | 'assistant' | 'system'
  content: string
  createdAt: string
  stepCount?: number
  steps?: any[]
  isStreaming?: boolean
  cleanContent?: string
  timestamp?: Date
}

/**
 * Props for AssistantUIWrapper component
 */
type AssistantUIWrapperProps = {
  conversationId?: string
  initialMessages?: ExtendedMessage[]
  onMessageFinish?: (message: any) => void
  // Pagination support
  hasMoreMessages?: boolean
  isLoadingMoreMessages?: boolean
  onLoadMoreMessages?: () => void
}

/**
 * Custom message component that can render tool calls and reasoning
 */
const CustomMessage: React.FC<{
  messageId?: string
  conversationId?: string
}> = ({ messageId, conversationId }) => {
  // Use selector that returns the store object itself to keep snapshot stable
  const content = useMessage(m => m.content)
  const role = useMessage(m => m.role)
  const status = useMessage(m => m.status)
  const stepCount = useMessage(m => (m as any).stepCount)
  const isStreaming = useMessage(m => (m as any).isStreaming)
  const steps = useMessage((m: any) => m.steps)

  const message = React.useMemo(
    () => ({
      content,
      role,
      status,
      stepCount,
      isStreaming,
      id: messageId ?? '',
      steps,
    }),
    [content, role, status, stepCount, isStreaming, messageId, steps]
  )

  // ────────────────────────────────────────────────
  // Tool-call rendering – show live web_search queries
  // ────────────────────────────────────────────────
  const WebSearchToolCallPart: React.FC<any> = ({ args, status }) => {
    const query = args?.query || ''
    const isRunning = status?.type === 'running'
    const resultCount = status?.result?.resultCount || status?.resultCount
    return (
      <div className="flex items-center gap-1 text-xs text-gray-500 my-1">
        <FiSearch className="w-3 h-3" />
        {isRunning ? (
          <>
            <FiLoader className="w-3 h-3 animate-spin" /> Searching:{' '}
            <span className="font-mono">{query}</span>
          </>
        ) : (
          <>
            Found <span className="font-semibold">{resultCount ?? '?'}</span>{' '}
            results for <span className="font-mono">{query}</span>
          </>
        )}
      </div>
    )
  }

  // Memoise tool components mapping to avoid new object each render
  const toolComponents = React.useMemo(
    () => ({
      Fallback: WebSearchToolCallPart,
      by_name: {
        web_search: WebSearchToolCallPart,
      },
    }),
    []
  )

  // Determine if message is long (heuristic by character count)
  const LONG_THRESHOLD = 800
  const charCount = (() => {
    if (!message.content || typeof message.content !== 'string') return 0
    return (message.content as string).length
  })()

  const hasTextContent = (() => {
    if (!message.content) return false
    if (typeof message.content === 'string')
      return (message.content as string).trim().length > 0
    // If content is array of parts, check if any text part exists
    if (Array.isArray(message.content)) {
      return message.content.some((p: any) => {
        if (!p || p.type !== 'text') return false
        const txt = p.text as string | undefined
        return typeof txt === 'string' && (txt as string).trim().length > 0
      })
    }
    return false
  })()
  const isLong = charCount > LONG_THRESHOLD
  const [collapsed, setCollapsed] = useState<boolean>(isLong)

  // Copy message content to clipboard
  const handleCopy = async () => {
    try {
      const textToCopy =
        typeof message.content === 'string'
          ? (message.content as string)
          : Array.isArray(message.content)
            ? (message.content as any[])
                .map(part => {
                  if (typeof part === 'string') return part
                  // Fallback for structured parts
                  if ('text' in part) return part.text
                  return ''
                })
                .join('')
            : JSON.stringify(message.content)

      await navigator.clipboard.writeText(textToCopy)
      toast.success('Message copied')
    } catch (e) {
      toast.error('Failed to copy')
    }
  }

  // Check if this message has step count (indicating reasoning/tool usage)
  const hasSteps =
    message.role === 'assistant' &&
    (message as any).stepCount &&
    (message as any).stepCount > 0

  // Debug logging for step data
  if (message.role === 'assistant') {
    console.log('[AssistantUIWrapper] Assistant message debug:', {
      messageId: message.id,
      stepCount: (message as any).stepCount,
      steps: (message as any).steps,
      hasSteps,
      isStreaming: (message as any).isStreaming,
    })
  }

  const bubbleBaseClasses =
    'relative inline-block rounded-lg max-w-[80%] text-sm leading-relaxed'
  const bubblePadding = 'px-3 py-2'

  const bubbleColorClasses =
    message.role === 'user'
      ? 'bg-blue-600 text-white ml-auto mr-2'
      : 'bg-gray-100 text-gray-900 dark:bg-gray-800 dark:text-gray-100 ml-2 mr-auto'

  return (
    <MessagePrimitive.Root>
      <div
        className={`mb-4 ${
          message.role === 'user' ? 'text-right' : 'text-left'
        }`}
      >
        <div
          className={`${bubbleBaseClasses} ${bubblePadding} ${bubbleColorClasses}`}
        >
          {/* Copy icon */}
          <button
            onClick={handleCopy}
            className="absolute top-1 right-1 text-xs text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
            title="Copy message"
          >
            <FiCopy className="w-4 h-4" />
          </button>

          {/* Message content with optional collapse */}
          <div className="whitespace-pre-wrap break-words">
            {/* If there's no final text but there are tool parts, render parts directly */}
            {!hasTextContent ? (
              <MessagePrimitive.Parts components={{ tools: toolComponents }} />
            ) : collapsed && isLong ? (
              <>
                <div className="overflow-hidden max-h-60">
                  {/* approx 15rem */}
                  {/* Provide empty components object to avoid undefined error inside MessagePrimitive.Parts */}
                  <MessagePrimitive.Content
                    components={{ tools: toolComponents }}
                  />
                </div>
                {/* Gradient overlay */}
                <div className="absolute bottom-0 left-0 w-full h-10 bg-gradient-to-b from-transparent to-inherit pointer-events-none rounded-b-lg" />
                <button
                  onClick={() => setCollapsed(false)}
                  className="mt-2 flex items-center gap-1 text-xs text-blue-500 hover:underline"
                >
                  Show more <FiChevronDown className="w-3 h-3" />
                </button>
              </>
            ) : (
              <>
                <MessagePrimitive.Content
                  components={{ tools: toolComponents }}
                />
                {isLong && (
                  <button
                    onClick={() => setCollapsed(true)}
                    className="mt-2 flex items-center gap-1 text-xs text-blue-500 hover:underline"
                  >
                    Show less <FiChevronUp className="w-3 h-3" />
                  </button>
                )}
              </>
            )}
          </div>
        </div>

        {/* Show reasoning timeline for assistant messages with steps */}
        {hasSteps && (
          <div className="mt-3">
            <ReasoningTimeline
              messageId={message.id}
              stepCount={(message as any).stepCount || 0}
              isStreaming={(message as any).isStreaming || false}
              liveSteps={(message as any).steps}
            />
          </div>
        )}
      </div>
    </MessagePrimitive.Root>
  )
}

/**
 * Assistant-UI wrapper component for chat interface
 * Provides a modern, robust chat UI to replace the legacy implementation
 */
export default function AssistantUIWrapper({
  conversationId,
  initialMessages = [],
  onMessageFinish,
  hasMoreMessages = false,
  isLoadingMoreMessages = false,
  onLoadMoreMessages,
}: AssistantUIWrapperProps) {
  // Scroll management state
  const [isAtBottom, setIsAtBottom] = useState(true)
  const [showScrollToBottom, setShowScrollToBottom] = useState(false)
  const scrollContainerRef = useRef<HTMLDivElement>(null)
  const isInitialLoad = useRef(true)

  // Convert our message format to assistant-ui format
  const formattedMessages = initialMessages.map(msg => ({
    id: msg.id,
    role: msg.role.toLowerCase() as 'user' | 'assistant' | 'system',
    content: ('cleanContent' in msg ? msg.cleanContent : null) || msg.content,
    createdAt:
      msg.timestamp ||
      ('createdAt' in msg && msg.createdAt
        ? new Date(msg.createdAt)
        : new Date()),
    // Include step data for reasoning timeline
    stepCount: 'stepCount' in msg ? msg.stepCount : undefined,
    isStreaming: 'isStreaming' in msg ? msg.isStreaming : undefined,
  }))

  // Debug: log initial messages to verify they reach wrapper
  console.log('[AssistantUIWrapper] initialMessages:', formattedMessages)

  // Set up chat runtime with API endpoint
  const runtime = useChatRuntime({
    api: '/api/aipane/chat',
    initialMessages: formattedMessages,
    body: {
      conversationId,
    },
    onFinish: onMessageFinish,
  })

  // Handle scroll events
  const handleScroll = useCallback(() => {
    const container = scrollContainerRef.current
    if (!container) return

    const { scrollTop, scrollHeight, clientHeight } = container
    const isNearBottom = scrollHeight - scrollTop - clientHeight <= 50

    setIsAtBottom(isNearBottom)
    setShowScrollToBottom(!isNearBottom && scrollTop > 200)
  }, [])

  // Scroll to bottom function
  const scrollToBottom = useCallback(() => {
    const container = scrollContainerRef.current
    if (container) {
      container.scrollTo({
        top: container.scrollHeight,
        behavior: 'smooth',
      })
    }
  }, [])

  // Auto-scroll to bottom on initial load and new messages
  useEffect(() => {
    const container = scrollContainerRef.current
    if (!container) return

    // For initial load, scroll to bottom immediately
    if (isInitialLoad.current) {
      setTimeout(() => {
        container.scrollTop = container.scrollHeight
        isInitialLoad.current = false
      }, 100)
      return
    }

    // For new messages, auto-scroll if user is at bottom
    if (isAtBottom) {
      setTimeout(() => {
        container.scrollTo({
          top: container.scrollHeight,
          behavior: 'smooth',
        })
      }, 100)
    }
  }, [formattedMessages.length, isAtBottom])

  return (
    <AssistantRuntimeProvider runtime={runtime}>
      <div className="flex flex-col h-full">
        {/* Main chat thread with custom message renderer and built-in pagination */}
        <div className="flex-1 min-h-0 relative">
          <div
            ref={scrollContainerRef}
            className="h-full overflow-y-auto chat-thread"
            onScroll={handleScroll}
          >
            <ThreadPrimitive.Root className="h-full min-h-0">
              <ThreadPrimitive.Messages
                components={{ Message: CustomMessage }}
              />
            </ThreadPrimitive.Root>
          </div>

          {/* Scroll to bottom button */}
          {showScrollToBottom && (
            <button
              onClick={scrollToBottom}
              className="absolute bottom-4 right-4 bg-blue-600 hover:bg-blue-700 text-white rounded-full p-3 shadow-lg transition-colors duration-200 z-10"
              aria-label="Scroll to bottom"
            >
              <FiArrowDown className="w-5 h-5" />
            </button>
          )}
        </div>

        {/* Input composer */}
        <div className="border-t bg-background p-4">
          <ComposerPrimitive.Root className="relative flex items-end bg-gray-50 rounded-xl border border-gray-200 focus-within:border-blue-500 transition-colors">
            <ComposerPrimitive.Input
              placeholder="Message AI Assistant..."
              className="flex-1 resize-y overflow-auto max-h-60 min-h-[44px] bg-transparent border-none shadow-none focus:ring-0 focus:outline-none text-gray-900 placeholder-gray-500 px-4 py-3 text-sm leading-relaxed"
              rows={1}
            />
            <ComposerPrimitive.Send className="ml-2 mr-3 mb-3 w-8 h-8 p-0 rounded-lg transition-all duration-200 flex items-center justify-center bg-blue-600 hover:bg-blue-700 text-white shadow-sm hover:shadow-md disabled:bg-gray-200 disabled:text-gray-400 disabled:cursor-not-allowed">
              <svg
                className="w-4 h-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"
                />
              </svg>
            </ComposerPrimitive.Send>
          </ComposerPrimitive.Root>
        </div>
      </div>
    </AssistantRuntimeProvider>
  )
}

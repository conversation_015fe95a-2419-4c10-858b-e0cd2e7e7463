'use client'

import React, { useState } from 'react'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import {
  FiSearch,
  FiExternalLink,
  FiCopy,
  FiChevronDown,
  FiChevronRight,
  FiGlobe,
  FiClock,
  FiCheck,
  FiAlertCircle,
} from 'react-icons/fi'
import toast from 'react-hot-toast'

type SearchMetadata = {
  keyword: string
  url: string
  icon?: string
  snippets: string[]
  timestamp: string
  source: 'brave_search'
}

type SearchStepDisplayProps = {
  query: string
  results?: SearchMetadata[]
  resultCount?: number
  status: 'searching' | 'completed' | 'error'
  error?: string
  timestamp?: string
  className?: string
}

// Helper function to extract domain from URL
function getDomain(url: string): string {
  try {
    return new URL(url).hostname.replace('www.', '')
  } catch {
    return url
  }
}

// Helper function to copy to clipboard
function copyToClipboard(text: string, label: string) {
  navigator.clipboard
    .writeText(text)
    .then(() => {
      toast.success(`${label} copied to clipboard`)
    })
    .catch(() => {
      toast.error('Failed to copy to clipboard')
    })
}

// Individual search result component
const SearchResult: React.FC<{ result: SearchMetadata; index: number }> = ({
  result,
  index,
}) => {
  const [isExpanded, setIsExpanded] = useState<boolean>(false)
  const domain = getDomain(result.url)

  return (
    <div className="border border-gray-200 rounded-lg bg-white">
      {/* Result header */}
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className="w-full p-3 flex items-center gap-3 text-left hover:bg-gray-50 transition-colors"
      >
        <div className="flex items-center gap-2 flex-1 min-w-0">
          {/* Result index */}
          <div className="flex items-center justify-center w-6 h-6 bg-blue-100 text-blue-700 text-xs font-medium rounded-full shrink-0">
            {index + 1}
          </div>

          {/* Domain favicon and info */}
          <div className="flex items-center gap-2 min-w-0 flex-1">
            {result.icon ? (
              <img
                src={result.icon}
                alt=""
                className="w-4 h-4 shrink-0"
                onError={e => {
                  e.currentTarget.style.display = 'none'
                }}
              />
            ) : (
              <FiGlobe className="w-4 h-4 text-gray-400 shrink-0" />
            )}

            <div className="min-w-0 flex-1">
              <div className="font-medium text-sm text-gray-900 truncate">
                {result.keyword || domain}
              </div>
              <div className="text-xs text-gray-500 truncate">{domain}</div>
            </div>
          </div>

          {/* Snippet count */}
          <div className="text-xs text-gray-500 shrink-0">
            {result.snippets.length} snippet
            {result.snippets.length !== 1 ? 's' : ''}
          </div>
        </div>

        {/* Expand indicator */}
        {isExpanded ? (
          <FiChevronDown className="w-4 h-4 text-gray-400" />
        ) : (
          <FiChevronRight className="w-4 h-4 text-gray-400" />
        )}
      </button>

      {/* Expanded content */}
      {isExpanded && (
        <div className="px-3 pb-3 border-t border-gray-100">
          <div className="pt-3 space-y-3">
            {/* URL with external link */}
            <div className="flex items-center gap-2">
              <FiExternalLink className="w-3 h-3 text-gray-400" />
              <a
                href={result.url}
                target="_blank"
                rel="noopener noreferrer"
                className="text-xs text-blue-600 hover:text-blue-800 hover:underline truncate flex-1"
              >
                {result.url}
              </a>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => copyToClipboard(result.url, 'URL')}
                className="h-6 px-2 text-xs"
              >
                <FiCopy className="w-3 h-3" />
              </Button>
            </div>

            {/* Snippets */}
            {result.snippets.length > 0 && (
              <div className="space-y-2">
                <div className="text-xs font-medium text-gray-700">
                  Relevant Snippets:
                </div>
                <div className="space-y-2">
                  {result.snippets.map((snippet, snippetIndex) => (
                    <div
                      key={snippetIndex}
                      className="text-xs text-gray-600 p-2 bg-gray-50 rounded border-l-2 border-blue-200"
                    >
                      "{snippet}"
                    </div>
                  ))}
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() =>
                    copyToClipboard(result.snippets.join('\n\n'), 'Snippets')
                  }
                  className="h-6 text-xs"
                >
                  <FiCopy className="w-3 h-3 mr-1" />
                  Copy Snippets
                </Button>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  )
}

export const SearchStepDisplay: React.FC<SearchStepDisplayProps> = ({
  query,
  results = [],
  resultCount,
  status,
  error,
  timestamp,
  className,
}) => {
  const [showAllResults, setShowAllResults] = useState<boolean>(false)

  // Status indicator
  const renderStatusIndicator = () => {
    switch (status) {
      case 'searching':
        return (
          <div className="flex items-center gap-2 text-blue-600">
            <FiSearch className="w-4 h-4 animate-pulse" />
            <span className="text-sm font-medium">Searching...</span>
          </div>
        )
      case 'completed':
        return (
          <div className="flex items-center gap-2 text-green-600">
            <FiCheck className="w-4 h-4" />
            <span className="text-sm font-medium">
              Found {resultCount || results.length} result
              {(resultCount || results.length) !== 1 ? 's' : ''}
            </span>
          </div>
        )
      case 'error':
        return (
          <div className="flex items-center gap-2 text-red-600">
            <FiAlertCircle className="w-4 h-4" />
            <span className="text-sm font-medium">Search failed</span>
          </div>
        )
      default:
        return null
    }
  }

  // Show first 3 results by default
  const displayResults = showAllResults ? results : results.slice(0, 3)
  const hasMoreResults = results.length > 3

  return (
    <div className={cn('space-y-3', className)}>
      {/* Header */}
      <div className="flex items-center justify-between p-3 bg-blue-50 border border-blue-200 rounded-lg">
        <div className="flex items-center gap-3 flex-1">
          <FiSearch className="w-4 h-4 text-blue-600" />
          <div className="flex-1 min-w-0">
            <div className="text-sm font-medium text-blue-900">Web Search</div>
            <div className="text-xs text-blue-700 truncate">"{query}"</div>
          </div>
        </div>

        <div className="flex items-center gap-3">
          {renderStatusIndicator()}
          {timestamp && (
            <div className="text-xs text-blue-600 flex items-center gap-1">
              <FiClock className="w-3 h-3" />
              {new Date(timestamp).toLocaleTimeString()}
            </div>
          )}
        </div>
      </div>

      {/* Error state */}
      {status === 'error' && error && (
        <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
          <div className="text-sm text-red-700">{error}</div>
        </div>
      )}

      {/* Search results */}
      {status === 'completed' && results.length > 0 && (
        <div className="space-y-2">
          <div className="text-sm font-medium text-gray-700">
            Search Results ({results.length})
          </div>

          <div className="space-y-2">
            {displayResults.map((result, index) => (
              <SearchResult
                key={`${result.url}-${index}`}
                result={result}
                index={index}
              />
            ))}
          </div>

          {/* Show more button */}
          {hasMoreResults && !showAllResults && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowAllResults(true)}
              className="w-full h-8 text-xs text-gray-600"
            >
              Show {results.length - 3} more results
            </Button>
          )}

          {hasMoreResults && showAllResults && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowAllResults(false)}
              className="w-full h-8 text-xs text-gray-600"
            >
              Show less
            </Button>
          )}
        </div>
      )}

      {/* No results */}
      {status === 'completed' && results.length === 0 && (
        <div className="p-3 bg-gray-50 border border-gray-200 rounded-lg">
          <div className="text-sm text-gray-600 text-center">
            No search results found for "{query}"
          </div>
        </div>
      )}
    </div>
  )
}

export default SearchStepDisplay

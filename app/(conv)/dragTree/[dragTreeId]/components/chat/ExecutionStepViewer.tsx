/* eslint-disable react/display-name */
'use client'

import React, { useState } from 'react'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import {
  FiChevronDown,
  FiChevronRight,
  FiCpu,
  FiSearch,
  FiTool,
  FiFileText,
  FiUsers,
  FiClock,
  FiExternalLink,
  FiCopy,
} from 'react-icons/fi'
import { AiStepType } from '@prisma/client'
import toast from 'react-hot-toast'
import {
  getTypedStepMetadata,
  type ThoughtStepMetadata,
  type ToolCallStepMetadata,
  type ToolResultStepMetadata,
  type ReasoningSummaryStepMetadata,
  type ToolResultContext,
} from '@/app/types/ai-metadata'

type ExecutionStep = {
  id: string
  stepOrder: number
  type: AiStepType
  metadata: any
  parallelKey?: string | null
  parentStepId?: string | null
  createdAt: string
  updatedAt: string
}

type ExecutionStepViewerProps = {
  steps: ExecutionStep[]
  isLoading?: boolean
  className?: string
}

type StepDisplayProps = {
  step: ExecutionStep
  isExpanded: boolean
  onToggle: () => void
}

// Helper function to get step type icon and color
function getStepTypeInfo(type: AiStepType) {
  switch (type) {
    case AiStepType.THOUGHT:
      return {
        icon: FiCpu,
        label: 'Thinking',
        color: 'text-blue-600',
        bgColor: 'bg-blue-50',
        borderColor: 'border-blue-200',
      }
    case AiStepType.TOOL_CALL:
      return {
        icon: FiTool,
        label: 'Tool Call',
        color: 'text-green-600',
        bgColor: 'bg-green-50',
        borderColor: 'border-green-200',
      }
    case AiStepType.TOOL_RESULT:
      return {
        icon: FiFileText,
        label: 'Tool Result',
        color: 'text-orange-600',
        bgColor: 'bg-orange-50',
        borderColor: 'border-orange-200',
      }
    case AiStepType.REASONING_SUMMARY:
      return {
        icon: FiFileText,
        label: 'Summary',
        color: 'text-purple-600',
        bgColor: 'bg-purple-50',
        borderColor: 'border-purple-200',
      }
    case AiStepType.SUB_AGENT_INVOCATION:
      return {
        icon: FiUsers,
        label: 'Sub-Agent',
        color: 'text-indigo-600',
        bgColor: 'bg-indigo-50',
        borderColor: 'border-indigo-200',
      }
    default:
      return {
        icon: FiCpu,
        label: 'Unknown',
        color: 'text-gray-600',
        bgColor: 'bg-gray-50',
        borderColor: 'border-gray-200',
      }
  }
}

// Helper function to format time difference
function getTimeAgo(timestamp: string): string {
  const now = new Date()
  const time = new Date(timestamp)
  const diffMs = now.getTime() - time.getTime()
  const diffSecs = Math.floor(diffMs / 1000)

  if (diffSecs < 60) return `${diffSecs}s ago`
  const diffMins = Math.floor(diffSecs / 60)
  if (diffMins < 60) return `${diffMins}m ago`
  const diffHours = Math.floor(diffMins / 60)
  if (diffHours < 24) return `${diffHours}h ago`
  const diffDays = Math.floor(diffHours / 24)
  return `${diffDays}d ago`
}

// Copy to clipboard helper with fallback
function copyToClipboard(text: string, label: string) {
  if (!navigator.clipboard) {
    // Fallback for older browsers or insecure contexts
    try {
      const textArea = document.createElement('textarea')
      textArea.value = text
      textArea.style.position = 'fixed'
      textArea.style.opacity = '0'
      document.body.appendChild(textArea)
      textArea.select()
      document.execCommand('copy')
      document.body.removeChild(textArea)
      toast.success(`${label} copied to clipboard`)
    } catch {
      toast.error('Copy not supported in this browser')
    }
    return
  }

  navigator.clipboard
    .writeText(text)
    .then(() => {
      toast.success(`${label} copied to clipboard`)
    })
    .catch(() => {
      toast.error('Failed to copy to clipboard')
    })
}

// Individual step display component
const StepDisplay: React.FC<StepDisplayProps> = React.memo(
  ({ step, isExpanded, onToggle }) => {
    const typeInfo = getStepTypeInfo(step.type)
    const Icon = typeInfo.icon
    const timeAgo = getTimeAgo(step.createdAt)

    // Parse metadata based on step type
    const renderStepContent = () => {
      const metadata = step.metadata || {}

      switch (step.type) {
        case AiStepType.THOUGHT:
          const thoughtMeta = getTypedStepMetadata<ThoughtStepMetadata>(
            metadata,
            'THOUGHT'
          )
          return (
            <div className="space-y-3">
              {thoughtMeta?.reasoning && (
                <div className="bg-blue-50 p-3 rounded-lg border-l-4 border-blue-200">
                  <div className="font-medium text-blue-800 mb-2 text-sm">
                    🧠 AI Reasoning:
                  </div>
                  <div className="text-sm text-gray-700 leading-relaxed italic">
                    "{thoughtMeta.reasoning}"
                  </div>

                  {/* Additional thought metadata */}
                  <div className="mt-2 flex flex-wrap gap-2">
                    {thoughtMeta.complexity && (
                      <span
                        className={cn(
                          'text-xs px-2 py-1 rounded-full',
                          thoughtMeta.complexity === 'simple'
                            ? 'bg-green-100 text-green-700'
                            : thoughtMeta.complexity === 'moderate'
                              ? 'bg-yellow-100 text-yellow-700'
                              : 'bg-red-100 text-red-700'
                        )}
                      >
                        {thoughtMeta.complexity} reasoning
                      </span>
                    )}
                    {thoughtMeta.category && (
                      <span className="text-xs px-2 py-1 bg-blue-100 text-blue-700 rounded-full">
                        {thoughtMeta.category}
                      </span>
                    )}
                    {thoughtMeta.thinkingTime && (
                      <span className="text-xs px-2 py-1 bg-gray-100 text-gray-600 rounded-full">
                        {thoughtMeta.thinkingTime}s thinking
                      </span>
                    )}
                  </div>
                </div>
              )}
              {metadata.timestamp && (
                <div className="text-xs text-gray-500">
                  <FiClock className="inline w-3 h-3 mr-1" />
                  {new Date(metadata.timestamp).toLocaleTimeString()}
                </div>
              )}
            </div>
          )

        case AiStepType.TOOL_CALL:
          const toolCallMeta = getTypedStepMetadata<ToolCallStepMetadata>(
            metadata,
            'TOOL_CALL'
          )
          const toolName =
            toolCallMeta?.toolName || metadata.toolName || 'unknown'
          const args = toolCallMeta?.args || metadata.args || {}

          return (
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                {toolName === 'web_search' && (
                  <FiSearch className="w-4 h-4 text-blue-600" />
                )}
                <span className="font-medium text-purple-700">
                  🔧 Calling Tool:
                </span>
                <code className="bg-purple-100 px-2 py-1 rounded text-purple-800 font-semibold">
                  {toolName}
                </code>
                {toolCallMeta?.priority && (
                  <span
                    className={cn(
                      'text-xs px-2 py-1 rounded-full',
                      toolCallMeta.priority === 'high'
                        ? 'bg-red-100 text-red-700'
                        : toolCallMeta.priority === 'medium'
                          ? 'bg-yellow-100 text-yellow-700'
                          : 'bg-green-100 text-green-700'
                    )}
                  >
                    {toolCallMeta.priority} priority
                  </span>
                )}
                {metadata.timestamp && (
                  <div className="text-xs text-gray-500 ml-auto">
                    <FiClock className="inline w-3 h-3 mr-1" />
                    {new Date(metadata.timestamp).toLocaleTimeString()}
                  </div>
                )}
              </div>

              {/* Enhanced argument display */}
              {Object.keys(args).length > 0 && (
                <div className="bg-purple-50 p-3 rounded-lg border border-purple-200">
                  <div className="font-medium text-purple-800 mb-2 text-sm">
                    📋 Parameters:
                  </div>
                  {toolName === 'web_search' && args.query ? (
                    <div className="space-y-2">
                      <div className="text-sm">
                        <span className="font-medium text-gray-700">
                          Search Query:
                        </span>
                        <div className="mt-1 bg-white p-2 rounded border text-gray-800 font-medium">
                          "{args.query}"
                        </div>
                      </div>
                      {args.maxResults && (
                        <div className="text-xs text-gray-600">
                          Max Results: {args.maxResults}
                        </div>
                      )}
                      {toolCallMeta?.expectedResultType && (
                        <div className="text-xs text-gray-600">
                          Expected: {toolCallMeta.expectedResultType}
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="text-sm space-y-1">
                      {Object.entries(args).map(([key, value]) => (
                        <div key={key} className="flex items-start gap-2">
                          <span className="text-gray-500 min-w-0 shrink-0 font-medium">
                            {key}:
                          </span>
                          <span className="text-gray-700 break-words">
                            {String(value)}
                          </span>
                        </div>
                      ))}
                    </div>
                  )}

                  {/* Retry information */}
                  {toolCallMeta?.retryCount && toolCallMeta.retryCount > 0 && (
                    <div className="mt-2 text-xs text-orange-600">
                      ⚠️ Retry attempt #{toolCallMeta.retryCount}
                    </div>
                  )}
                </div>
              )}
            </div>
          )

        case AiStepType.TOOL_RESULT:
          const toolResultMeta = getTypedStepMetadata<ToolResultStepMetadata>(
            metadata,
            'TOOL_RESULT'
          )
          const result = toolResultMeta?.result || metadata.result
          const error = toolResultMeta?.error || metadata.error
          const resultToolName =
            toolResultMeta?.toolName || metadata.toolName || 'unknown'
          const success = toolResultMeta?.success ?? !error

          return (
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <span className="font-medium text-green-700">Tool Result:</span>
                <code className="bg-green-100 px-2 py-1 rounded text-green-800 font-semibold">
                  {resultToolName}
                </code>
                {metadata.timestamp && (
                  <div className="text-xs text-gray-500 ml-auto">
                    <FiClock className="inline w-3 h-3 mr-1" />
                    {new Date(metadata.timestamp).toLocaleTimeString()}
                  </div>
                )}
              </div>

              {error ? (
                <div className="bg-red-50 p-3 rounded-lg border border-red-200">
                  <div className="text-sm text-red-600 font-medium">
                    ❌ Error: {error}
                  </div>
                </div>
              ) : result ? (
                <div className="bg-green-50 p-3 rounded-lg border border-green-200">
                  <div className="font-medium text-green-800 mb-2 text-sm">
                    ✅ Success - Information Retrieved:
                  </div>

                  {/* Enhanced result display for web search */}
                  {resultToolName === 'web_search' &&
                  typeof result === 'object' &&
                  result.query ? (
                    <div className="space-y-3">
                      <div className="text-sm">
                        <span className="font-medium text-gray-700">
                          Search Query:
                        </span>
                        <div className="mt-1 bg-white p-2 rounded border text-gray-800">
                          "{result.query}"
                        </div>
                      </div>
                      {result.resultCount && (
                        <div className="text-sm">
                          <span className="font-medium text-gray-700">
                            Results Found:
                          </span>
                          <span className="ml-2 text-gray-800">
                            {result.resultCount} results
                          </span>
                        </div>
                      )}
                      {result.summary && (
                        <div className="text-sm">
                          <span className="font-medium text-gray-700">
                            Content Summary:
                          </span>
                          <div className="mt-1 bg-white p-2 rounded border text-gray-800 max-h-32 overflow-y-auto">
                            {result.summary}
                          </div>
                        </div>
                      )}
                    </div>
                  ) : typeof result === 'object' ? (
                    <div className="text-sm space-y-1">
                      {Object.entries(result).map(([key, value]) => (
                        <div key={key} className="flex items-start gap-2">
                          <span className="text-gray-500 min-w-0 shrink-0 font-medium">
                            {key}:
                          </span>
                          <span className="text-gray-700 break-words">
                            {String(value)}
                          </span>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-sm text-gray-700 max-h-32 overflow-y-auto bg-white p-2 rounded border">
                      {String(result)}
                    </div>
                  )}
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() =>
                      copyToClipboard(
                        typeof result === 'object'
                          ? JSON.stringify(result, null, 2)
                          : String(result),
                        'Tool result'
                      )
                    }
                    className="h-6 text-xs"
                  >
                    <FiCopy className="w-3 h-3 mr-1" />
                    Copy
                  </Button>
                </div>
              ) : (
                <div className="text-sm text-gray-500 italic">
                  No result data
                </div>
              )}
            </div>
          )

        case AiStepType.REASONING_SUMMARY:
          const reasoningSummaryMeta =
            getTypedStepMetadata<ReasoningSummaryStepMetadata>(
              metadata,
              'REASONING_SUMMARY'
            )
          return (
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <span className="font-medium text-indigo-700">
                  🎯 AI Summary:
                </span>
                {reasoningSummaryMeta?.confidenceLevel && (
                  <span
                    className={cn(
                      'text-xs px-2 py-1 rounded-full',
                      reasoningSummaryMeta.confidenceLevel >= 0.8
                        ? 'bg-green-100 text-green-700'
                        : reasoningSummaryMeta.confidenceLevel >= 0.6
                          ? 'bg-yellow-100 text-yellow-700'
                          : 'bg-red-100 text-red-700'
                    )}
                  >
                    {Math.round(reasoningSummaryMeta.confidenceLevel * 100)}%
                    confident
                  </span>
                )}
                {metadata.timestamp && (
                  <div className="text-xs text-gray-500 ml-auto">
                    <FiClock className="inline w-3 h-3 mr-1" />
                    {new Date(metadata.timestamp).toLocaleTimeString()}
                  </div>
                )}
              </div>

              {(reasoningSummaryMeta?.summary || metadata.summary) && (
                <div className="bg-indigo-50 p-3 rounded-lg border border-indigo-200">
                  <div className="font-medium text-indigo-800 mb-2 text-sm">
                    🧠 AI's Reasoning Summary:
                  </div>
                  <div className="text-sm text-gray-700 leading-relaxed">
                    {reasoningSummaryMeta?.summary || metadata.summary}
                  </div>

                  {/* Enhanced reasoning metadata */}
                  {reasoningSummaryMeta?.keyDecisions &&
                    reasoningSummaryMeta.keyDecisions.length > 0 && (
                      <div className="mt-3">
                        <div className="font-medium text-indigo-700 mb-1 text-xs">
                          Key Decisions:
                        </div>
                        <ul className="list-disc list-inside text-xs text-gray-600 space-y-1">
                          {reasoningSummaryMeta.keyDecisions.map(
                            (decision, idx) => (
                              <li key={idx}>{decision}</li>
                            )
                          )}
                        </ul>
                      </div>
                    )}

                  {reasoningSummaryMeta?.alternativesConsidered &&
                    reasoningSummaryMeta.alternativesConsidered.length > 0 && (
                      <div className="mt-3">
                        <div className="font-medium text-indigo-700 mb-1 text-xs">
                          Alternatives Considered:
                        </div>
                        <ul className="list-disc list-inside text-xs text-gray-600 space-y-1">
                          {reasoningSummaryMeta.alternativesConsidered.map(
                            (alt, idx) => (
                              <li key={idx}>{alt}</li>
                            )
                          )}
                        </ul>
                      </div>
                    )}
                </div>
              )}
            </div>
          )

        default:
          return (
            <div className="text-sm text-gray-600">
              <pre className="whitespace-pre-wrap overflow-x-auto">
                {JSON.stringify(metadata, null, 2)}
              </pre>
            </div>
          )
      }
    }

    return (
      <div
        className={cn(
          'border rounded-lg transition-all duration-200',
          typeInfo.borderColor,
          typeInfo.bgColor
        )}
      >
        <button
          onClick={onToggle}
          className="w-full p-2 flex items-center gap-2 text-left hover:bg-gray-50 transition-colors"
        >
          <div className="flex items-center gap-2 flex-1">
            <Icon className={cn('w-3 h-3', typeInfo.color)} />
            {step.type === AiStepType.TOOL_CALL ? (
              <span className="text-sm text-gray-700">
                {(() => {
                  const toolName = step.metadata?.toolName || 'unknown'
                  const args = step.metadata?.args || {}
                  const keyArg =
                    args.query ||
                    args.url ||
                    args.input ||
                    args.text ||
                    Object.values(args)[0]
                  return keyArg ? `${toolName}: ${keyArg}` : toolName
                })()}
              </span>
            ) : (
              <span className={cn('text-sm', typeInfo.color)}>
                {typeInfo.label}
              </span>
            )}
          </div>
          <div className="flex items-center gap-1">
            {isExpanded ? (
              <FiChevronDown className="w-3 h-3 text-gray-400" />
            ) : (
              <FiChevronRight className="w-3 h-3 text-gray-400" />
            )}
          </div>
        </button>

        {isExpanded && (
          <div className="px-3 pb-3 border-t border-gray-200">
            <div className="pt-3">{renderStepContent()}</div>
          </div>
        )}
      </div>
    )
  },
  (prevProps, nextProps) => {
    // Custom comparison for StepDisplay memo
    return (
      prevProps.step.id === nextProps.step.id &&
      prevProps.step.updatedAt === nextProps.step.updatedAt &&
      prevProps.isExpanded === nextProps.isExpanded
    )
  }
)

// Main ExecutionStepViewer component
export const ExecutionStepViewer: React.FC<ExecutionStepViewerProps> =
  React.memo(
    ({ steps, isLoading = false, className }) => {
      const [expandedSteps, setExpandedSteps] = useState<Set<string>>(new Set())
      const [showAll, setShowAll] = useState<boolean>(false)

      const toggleStep = (stepId: string) => {
        const newExpanded = new Set(expandedSteps)
        if (newExpanded.has(stepId)) {
          newExpanded.delete(stepId)
        } else {
          newExpanded.add(stepId)
        }
        setExpandedSteps(newExpanded)
      }

      const toggleAllSteps = () => {
        if (expandedSteps.size === steps.length) {
          setExpandedSteps(new Set())
        } else {
          setExpandedSteps(new Set(steps.map(step => step.id)))
        }
      }

      if (isLoading) {
        return (
          <div className={cn('space-y-2', className)}>
            <div className="animate-pulse">
              <div className="h-12 bg-gray-200 rounded-lg"></div>
            </div>
          </div>
        )
      }

      if (steps.length === 0) {
        return (
          <div
            className={cn(
              'text-sm text-gray-500 italic text-center py-4',
              className
            )}
          >
            No execution steps available
          </div>
        )
      }

      // Filter out tool results and sort by order (only show tool calls and thoughts)
      const filteredSteps = steps.filter(
        step => step.type !== AiStepType.TOOL_RESULT
      )
      const sortedSteps = [...filteredSteps].sort(
        (a, b) => a.stepOrder - b.stepOrder
      )

      // Show only first 3 steps initially unless showAll is true
      const displaySteps = showAll ? sortedSteps : sortedSteps.slice(0, 3)
      const hasMore = sortedSteps.length > 3

      return (
        <div className={cn('space-y-2', className)}>
          {/* Steps list - no header, more compact */}
          <div className="space-y-1">
            {displaySteps.map(step => (
              <StepDisplay
                key={step.id}
                step={step}
                isExpanded={expandedSteps.has(step.id)}
                onToggle={() => toggleStep(step.id)}
              />
            ))}
          </div>

          {/* Show more button */}
          {hasMore && !showAll && (
            <div className="text-center">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowAll(true)}
                className="h-6 text-xs text-gray-500 hover:text-gray-700"
              >
                Show {sortedSteps.length - 3} more steps
              </Button>
            </div>
          )}

          {hasMore && showAll && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowAll(false)}
              className="w-full h-8 text-xs text-gray-600"
            >
              Show less
            </Button>
          )}
        </div>
      )
    },
    (prevProps, nextProps) => {
      // Custom comparison for ExecutionStepViewer memo
      if (prevProps.isLoading !== nextProps.isLoading) return false
      if (prevProps.className !== nextProps.className) return false
      if (prevProps.steps.length !== nextProps.steps.length) return false

      // Deep comparison of steps (only check essential fields)
      for (let i = 0; i < prevProps.steps.length; i++) {
        const prevStep = prevProps.steps[i]
        const nextStep = nextProps.steps[i]
        if (
          prevStep.id !== nextStep.id ||
          prevStep.updatedAt !== nextStep.updatedAt ||
          prevStep.stepOrder !== nextStep.stepOrder
        ) {
          return false
        }
      }

      return true
    }
  )

export default ExecutionStepViewer

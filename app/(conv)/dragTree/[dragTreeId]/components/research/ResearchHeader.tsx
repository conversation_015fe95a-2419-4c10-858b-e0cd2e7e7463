'use client'

import React from 'react'
import { cn } from '@/lib/utils'
import { useNavigationStore } from '@/app/stores/navigation_store'
import { useDragTreeStore } from '@/app/stores/dragtree_store'
import {
  SourceCitations,
  hasSearchResults,
} from '@/app/(conv)/dragTree/[dragTreeId]/components/SearchResultsDisplay'
import { TreeNode } from '@/app/types'

export interface ResearchHeaderProps {
  nodeId: string
  contentId: string
  fullTitle: string
  variant?: 'default' | 'compact' | 'tab'
  showNavigation?: boolean
  showSearchResults?: boolean
  className?: string
}

// Utility function to find a category node by matching its label in the tree
const findCategoryNodeByLabel = (
  tree: TreeNode | null,
  categoryLabel: string
): TreeNode | null => {
  if (!tree) return null

  // Check if current node is a category with matching label
  if (tree.type === 'category' && tree.label === categoryLabel) {
    return tree
  }

  // Recursively search in children
  for (const child of tree.children) {
    const found = findCategoryNodeByLabel(child, categoryLabel)
    if (found) return found
  }

  return null
}

/**
 * Reusable research header component
 * Displays title, category path, and search results
 * Can be used in both main research display and tab views
 */
export const ResearchHeader: React.FC<ResearchHeaderProps> = ({
  nodeId,
  contentId,
  fullTitle,
  variant = 'default',
  showNavigation = true,
  showSearchResults = true,
  className,
}) => {
  const { navigateToTreeNodeFromReactFlow } = useNavigationStore()
  // Optimized: Use individual selectors to prevent unnecessary re-renders
  const getNodeContent = useDragTreeStore(state => state.getNodeContent)
  const frontendTreeStructure = useDragTreeStore(
    state => state.frontendTreeStructure
  )

  // Parse the full title to separate category path from question
  const parseTitle = (fullTitle: string) => {
    const parts = fullTitle.split(' > ')
    if (parts.length > 1) {
      const questionPart = parts[parts.length - 1]
      const categoryPath = parts.slice(0, -1).join(' > ')
      const categoryParts = parts.slice(0, -1) // Individual category labels
      return { categoryPath, questionPart, categoryParts }
    }
    return { categoryPath: '', questionPart: fullTitle, categoryParts: [] }
  }

  const { categoryPath, questionPart, categoryParts } = parseTitle(fullTitle)

  // Get search results for this content
  const nodeContentMap = getNodeContent(nodeId)
  const storedNodeContent = nodeContentMap?.get(contentId)
  const searchResults =
    storedNodeContent?.metadata && hasSearchResults(storedNodeContent.metadata)
      ? storedNodeContent.metadata.searchResults
      : null

  const handleNavigateToNode = () => {
    if (showNavigation && nodeId) {
      navigateToTreeNodeFromReactFlow(nodeId)
    }
  }

  const handleNavigateToCategory = (categoryLabel: string) => {
    if (!showNavigation || !frontendTreeStructure) return

    const categoryNode = findCategoryNodeByLabel(
      frontendTreeStructure,
      categoryLabel
    )
    if (categoryNode) {
      navigateToTreeNodeFromReactFlow(categoryNode.id)
    }
  }

  // Get variant-specific styles
  const getVariantStyles = () => {
    switch (variant) {
      case 'compact':
        return {
          categoryText: 'text-xs text-slate-500 mb-1',
          questionText: 'text-sm font-medium text-slate-700',
          container: 'mb-2',
        }
      case 'tab':
        return {
          categoryText: 'text-xs text-slate-500 mb-1',
          questionText: 'text-sm font-medium text-slate-700',
          container: 'mb-4',
        }
      default:
        return {
          categoryText: 'text-sm text-slate-500 mb-1',
          questionText: 'text-lg font-semibold text-slate-800',
          container: 'mb-4',
        }
    }
  }

  const styles = getVariantStyles()

  return (
    <div className={cn(styles.container, className)}>
      {/* Category Path - clickable parts */}
      {categoryPath && (
        <div
          className={cn(
            styles.categoryText,
            'flex items-center gap-1 flex-wrap'
          )}
        >
          {categoryParts.map((categoryLabel, index) => (
            <React.Fragment key={index}>
              <button
                onClick={() => handleNavigateToCategory(categoryLabel)}
                className={cn(
                  'hover:text-blue-600 hover:underline transition-colors cursor-pointer',
                  showNavigation ? 'text-slate-600' : 'text-slate-500'
                )}
                title={
                  showNavigation
                    ? `Navigate to "${categoryLabel}" category`
                    : categoryLabel
                }
              >
                {categoryLabel}
              </button>
              {index < categoryParts.length - 1 && (
                <span className="text-slate-400">›</span>
              )}
            </React.Fragment>
          ))}
        </div>
      )}

      {/* Question Title with inline source icons */}
      <div className="flex items-start gap-2 flex-wrap">
        <h2
          className={cn(
            styles.questionText,
            'flex-1 min-w-0 leading-relaxed break-words',
            showNavigation &&
              'cursor-pointer hover:text-blue-600 transition-colors'
          )}
          title={
            showNavigation
              ? 'Click to navigate to this node in the outline'
              : fullTitle
          }
          onClick={showNavigation ? handleNavigateToNode : undefined}
          style={{
            wordWrap: 'break-word',
            overflowWrap: 'break-word',
            whiteSpace: 'normal',
          }}
        >
          {questionPart}
        </h2>

        {/* Source Citations inline with question */}
        {showSearchResults && searchResults && searchResults.length > 0 && (
          <div className="flex-shrink-0">
            <SourceCitations
              searchResults={searchResults}
              maxSources={searchResults.length}
              className="text-xs"
            />
          </div>
        )}
      </div>
    </div>
  )
}

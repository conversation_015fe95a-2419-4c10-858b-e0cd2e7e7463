'use client'

import React, { useState, useEffect, useMemo, useCallback } from 'react'
import { cn } from '@/lib/utils'
import { useDragTreeStore } from '@/app/stores/dragtree_store'
import { useBatchResearchStore } from '@/app/stores/batchResearchStore'
import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import {
  FiFolder,
  FiFile,
  FiCheck,
  FiX,
  FiChevronDown,
  FiChevronRight,
  FiSearch,
} from 'react-icons/fi'
import { CheckCircle, Clock } from 'lucide-react'
import type { BatchResearchItem } from '@/app/stores/batchResearchStore'

type TreeSelectionPanelProps = {
  dragTreeId: string
  items: BatchResearchItem[]
  onCoachModeToggle: () => void
  isCoachModeOpen: boolean
  currentQuestionId?: string // Add current question ID for highlighting
  className?: string
}

const TreeSelectionPanel: React.FC<TreeSelectionPanelProps> = ({
  dragTreeId,
  items,
  onCoachModeToggle,
  isCoachModeOpen,
  currentQuestionId,
  className,
}) => {
  const frontendTreeStructure = useDragTreeStore(
    state => state.frontendTreeStructure
  )
  const nodeContent = useDragTreeStore(state => state.nodeContent)
  const {
    getSelections,
    getExpandedNodes,
    toggleSelection,
    toggleExpanded,
    clearSelections,
    setExpandedNodes,
  } = useBatchResearchStore()

  const [searchTerm, setSearchTerm] = useState<string>('')
  const [showResearchableOnly, setShowResearchableOnly] =
    useState<boolean>(false)

  const selectedItems = getSelections(dragTreeId)
  const expandedNodes = getExpandedNodes(dragTreeId)

  // Auto-expand all nodes on first load
  useEffect(() => {
    if (frontendTreeStructure && expandedNodes.size === 0) {
      const allNodeIds = new Set<string>()

      const collectNodeIds = (node: any) => {
        if (node?.id) {
          allNodeIds.add(node.id)
        }
        if (node?.children && Array.isArray(node.children)) {
          node.children.forEach(collectNodeIds)
        }
      }

      collectNodeIds(frontendTreeStructure)
      setExpandedNodes(dragTreeId, allNodeIds)
    }
  }, [frontendTreeStructure, expandedNodes.size, dragTreeId, setExpandedNodes])

  // Auto-expand to show current question context
  useEffect(() => {
    if (currentQuestionId && frontendTreeStructure && isCoachModeOpen) {
      const pathToQuestion = findPathToNode(
        frontendTreeStructure,
        currentQuestionId
      )
      if (pathToQuestion.length > 0) {
        const nodesToExpand = new Set(expandedNodes)
        let hasNewNodes = false
        pathToQuestion.forEach(nodeId => {
          if (!nodesToExpand.has(nodeId)) {
            nodesToExpand.add(nodeId)
            hasNewNodes = true
          }
        })
        // Only update if we actually have new nodes to prevent infinite loops
        if (hasNewNodes) {
          setExpandedNodes(dragTreeId, nodesToExpand)
        }
      }
    }
  }, [currentQuestionId, frontendTreeStructure, dragTreeId, isCoachModeOpen]) // Remove expandedNodes from deps to prevent loops

  // Find path to a specific node
  const findPathToNode = useCallback(
    (node: any, targetId: string, path: string[] = []): string[] => {
      if (node.id === targetId) {
        return [...path, node.id]
      }
      if (node.children) {
        for (const child of node.children) {
          const result = findPathToNode(child, targetId, [...path, node.id])
          if (result.length > 0) return result
        }
      }
      return []
    },
    []
  )

  // Get researchable items count
  const researchableItems = useMemo(() => {
    return items.filter(item => !item.hasExistingResearch)
  }, [items])

  // Get all descendant IDs recursively for category selection
  const getAllDescendantIds = useCallback(
    (nodeId: string): string[] => {
      if (!frontendTreeStructure) return []

      const findNode = (node: any, targetId: string): any => {
        if (node.id === targetId) return node
        if (node.children) {
          for (const child of node.children) {
            const found = findNode(child, targetId)
            if (found) return found
          }
        }
        return null
      }

      const collectDescendants = (node: any): string[] => {
        const descendants: string[] = []
        if (node.children) {
          for (const child of node.children) {
            // Only include nodes that are in our items list
            if (items.some(item => item.nodeId === child.id)) {
              descendants.push(child.id)
            }
            descendants.push(...collectDescendants(child))
          }
        }
        return descendants
      }

      const targetNode = findNode(frontendTreeStructure, nodeId)
      return targetNode ? collectDescendants(targetNode) : []
    },
    [frontendTreeStructure, items]
  )

  // Handle node selection with recursive selection for categories
  const handleNodeToggle = useCallback(
    (nodeId: string) => {
      // Prevent interaction during coach mode
      if (isCoachModeOpen) return

      const targetItem = items.find(item => item.nodeId === nodeId)

      if (targetItem && !targetItem.hasExistingResearch) {
        // If it's a researchable question, just toggle it
        toggleSelection(dragTreeId, nodeId)
      } else {
        // If it's a category, toggle all descendants that are researchable
        const descendantIds = getAllDescendantIds(nodeId)
        const researchableDescendants = descendantIds.filter(id =>
          researchableItems.some(item => item.nodeId === id)
        )

        // Check if all researchable descendants are selected
        const allSelected = researchableDescendants.every(id =>
          selectedItems.has(id)
        )

        // Toggle all researchable descendants
        researchableDescendants.forEach(id => {
          if (allSelected) {
            // If all are selected, deselect all
            if (selectedItems.has(id)) {
              toggleSelection(dragTreeId, id)
            }
          } else {
            // If not all are selected, select all
            if (!selectedItems.has(id)) {
              toggleSelection(dragTreeId, id)
            }
          }
        })
      }
    },
    [
      items,
      researchableItems,
      selectedItems,
      dragTreeId,
      toggleSelection,
      getAllDescendantIds,
      isCoachModeOpen,
    ]
  )

  const selectedCount = selectedItems.size
  const totalResearchableCount = researchableItems.length

  // Scroll to current question node when it changes
  useEffect(() => {
    if (!currentQuestionId) return

    const targetElement = document.querySelector(
      `[data-node-id="${currentQuestionId}"]`
    ) as HTMLElement | null
    if (targetElement) {
      targetElement.scrollIntoView({ behavior: 'smooth', block: 'center' })
    }
  }, [currentQuestionId])

  return (
    <div className={cn('flex flex-col h-full min-h-0 relative', className)}>
      {/* Blur overlay when coach mode is active */}
      {isCoachModeOpen && (
        <div className="absolute inset-0 bg-white/20 z-10 pointer-events-none" />
      )}

      {/* Compact Header with Search */}
      <div className="p-4 border-b space-y-3">
        {/* Top row: Title and Search */}
        <div className="flex items-center gap-4">
          <div className="flex-1">
            <h3 className="text-lg font-semibold">
              Select Questions for Research
            </h3>
            <p className="text-sm text-gray-600">
              {selectedCount} of {totalResearchableCount} selected
              {isCoachModeOpen && (
                <span className="ml-2 text-blue-600 font-medium">
                  • Coach Mode Active
                </span>
              )}
            </p>
          </div>

          {/* Search input */}
          <div className="relative flex-1 max-w-md">
            <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Search questions and categories..."
              value={searchTerm}
              onChange={e => setSearchTerm(e.target.value)}
              disabled={isCoachModeOpen}
              className={cn(
                'w-full pl-9 pr-3 py-2 text-sm border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all duration-200',
                isCoachModeOpen && 'cursor-not-allowed bg-gray-50'
              )}
            />
          </div>
        </div>
        {/*
          COACH MODE TOGGLE BUTTON - TEMPORARILY DISABLED

          The coach mode feature (tinder-like card interface) has been implemented
          but is temporarily disabled as it doesn't significantly help users scan
          questions faster than the current tree interface.

          The implementation includes:
          - Carousel-based tinder-like interface with swipe gestures
          - Heart/X buttons for interested/skip actions
          - Improved counting logic (selected vs processed vs remaining)
          - Keyboard shortcuts and animations
          - Side-by-side layout with tree view

          We may revisit this feature in the future based on user feedback.
          To re-enable, uncomment the button below and ensure all coach mode
          logic in BatchResearchDialog.tsx is active.
        */}
        {/*
        <Button
          onClick={onCoachModeToggle}
          variant={isCoachModeOpen ? 'default' : 'outline'}
          size="sm"
          className={cn(
            'flex items-center gap-2 transition-all duration-200',
            isCoachModeOpen && 'ring-2 ring-blue-200 shadow-lg'
          )}
        >
          <FiTarget className="w-4 h-4" />
          {isCoachModeOpen ? 'Close Coach' : 'Open Coach Mode'}
        </Button>
        */}

        {/* Bottom row: Quick actions and filter controls */}
        <div
          className={cn(
            'flex items-center justify-between transition-opacity duration-200',
            isCoachModeOpen && 'opacity-50'
          )}
        >
          <div className="flex space-x-2">
            <Button
              onClick={() => {
                if (isCoachModeOpen) return
                const researchableIds = researchableItems.map(
                  item => item.nodeId
                )
                researchableIds.forEach(id => {
                  if (!selectedItems.has(id)) {
                    toggleSelection(dragTreeId, id)
                  }
                })
              }}
              variant="outline"
              size="sm"
              disabled={isCoachModeOpen}
              className={cn(
                'transition-all duration-200',
                isCoachModeOpen && 'opacity-50 cursor-not-allowed'
              )}
            >
              <FiCheck className="w-3 h-3 mr-1" />
              Select All
            </Button>
            <Button
              onClick={() => {
                if (isCoachModeOpen) return
                clearSelections(dragTreeId)
              }}
              variant="outline"
              size="sm"
              disabled={isCoachModeOpen}
              className={cn(
                'transition-all duration-200',
                isCoachModeOpen && 'opacity-50 cursor-not-allowed'
              )}
            >
              <FiX className="w-3 h-3 mr-1" />
              Clear All
            </Button>

            {/* Show Researchable Only Filter */}
            <Button
              onClick={() => setShowResearchableOnly(!showResearchableOnly)}
              variant={showResearchableOnly ? 'default' : 'outline'}
              size="sm"
              disabled={isCoachModeOpen}
              className={cn(
                'transition-all duration-200',
                isCoachModeOpen && 'opacity-50 cursor-not-allowed',
                showResearchableOnly &&
                  'bg-green-600 hover:bg-green-700 text-white'
              )}
            >
              <FiSearch className="w-3 h-3 mr-1" />
              {showResearchableOnly ? 'Show All' : 'Show Researchable'}
            </Button>
          </div>

          <div className="flex items-center gap-3">
            {showResearchableOnly && (
              <span className="text-sm text-green-600 font-medium">
                Filtering: researchable only
              </span>
            )}
            {selectedCount > 0 && (
              <span className="text-sm text-blue-600 font-medium">
                {selectedCount} selected
              </span>
            )}
          </div>
        </div>
      </div>

      {/* Tree view - FIXED SCROLLING */}
      <div
        className={cn(
          'flex-1 min-h-0 overflow-y-auto overflow-x-hidden p-4 transition-all duration-200',
          isCoachModeOpen && 'blur-sm opacity-60'
        )}
      >
        {frontendTreeStructure ? (
          <RecursiveBatchTreeView
            node={frontendTreeStructure}
            items={items}
            expandedNodes={expandedNodes}
            selectedItems={selectedItems}
            onNodeToggle={handleNodeToggle}
            onExpandToggle={(nodeId: string) => {
              if (isCoachModeOpen) return
              toggleExpanded(dragTreeId, nodeId)
            }}
            searchTerm={searchTerm}
            currentQuestionId={currentQuestionId}
            isCoachModeActive={isCoachModeOpen}
            showResearchableOnly={showResearchableOnly}
          />
        ) : (
          <div className="text-center py-8 text-gray-500">
            <Clock className="w-12 h-12 mx-auto mb-4 text-gray-300" />
            <p className="text-lg font-medium">Loading tree structure...</p>
          </div>
        )}
      </div>
    </div>
  )
}

// Recursive tree component for batch research
type RecursiveBatchTreeViewProps = {
  node: any
  items: BatchResearchItem[]
  expandedNodes: Set<string>
  selectedItems: Set<string>
  onNodeToggle: (nodeId: string) => void
  onExpandToggle: (nodeId: string) => void
  searchTerm: string
  currentQuestionId?: string
  isCoachModeActive: boolean
  showResearchableOnly: boolean
  level?: number
}

const RecursiveBatchTreeView: React.FC<RecursiveBatchTreeViewProps> = ({
  node,
  items,
  expandedNodes,
  selectedItems,
  onNodeToggle,
  onExpandToggle,
  searchTerm,
  currentQuestionId,
  isCoachModeActive,
  showResearchableOnly,
  level = 0,
}) => {
  if (!node) return null

  const nodeItem = items.find(item => item.nodeId === node.id)
  const isCategory = node.type === 'category'
  const isSelected = selectedItems.has(node.id)
  const isExpanded = expandedNodes.has(node.id)
  const hasChildren = node.children && node.children.length > 0
  const isCurrentQuestion = currentQuestionId === node.id

  // For categories, get stats about children
  const getChildrenStats = (
    node: any
  ): { total: number; researchable: number; selected: number } => {
    if (!node.children) return { total: 0, researchable: 0, selected: 0 }

    let total = 0
    let researchable = 0
    let selected = 0

    const collectStats = (n: any) => {
      const item = items.find(item => item.nodeId === n.id)
      if (item) {
        total++
        if (!item.hasExistingResearch) {
          researchable++
          if (selectedItems.has(n.id)) {
            selected++
          }
        }
      }
      if (n.children) {
        n.children.forEach(collectStats)
      }
    }

    node.children.forEach(collectStats)
    return { total, researchable, selected }
  }

  const childrenStats = isCategory
    ? getChildrenStats(node)
    : { total: 0, researchable: 0, selected: 0 }
  const isCategoryFullySelected =
    isCategory &&
    childrenStats.researchable > 0 &&
    childrenStats.selected === childrenStats.researchable
  const isCategoryPartiallySelected =
    isCategory &&
    childrenStats.selected > 0 &&
    childrenStats.selected < childrenStats.researchable

  // Filter based on search
  const matchesSearch =
    !searchTerm ||
    (node.label || '').toLowerCase().includes(searchTerm.toLowerCase())

  // Check if any descendant matches search
  const hasMatchingDescendant = (n: any): boolean => {
    if (!n.children) return false
    return n.children.some(
      (child: any) =>
        (child.label || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
        hasMatchingDescendant(child)
    )
  }

  const shouldShow = matchesSearch || hasMatchingDescendant(node)

  if (!shouldShow && searchTerm) return null

  // Filter for researchable only mode
  if (showResearchableOnly) {
    const hasResearchableChild = (n: any): boolean => {
      const childItem = items.find(item => item.nodeId === n.id)
      if (childItem && !childItem.hasExistingResearch) {
        return true
      }
      if (n.children) {
        return n.children.some(hasResearchableChild)
      }
      return false
    }

    // Show node if it's researchable itself or has researchable descendants
    const isNodeResearchable = nodeItem && !nodeItem.hasExistingResearch
    const hasResearchableDescendants = hasResearchableChild(node)

    if (!isNodeResearchable && !hasResearchableDescendants) {
      return null
    }
  }

  const paddingLeft = level * 20

  return (
    <div style={{ paddingLeft }}>
      <div
        className={cn(
          'flex items-center space-x-2 p-2 rounded-md transition-all duration-200',
          // Highlight current question when in coach mode
          isCurrentQuestion &&
            isCoachModeActive &&
            'bg-blue-100 border-2 border-blue-300 shadow-md scale-105',
          // Regular hover effects when not coach mode active
          !isCoachModeActive && 'hover:bg-gray-50',
          // Reduce interactivity when coach mode is active
          isCoachModeActive && !isCurrentQuestion && 'opacity-60'
        )}
      >
        {/* Expand/collapse button */}
        {hasChildren && (
          <button
            onClick={() => onExpandToggle(node.id)}
            disabled={isCoachModeActive}
            className={cn(
              'p-1 rounded transition-all duration-200',
              isCoachModeActive
                ? 'cursor-not-allowed opacity-50'
                : 'hover:bg-gray-200'
            )}
          >
            {isExpanded ? (
              <FiChevronDown className="w-3 h-3 text-gray-600" />
            ) : (
              <FiChevronRight className="w-3 h-3 text-gray-600" />
            )}
          </button>
        )}

        {/* Selection checkbox */}
        {(nodeItem && !nodeItem.hasExistingResearch) ||
        (isCategory && childrenStats.researchable > 0) ? (
          <Checkbox
            checked={nodeItem ? isSelected : isCategoryFullySelected}
            onCheckedChange={() => onNodeToggle(node.id)}
            disabled={isCoachModeActive}
            className={cn(
              'w-4 h-4 transition-all duration-200',
              isCategory && isCategoryPartiallySelected && 'opacity-50',
              isCoachModeActive && 'cursor-not-allowed opacity-50'
            )}
          />
        ) : (
          <div className="w-4 h-4 flex items-center justify-center">
            <div className="w-3 h-3 bg-gray-200 rounded border border-gray-300" />
          </div>
        )}

        {/* Icon */}
        {isCategory ? (
          <FiFolder
            className={cn(
              'w-4 h-4 text-blue-500 transition-all duration-200',
              isCurrentQuestion && isCoachModeActive && 'text-blue-700'
            )}
          />
        ) : (
          <FiFile
            className={cn(
              'w-4 h-4 text-gray-400 transition-all duration-200',
              isCurrentQuestion && isCoachModeActive && 'text-blue-600'
            )}
          />
        )}

        {/* Label and status */}
        <div className="flex-1 min-w-0">
          <span
            className={cn(
              'text-sm font-medium transition-all duration-200',
              nodeItem?.hasExistingResearch ? 'text-gray-600' : 'text-gray-900',
              matchesSearch && searchTerm ? 'bg-yellow-100' : '',
              isCurrentQuestion &&
                isCoachModeActive &&
                'text-blue-800 font-semibold'
            )}
          >
            {node.label || 'Untitled'}
            {isCurrentQuestion && isCoachModeActive && (
              <span className="ml-2 text-xs bg-blue-200 text-blue-800 px-2 py-1 rounded-full font-medium">
                Current
              </span>
            )}
          </span>

          <div className="flex items-center gap-2 mt-1">
            {/* Research status for questions */}
            {nodeItem && (
              <>
                {nodeItem.hasExistingResearch ? (
                  <span className="text-xs text-green-600 bg-green-50 px-1.5 py-0.5 rounded flex items-center gap-1">
                    <CheckCircle className="w-3 h-3" />
                    researched
                  </span>
                ) : (
                  <span
                    className={cn(
                      'text-xs px-1.5 py-0.5 rounded transition-all duration-200',
                      isCurrentQuestion && isCoachModeActive
                        ? 'text-blue-700 bg-blue-100 font-medium'
                        : 'text-orange-600 bg-orange-50'
                    )}
                  >
                    needs research
                  </span>
                )}
              </>
            )}

            {/* Stats for categories */}
            {isCategory && childrenStats.total > 0 && (
              <span className="text-xs text-gray-500">
                {childrenStats.researchable} researchable
                {childrenStats.selected > 0 && (
                  <span className="text-blue-600 font-medium">
                    , {childrenStats.selected} selected
                  </span>
                )}
              </span>
            )}
          </div>
        </div>
      </div>

      {/* Children */}
      {isExpanded && hasChildren && (
        <div>
          {node.children.map((child: any) => (
            <RecursiveBatchTreeView
              key={child.id}
              node={child}
              items={items}
              expandedNodes={expandedNodes}
              selectedItems={selectedItems}
              onNodeToggle={onNodeToggle}
              onExpandToggle={onExpandToggle}
              searchTerm={searchTerm}
              currentQuestionId={currentQuestionId}
              isCoachModeActive={isCoachModeActive}
              showResearchableOnly={showResearchableOnly}
              level={level + 1}
            />
          ))}
        </div>
      )}
    </div>
  )
}

export default React.memo(TreeSelectionPanel)

import React, {
  useState,
  useEffect,
  useCallback,
  ErrorInfo,
  ReactNode,
  useMemo,
  useRef,
} from 'react'
import { Textarea } from '@/components/ui/textarea'
import { Button } from '@/components/ui/button'
import { Eye, EyeOff, AlertTriangle, ExternalLink, Loader2 } from 'lucide-react'
import { DragTreeNodeContentStatus } from '@prisma/client'
import { useDragTreeStore } from '@/app/stores/dragtree_store'
import { useResearchLifecycle } from '@/app/(conv)/dragTree/[dragTreeId]/hooks/useResearchLifecycle'
import { cn } from '@/lib/utils'
import debounce from 'lodash/debounce'
import { useUIStore } from '@/app/stores/ui_store'
import { motion, AnimatePresence } from 'framer-motion'
import {
  SearchProgressIndicator,
  SourceCitations,
  hasSearchResults,
} from '@/app/(conv)/dragTree/[dragTreeId]/components/SearchResultsDisplay'
import { useTabStore } from '@/app/(conv)/dragTree/[dragTreeId]/stores/useTabStore'
import dynamic from 'next/dynamic'
import { shallow } from 'zustand/shallow'

// Simple error boundary for research operations
class ResearchErrorBoundary extends React.Component<
  { children: ReactNode; fallback?: ReactNode },
  { hasError: boolean; error?: Error }
> {
  constructor(props: { children: ReactNode; fallback?: ReactNode }) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('[Research] Error boundary caught error:', error, errorInfo)
  }

  render() {
    if (this.state.hasError) {
      return (
        this.props.fallback || (
          <div className="p-2 bg-red-50 border border-red-200 rounded text-red-700 text-sm">
            <div className="flex items-center gap-2">
              <AlertTriangle className="h-4 w-4" />
              <span>Research component error</span>
            </div>
          </div>
        )
      )
    }

    return this.props.children
  }
}

type QuickResearchPreviewButtonProps = {
  nodeId: string
  questionText: string
  contentId?: string
  className?: string
  enableHoverPreview?: boolean // Control whether to show on general hover
  /**
   * When true, the content will be constrained to a specific height and enable scrolling.
   * Used in tab view or other contexts where height should be limited.
   */
  constrainHeight?: boolean
}

// Lazy-load the heavy TipTap editor so dozens of collapsed previews don't inflate the JS bundle.
// The editor is only needed once the user expands a preview and streaming is complete.
const TiptapQuickResearchEditor = dynamic(
  () => import('@/app/components/editor/TiptapQuickResearchEditor'),
  {
    ssr: false,
    loading: () => (
      <div className="text-xs text-gray-400 py-2">Loading editor…</div>
    ),
  }
)

const QuickResearchPreviewButton: React.FC<QuickResearchPreviewButtonProps> = ({
  nodeId,
  questionText,
  contentId,
  className = '',
  enableHoverPreview = false,
  constrainHeight = false,
}) => {
  // --- STATE MANAGEMENT ---
  const nodeContentMap = useDragTreeStore(
    state => state.nodeContent.get(nodeId),
    shallow
  )
  const updateNodeContent = useDragTreeStore(state => state.updateNodeContent)
  const fetchNodeContent = useDragTreeStore(state => state.fetchNodeContent)
  const getNodePath = useDragTreeStore(state => state.getNodePath)

  const { setHoverCollapseLock } = useUIStore()
  const { addTab } = useTabStore()

  // --- DERIVED STATE & LIFECYCLE ---
  const fullQuestionPath = useMemo(
    () => getNodePath(nodeId) || questionText,
    [nodeId, questionText, getNodePath]
  )

  const { isStreaming, streamingContent, startResearch, activeContentId } =
    useResearchLifecycle({
      nodeId,
      questionText: fullQuestionPath,
    })

  // Memoize all derived values dependent on nodeContentMap and IDs
  const {
    workingContentId,
    storedNodeContent,
    status,
    storeContent,
    searchResults,
  } = useMemo(() => {
    const workingContentIdLocal =
      contentId ||
      activeContentId ||
      (nodeContentMap && nodeContentMap.size > 0
        ? Array.from(nodeContentMap.keys())[0]
        : null)

    const storedNodeContentLocal = workingContentIdLocal
      ? nodeContentMap?.get(workingContentIdLocal)
      : null

    const statusLocal = storedNodeContentLocal?.status || null
    const storeContentLocal = storedNodeContentLocal?.contentText || ''

    const searchResultsLocal =
      storedNodeContentLocal?.metadata &&
      hasSearchResults(storedNodeContentLocal.metadata)
        ? storedNodeContentLocal.metadata.searchResults
        : null

    return {
      workingContentId: workingContentIdLocal,
      storedNodeContent: storedNodeContentLocal,
      status: statusLocal,
      storeContent: storeContentLocal,
      searchResults: searchResultsLocal,
    }
  }, [contentId, activeContentId, nodeContentMap])

  // Track if this content item is currently being fetched
  const isContentFetching = useDragTreeStore(
    state =>
      workingContentId ? state.isContentFetching(workingContentId) : false,
    shallow
  )

  // --- LOCAL UI STATE ---
  const [showContent, setShowContent] = useState<boolean>(false)
  const [isPersistent, setIsPersistent] = useState<boolean>(false)
  const [isHovered, setIsHovered] = useState<boolean>(false)
  const [isButtonHovered, setIsButtonHovered] = useState<boolean>(false)
  const [isSaving, setIsSaving] = useState<boolean>(false)
  const [localContent, setLocalContent] = useState<string>(storeContent)
  const [currentSearchQuery, setCurrentSearchQuery] = useState<string>('')
  const [isSearching, setIsSearching] = useState<boolean>(false)
  const [localLoading, setLocalLoading] = useState<boolean>(false)

  // Ref to store debounce timeout so click can cancel it
  const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  // Sync with store content when not streaming
  useEffect(() => {
    if (!isStreaming) {
      setLocalContent(storeContent)
    }
  }, [storeContent, isStreaming])

  // Sync with streaming content
  useEffect(() => {
    if (isStreaming && streamingContent) {
      setLocalContent(streamingContent)
    }
  }, [isStreaming, streamingContent])

  // --- VISIBILITY LOGIC ---
  useEffect(() => {
    // Show content if it's pinned, being hovered, or is actively streaming.
    const shouldShow =
      isPersistent || isHovered || isButtonHovered || isStreaming
    if (shouldShow !== showContent) {
      setShowContent(shouldShow)
    }
  }, [isPersistent, isHovered, isButtonHovered, isStreaming, showContent])

  // NEW: Lazy-load metadata only when preview becomes visible
  useEffect(() => {
    if (
      showContent &&
      status === DragTreeNodeContentStatus.ACTIVE &&
      !searchResults &&
      workingContentId
    ) {
      console.log(
        '[QuickResearchPreview] Fetching metadata for content',
        workingContentId
      )
      fetch(`/api/dragtree/content?contentId=${workingContentId}`)
        .then(res => res.json())
        .then(json => {
          if (json.success && json.data?.content_metadata) {
            updateNodeContent(nodeId, workingContentId, {
              metadata: json.data.content_metadata,
            })
          }
        })
        .catch(err => console.error('Failed to load metadata:', err))
    }
  }, [
    showContent,
    status,
    searchResults,
    workingContentId,
    nodeId,
    updateNodeContent,
  ])

  // Debounced lazy-load to avoid flood of requests on quick cursor sweeps
  useEffect(() => {
    if (
      status !== DragTreeNodeContentStatus.ACTIVE ||
      storeContent.trim().length > 0 ||
      !workingContentId
    ) {
      return
    }

    if (showContent) {
      setLocalLoading(true)
      debounceTimeoutRef.current = setTimeout(() => {
        fetchNodeContent(nodeId, workingContentId!).finally(() => {
          setLocalLoading(false)
        })
      }, 200)
    }

    return () => {
      if (debounceTimeoutRef.current) clearTimeout(debounceTimeoutRef.current)
    }
  }, [
    showContent,
    status,
    storeContent,
    workingContentId,
    nodeId,
    fetchNodeContent,
  ])

  // If storeContent arrives, ensure localLoading is cleared
  useEffect(() => {
    if (storeContent.trim().length > 0) {
      setLocalLoading(false)
    }
  }, [storeContent])

  // Auto-pin the view when a stream finishes successfully.
  useEffect(() => {
    if (isStreaming) {
      // While streaming, ensure it's pinned open.
      if (!isPersistent) setIsPersistent(true)
    }
  }, [isStreaming, isPersistent])

  // Auto-start research when a content record is freshly created (INITIALIZED)
  useEffect(() => {
    if (status === DragTreeNodeContentStatus.INITIALIZED && workingContentId) {
      // Small delay to ensure the store has fully updated before kicking off research
      const timer = setTimeout(() => {
        startResearch(workingContentId)
      }, 100)
      return () => clearTimeout(timer)
    }
  }, [status, workingContentId, startResearch])

  const debouncedSave = useCallback(
    debounce((newContentId: string, newContentText: string) => {
      updateNodeContent(nodeId, newContentId, { contentText: newContentText })
      setIsSaving(false)
    }, 1000),
    [nodeId, updateNodeContent]
  )

  // Determine if we should show a loading placeholder instead of the editor
  const showLoadingPlaceholder =
    (localLoading || isContentFetching) && storeContent.trim().length === 0

  const handleMouseEnter = () => setIsHovered(true)
  const handleMouseLeave = () => setIsHovered(false)
  const handleButtonMouseEnter = () => setIsButtonHovered(true)
  const handleButtonMouseLeave = () => setIsButtonHovered(false)

  const handleClick = (event: React.MouseEvent) => {
    event.preventDefault()
    event.stopPropagation()

    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current)
      debounceTimeoutRef.current = null
    }

    if (
      isContentFetching || // still loading – ignore fast-click
      (status === DragTreeNodeContentStatus.ACTIVE &&
        storeContent.trim().length === 0 &&
        workingContentId &&
        !isStreaming)
    ) {
      fetchNodeContent(nodeId, workingContentId!)
    }

    // Prevent editing/opening while loading
    if (isContentFetching) return

    // Allow pinning/unpinning only when not streaming.
    if (!isStreaming) {
      setIsPersistent(!isPersistent)
    }
  }

  const getPlaceholderText = () => {
    if (isStreaming) return 'AI is researching...'
    if (status === DragTreeNodeContentStatus.ACTIVE)
      return 'Research complete. Click to open in a new tab.'
    return 'Click the research button to start.'
  }

  const handleOpenInTab = () => {
    if (workingContentId) {
      const tabTitle =
        questionText.length > 30
          ? `${questionText.substring(0, 30)}...`
          : questionText

      addTab({
        title: tabTitle,
        fullTitle: fullQuestionPath,
        type: 'research',
        nodeId: nodeId,
        contentId: workingContentId,
        isClosable: true,
      })
    }
  }

  const handleContentContainerClick = (event: React.MouseEvent) => {
    event.stopPropagation()
    handleOpenInTab()
  }

  const stopPropagationOnClick = (event: React.MouseEvent) => {
    event.stopPropagation()
  }

  // --- RENDER ---
  const isEditable = status === DragTreeNodeContentStatus.ACTIVE && !isStreaming

  if (
    !enableHoverPreview &&
    !isPersistent &&
    !isStreaming &&
    !storedNodeContent
  ) {
    return null
  }

  return (
    <div
      className={cn('w-full max-w-full overflow-x-hidden', className)}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      {/* Subtle Research Button */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2 flex-1 min-w-0">
          <Button
            variant="ghost"
            size="sm"
            onClick={handleClick}
            onMouseEnter={handleButtonMouseEnter}
            onMouseLeave={handleButtonMouseLeave}
            className={cn(
              'h-6 px-2 text-xs font-normal transition-all duration-200 border-0',
              {
                'bg-blue-50/80 text-blue-600 shadow-sm':
                  showContent || isHovered,
                'bg-gray-50/60 text-gray-500 hover:bg-gray-100/80 hover:text-gray-700':
                  !showContent && !isHovered,
              }
            )}
          >
            {isPersistent ? (
              <EyeOff className="h-3 w-3 mr-1" />
            ) : (
              <Eye className="h-3 w-3 mr-1" />
            )}
            <span className="text-xs">
              {isStreaming || status === DragTreeNodeContentStatus.PROCESSING
                ? 'Researching...'
                : status === DragTreeNodeContentStatus.ACTIVE
                  ? 'Quick Research'
                  : 'Quick Research'}
            </span>
            {status === DragTreeNodeContentStatus.ACTIVE && !isStreaming && (
              <span className="ml-1.5 w-1.5 h-1.5 rounded-full bg-green-500"></span>
            )}
            {(status === DragTreeNodeContentStatus.PROCESSING ||
              isStreaming) && (
              <span className="ml-1.5 w-1.5 h-1.5 rounded-full bg-amber-500 animate-pulse"></span>
            )}
          </Button>

          {/* Source Citations in tree view cards */}
          {searchResults &&
            searchResults.length > 0 &&
            status === DragTreeNodeContentStatus.ACTIVE && (
              <div className="flex-shrink-0">
                <SourceCitations
                  searchResults={searchResults}
                  maxSources={searchResults.length}
                  className=""
                />
              </div>
            )}
        </div>

        {/* Open in Tab Button - aligned right */}
        {status === DragTreeNodeContentStatus.ACTIVE &&
          !isContentFetching &&
          localContent.trim() && (
            <Button
              variant="ghost"
              size="sm"
              onClick={handleOpenInTab}
              className={cn(
                'h-6 px-2 text-xs font-normal border-0',
                'bg-gray-50/60 text-gray-500 hover:bg-gray-100/80 hover:text-gray-700',
                isContentFetching && 'cursor-not-allowed opacity-50'
              )}
              title={
                isContentFetching ? 'Loading research…' : 'Open in separate tab'
              }
              disabled={isContentFetching}
            >
              {isContentFetching ? (
                <Loader2 className="h-3 w-3 animate-spin" />
              ) : (
                <ExternalLink className="h-3 w-3" />
              )}
            </Button>
          )}
      </div>

      {/* Search Progress Indicator */}
      {isSearching && (
        <div className="mt-2">
          <SearchProgressIndicator
            isSearching={isSearching}
            currentQuery={currentSearchQuery}
          />
        </div>
      )}

      {/* Animated Content */}
      <div
        className={cn(
          'transition-all duration-500 ease-in-out',
          'w-full max-w-full',
          !showContent && 'max-h-0 overflow-hidden opacity-0 mt-0',
          showContent &&
            (constrainHeight
              ? 'max-h-[400px] overflow-hidden opacity-100 mt-3'
              : 'max-h-none overflow-visible opacity-100 mt-3')
        )}
        style={{
          width: '100%',
          maxWidth: '100%',
          minWidth: 0,
          boxSizing: 'border-box',
        }}
      >
        <div
          className={cn(
            'relative w-full max-w-full',
            constrainHeight ? 'h-full' : ''
          )}
          style={{
            width: '100%',
            maxWidth: '100%',
            minWidth: 0,
            boxSizing: 'border-box',
            overflow: constrainHeight ? 'hidden' : 'visible',
            ...(constrainHeight ? { height: '350px' } : {}),
          }}
        >
          {/* Streaming / processing states use textarea, active shows editor or placeholder */}
          {isStreaming ||
          status === DragTreeNodeContentStatus.PROCESSING ||
          status === DragTreeNodeContentStatus.INITIALIZED ? (
            <Textarea
              value={
                isStreaming && streamingContent
                  ? streamingContent
                  : localContent
              }
              onChange={event => {
                const newContent = event.target.value
                setLocalContent(newContent)
                if (workingContentId) {
                  setIsSaving(true)
                  debouncedSave(workingContentId, newContent)
                }
              }}
              onMouseDown={e => e.stopPropagation()}
              onKeyDown={e => e.stopPropagation()}
              onClick={stopPropagationOnClick}
              placeholder={getPlaceholderText()}
              readOnly={!isEditable || isStreaming}
              className={cn(
                'w-full text-sm transition-all duration-300 resize-none',
                constrainHeight ? 'h-full' : 'min-h-[200px]',
                isEditable && !isStreaming
                  ? 'bg-white border-blue-200 focus:border-blue-400'
                  : 'bg-gray-50 border-gray-200'
              )}
              rows={constrainHeight ? undefined : 8}
              style={constrainHeight ? { height: '100%' } : {}}
            />
          ) : showLoadingPlaceholder ? (
            <div className="flex items-center justify-center h-24 text-sm text-gray-400">
              <Loader2 className="h-4 w-4 animate-spin mr-2" /> Loading
              research…
            </div>
          ) : (
            <div
              onClickCapture={handleContentContainerClick}
              role="button"
              tabIndex={0}
              className="cursor-pointer focus:outline-none"
            >
              <TiptapQuickResearchEditor
                content={localContent || ''}
                onContentChange={() => {}}
                onJSONChange={() => {}}
                isReadOnly={true}
                isStreaming={false}
                placeholder={getPlaceholderText()}
                className="max-w-full"
                showBubbleMenu={!constrainHeight}
                debounceMs={1000}
                autoFocus={false}
                compact={!constrainHeight}
                questionText={questionText}
                questionNodeId={nodeId}
                showResearchButton={false}
              />
            </div>
          )}
        </div>

        {/* Status Footer - only show for textarea (during streaming), TiptapQuickResearchEditor has its own */}
        {(isStreaming ||
          status === DragTreeNodeContentStatus.PROCESSING ||
          status === DragTreeNodeContentStatus.INITIALIZED) && (
          <div className="flex items-center justify-end text-xs text-gray-400 h-5 pr-2">
            <AnimatePresence mode="wait">
              {isSaving ? (
                <motion.div
                  key="saving"
                  initial={{ opacity: 0, y: 5 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -5 }}
                  style={{ display: 'flex', alignItems: 'center' }}
                >
                  <span className="mr-1.5 w-1.5 h-1.5 rounded-full bg-amber-500 animate-pulse"></span>
                  Saving...
                </motion.div>
              ) : status === DragTreeNodeContentStatus.ACTIVE && !isSaving ? (
                <motion.div
                  key="saved"
                  initial={{ opacity: 0, y: 5 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -5 }}
                  style={{ color: '#10b981' }}
                >
                  Saved
                </motion.div>
              ) : null}
            </AnimatePresence>
          </div>
        )}
      </div>
    </div>
  )
}

// Wrap with error boundary for better error handling
const QuickResearchPreviewButtonWithErrorBoundary: React.FC<
  QuickResearchPreviewButtonProps
> = props => (
  <ResearchErrorBoundary>
    <QuickResearchPreviewButton {...props} />
  </ResearchErrorBoundary>
)

// Memoize to prevent unnecessary re-renders when props haven't changed
export default React.memo(QuickResearchPreviewButtonWithErrorBoundary)

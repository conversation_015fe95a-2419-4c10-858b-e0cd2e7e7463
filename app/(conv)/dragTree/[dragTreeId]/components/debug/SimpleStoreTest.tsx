'use client'

import React, { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { useDragTreeStore } from '@/app/stores/dragtree_store'
import {
  TestDeleteNode,
  TestNodeMetadata,
  ViewDatabaseMetadata,
  PerformanceMonitor,
  TestUIImprovements,
} from './'

/**
 * Comprehensive testing interface for drag tree store operations
 * Organized into sections for better debugging workflow
 */
const SimpleStoreTest: React.FC = () => {
  const frontendTreeStructure = useDragTreeStore(
    state => state.frontendTreeStructure
  )
  const nodeMap = useDragTreeStore(state => state.nodeMap)
  const dragTreeId = useDragTreeStore(state => state.dragTreeId)
  const loadFromDatabase = useDragTreeStore(state => state.loadFromDatabase)

  const [activeSection, setActiveSection] = useState<string>('performance')

  const sections = [
    { id: 'performance', label: 'Performance Monitor', icon: '📊' },
    { id: 'ui', label: 'UI Improvements', icon: '🎨' },
    { id: 'metadata', label: 'Node Metadata', icon: '🏷️' },
    { id: 'database', label: 'Database View', icon: '🗄️' },
    { id: 'delete', label: 'Delete Testing', icon: '🗑️' },
  ]

  const handleLoadSampleTree = async () => {
    if (dragTreeId) {
      await loadFromDatabase()
    }
  }

  const handleClearTree = () => {
    // Reset the store by reloading the page for testing
    window.location.reload()
  }

  const renderActiveSection = () => {
    switch (activeSection) {
      case 'performance':
        return <PerformanceMonitor />
      case 'ui':
        return <TestUIImprovements />
      case 'metadata':
        return <TestNodeMetadata />
      case 'database':
        return <ViewDatabaseMetadata />
      case 'delete':
        return <TestDeleteNode />
      default:
        return <PerformanceMonitor />
    }
  }

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="text-2xl font-bold flex items-center gap-2">
            🧪 Debug Testing Interface
            <Badge variant="outline">{nodeMap.size} nodes</Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {/* Tree Status */}
          <div className="flex flex-wrap gap-2 mb-4">
            <Badge variant={frontendTreeStructure ? 'default' : 'secondary'}>
              Tree: {frontendTreeStructure ? 'Loaded' : 'Empty'}
            </Badge>
            <Badge variant={dragTreeId ? 'default' : 'secondary'}>
              DB ID: {dragTreeId || 'None'}
            </Badge>
          </div>

          {/* Section Navigation */}
          <div className="flex flex-wrap gap-2 mb-6">
            {sections.map(section => (
              <Button
                key={section.id}
                variant={activeSection === section.id ? 'default' : 'outline'}
                size="sm"
                onClick={() => setActiveSection(section.id)}
                className="text-sm"
              >
                {section.icon} {section.label}
              </Button>
            ))}
          </div>

          {/* Quick Actions */}
          <div className="flex flex-wrap gap-2 mb-6">
            <Button
              onClick={handleLoadSampleTree}
              disabled={!dragTreeId}
              variant="outline"
              size="sm"
            >
              🔄 Reload Tree
            </Button>
            <Button
              onClick={handleClearTree}
              variant="outline"
              size="sm"
              className="text-red-600 hover:text-red-700"
            >
              🧹 Clear Tree
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Active Section Content */}
      <div className="min-h-[400px]">{renderActiveSection()}</div>
    </div>
  )
}

export default SimpleStoreTest

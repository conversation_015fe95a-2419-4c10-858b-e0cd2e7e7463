'use client'

import React, { useState, useRef, useEffect, useCallback, useMemo } from 'react'
import { Session } from 'next-auth'
import {
  FiShare2,
  FiMoreHorizontal,
  FiZap,
  FiEdit2,
  FiCheck,
  FiX,
  FiCpu,
  FiCopy,
} from 'react-icons/fi'
import { toast } from 'react-hot-toast'
import { useDragTreeStore } from '@/app/stores/dragtree_store'
import { useRouter } from 'next/navigation'
import { updateDragTreeTitle } from '@/app/server-actions/drag-tree'
import { useUIStore } from '@/app/stores/ui_store'
import { cn } from '@/lib/utils'
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from '@/components/ui/sheet'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from '@/components/ui/tabs'
import { FlowExportButton } from '@/app/(conv)/dragTree/[dragTreeId]/components/VisualFlowDiagram/components/FlowExportButton'

type HeaderProps = {
  session: Session | null
  showSyncTest?: boolean
  onToggleSyncTest?: () => void
  isLoading?: boolean
}

const Header: React.FC<HeaderProps> = ({
  showSyncTest = false,
  onToggleSyncTest,
  isLoading = false,
}) => {
  // Optimized: Use individual selectors to prevent unnecessary re-renders
  // Header only needs these specific values, not the entire store
  const dragTreeId = useDragTreeStore(state => state.dragTreeId)
  const screeningQuestion = useDragTreeStore(state => state.screeningQuestion)
  const dragTreeTitle = useDragTreeStore(state => state.dragTreeTitle)
  const setDragTreeTitle = useDragTreeStore(state => state.setDragTreeTitle)
  // Only access frontendTreeStructure when actually needed for markdown generation
  const getFrontendTreeStructure = useDragTreeStore(
    state => () => state.frontendTreeStructure
  )

  const useRealLLMAPI = useUIStore(state => state.useRealLLMAPI)
  const setUseRealLLMAPI = useUIStore(state => state.setUseRealLLMAPI)
  const router = useRouter()

  // Title editing state
  const [isEditingTitle, setIsEditingTitle] = useState<boolean>(false)
  const [editedTitle, setEditedTitle] = useState<string>('')
  const [isUpdatingTitle, setIsUpdatingTitle] = useState<boolean>(false)
  const titleInputRef = useRef<HTMLInputElement>(null)

  // Dev tools sheet state
  const [isDevSheetOpen, setIsDevSheetOpen] = useState<boolean>(false)

  // Check if we're on localhost
  const isLocalhost =
    typeof window !== 'undefined' &&
    (window.location.hostname === 'localhost' ||
      window.location.hostname === '127.0.0.1')

  // Focus input when editing starts
  useEffect(() => {
    if (isEditingTitle && titleInputRef.current) {
      titleInputRef.current.focus()
      titleInputRef.current.select()
    }
  }, [isEditingTitle])

  // Reset editing state when drag tree changes
  useEffect(() => {
    setIsEditingTitle(false)
    setEditedTitle('')
  }, [dragTreeId])

  const handleFeatureClick = (featureName: string) => {
    toast.success(`${featureName} feature clicked!`)
  }

  // Handle LLM API toggle
  const handleToggleLLMAPI = useCallback(() => {
    const newValue = !useRealLLMAPI
    setUseRealLLMAPI(newValue)
    toast.success(
      newValue
        ? '🤖 Real LLM API enabled - using GPT-4o-mini'
        : '🧪 Using simulator mode'
    )
  }, [useRealLLMAPI, setUseRealLLMAPI])

  // Handle refine question - navigate to screening with current question prefilled
  const handleRefineQuestion = () => {
    if (screeningQuestion) {
      const encodedQuestion = encodeURIComponent(screeningQuestion)
      router.push(`/screening?question=${encodedQuestion}`)
      toast.success('Redirecting to refine your question...')
    } else {
      toast.error('No screening question available to refine')
    }
  }

  // Handle title editing
  const handleStartEditTitle = useCallback(() => {
    const currentTitle = dragTreeTitle || 'Untitled Drag Tree'
    setEditedTitle(currentTitle)
    setIsEditingTitle(true)
  }, [dragTreeTitle])

  const handleSaveTitle = async () => {
    if (!dragTreeId || !editedTitle.trim()) {
      toast.error('Please enter a valid title')
      return
    }

    setIsUpdatingTitle(true)
    try {
      const result = await updateDragTreeTitle(dragTreeId, editedTitle.trim())
      if (result.success) {
        setDragTreeTitle(editedTitle.trim())
        setIsEditingTitle(false)
        toast.success('Title updated successfully!')
      } else {
        toast.error('Failed to update title')
      }
    } catch (error) {
      console.error('Error updating title:', error)
      toast.error('Failed to update title')
    } finally {
      setIsUpdatingTitle(false)
    }
  }

  const handleCancelEditTitle = useCallback(() => {
    setIsEditingTitle(false)
    setEditedTitle('')
  }, [])

  const handleTitleKeyPress = useCallback(
    (e: React.KeyboardEvent) => {
      if (e.key === 'Enter') {
        handleSaveTitle()
      } else if (e.key === 'Escape') {
        handleCancelEditTitle()
      }
    },
    [handleSaveTitle, handleCancelEditTitle]
  )

  // Dev tools functions
  const handleCopyOriginalAsk = async () => {
    if (screeningQuestion) {
      try {
        await navigator.clipboard.writeText(screeningQuestion)
        toast.success('Original question copied to clipboard!')
      } catch (error) {
        toast.error('Failed to copy to clipboard')
      }
    } else {
      toast.error('No original question available')
    }
  }

  const generateMarkdownFromTree = useCallback(() => {
    const frontendTreeStructure = getFrontendTreeStructure()
    if (!frontendTreeStructure) {
      return 'No tree structure available'
    }

    const generateMarkdownRecursive = (
      node: any,
      depth: number = 0
    ): string => {
      if (!node) return ''

      const indent = '  '.repeat(depth)
      let markdown = ''

      if (node.type === 'category') {
        // Categories as headers
        const headerLevel = '#'.repeat(Math.min(depth + 1, 6))
        markdown = `${headerLevel} ${node.label}\n\n`
      } else if (node.type === 'question') {
        // Questions as list items
        markdown = `${indent}- ${node.label}\n`
      }

      // Process children
      if (node.children && node.children.length > 0) {
        node.children.forEach((child: any) => {
          markdown += generateMarkdownRecursive(child, depth + 1)
        })
      }

      return markdown
    }

    return generateMarkdownRecursive(frontendTreeStructure)
  }, [getFrontendTreeStructure])

  const handleCopyMarkdown = async () => {
    try {
      const markdown = generateMarkdownFromTree()
      await navigator.clipboard.writeText(markdown)
      toast.success('Markdown copied to clipboard!')
    } catch (error) {
      toast.error('Failed to copy markdown to clipboard')
    }
  }

  // The `isLoading` prop from the parent is the most reliable indicator.
  // The other condition is a fallback for when the tree is loading but the prop isn't provided.
  const isTitleLoading =
    isLoading || (dragTreeId && !getFrontendTreeStructure())

  // Get display title with loading state
  const getDisplayTitle = () => {
    if (isTitleLoading) {
      return (
        <div className="flex items-center space-x-2">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-400"></div>
          <span className="text-gray-500">Loading title...</span>
        </div>
      )
    }
    return dragTreeTitle || 'Untitled Drag Tree'
  }

  return (
    <header className="bg-white border-b border-gray-200 shadow-sm z-50 sticky top-0">
      <div className="flex items-center justify-between px-4 sm:px-6 lg:px-8 py-3">
        {/* Left section */}
        <div
          className={cn(
            'flex items-center space-x-4',
            isEditingTitle && 'flex-1 mr-4'
          )}
        >
          {/* Page title - editable */}
          <div
            className={cn(
              'flex flex-col',
              isEditingTitle ? 'flex-1' : 'max-w-3xl'
            )}
          >
            {isEditingTitle ? (
              <div className="flex items-center space-x-2 w-full">
                <input
                  ref={titleInputRef}
                  type="text"
                  value={editedTitle}
                  onChange={e => setEditedTitle(e.target.value)}
                  onKeyDown={handleTitleKeyPress}
                  className="text-base font-semibold text-gray-900 bg-white border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent w-full max-w-md"
                  disabled={isUpdatingTitle}
                  placeholder="Enter drag tree title..."
                />
                <button
                  onClick={handleSaveTitle}
                  disabled={isUpdatingTitle}
                  className="p-1 text-green-600 hover:text-green-700 disabled:opacity-50"
                  title="Save title"
                >
                  <FiCheck size={16} />
                </button>
                <button
                  onClick={handleCancelEditTitle}
                  disabled={isUpdatingTitle}
                  className="p-1 text-red-600 hover:text-red-700 disabled:opacity-50"
                  title="Cancel editing"
                >
                  <FiX size={16} />
                </button>
              </div>
            ) : (
              <div className="flex items-center space-x-2 group">
                <h1 className="text-lg font-semibold text-gray-900">
                  {getDisplayTitle()}
                </h1>
                {!isTitleLoading && (
                  <button
                    onClick={handleStartEditTitle}
                    className="opacity-0 group-hover:opacity-100 p-1 text-gray-400 hover:text-gray-600 transition-opacity"
                    title="Edit title"
                  >
                    <FiEdit2 size={14} />
                  </button>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Right section - Feature buttons */}
        <div className="flex items-center space-x-2">
          {/* Localhost-only Real/Simulator toggle – always visible */}
          {isLocalhost && (
            <Button
              onClick={handleToggleLLMAPI}
              variant="outline"
              size="sm"
              className="flex items-center space-x-1"
              title="Toggle LLM API mode"
            >
              <FiCpu className="h-4 w-4" />
              <span>
                {useRealLLMAPI
                  ? 'Using Real LLM API'
                  : 'Using Simulated LLM API'}
              </span>
            </Button>
          )}

          {/* Dev Tools Sheet */}
          <Sheet open={isDevSheetOpen} onOpenChange={setIsDevSheetOpen}>
            <SheetTrigger asChild>
              <button
                id="tutorial-node-actions"
                className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-md transition-colors"
                title="More options"
              >
                <FiMoreHorizontal size={20} />
              </button>
            </SheetTrigger>
            <SheetContent className="w-[400px] sm:w-[540px]">
              <SheetHeader>
                <SheetTitle>More Options</SheetTitle>
                <SheetDescription>
                  View metadata, and more to come
                </SheetDescription>
              </SheetHeader>
              <div className="mt-6">
                <Tabs defaultValue="metadata" className="w-full">
                  <TabsList className="grid w-full grid-cols-3">
                    <TabsTrigger value="metadata">Metadata</TabsTrigger>
                    <TabsTrigger value="export">Export</TabsTrigger>
                    <TabsTrigger value="share">Share</TabsTrigger>
                  </TabsList>

                  <TabsContent value="metadata" className="space-y-6 mt-6">
                    {/* Original Ask */}
                    <div className="space-y-2">
                      <label className="text-sm font-medium text-gray-700">
                        Original Question
                      </label>
                      <div className="relative">
                        <Textarea
                          value={
                            screeningQuestion ||
                            'No original question available'
                          }
                          readOnly
                          className="min-h-[100px] pr-10 resize-none"
                          placeholder="No original question available"
                        />
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={handleCopyOriginalAsk}
                          className="absolute top-2 right-2 h-8 w-8 p-0"
                          title="Copy original question"
                        >
                          <FiCopy className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>

                    {/* Tree Structure Preview */}
                    <div className="space-y-2">
                      <label className="text-sm font-medium text-gray-700">
                        Tree Structure (Markdown Preview)
                      </label>
                      <div className="relative">
                        <Textarea
                          value={generateMarkdownFromTree()}
                          readOnly
                          className="min-h-[200px] pr-10 resize-none font-mono text-xs"
                          placeholder="No tree structure available"
                        />
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={handleCopyMarkdown}
                          className="absolute top-2 right-2 h-8 w-8 p-0"
                          title="Copy markdown"
                        >
                          <FiCopy className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>

                    {/* Dev Tools Section - Only show on localhost */}
                    {isLocalhost && (
                      <div className="space-y-4 pt-4 border-t border-gray-200">
                        <h3 className="text-sm font-semibold text-gray-900">
                          Development Tools
                        </h3>

                        <div className="flex flex-col space-y-2">
                          {/* Real LLM Toggle */}
                          <Button
                            onClick={handleToggleLLMAPI}
                            variant={useRealLLMAPI ? 'default' : 'outline'}
                            className="w-full justify-start"
                          >
                            <FiCpu className="mr-2 h-4 w-4" />
                            {useRealLLMAPI
                              ? 'Real LLM API (Active)'
                              : 'Enable Real LLM API'}
                          </Button>

                          {/* Sync Test Toggle */}
                          {onToggleSyncTest && (
                            <Button
                              onClick={onToggleSyncTest}
                              variant={showSyncTest ? 'default' : 'outline'}
                              className="w-full justify-start"
                            >
                              <FiZap className="mr-2 h-4 w-4" />
                              {showSyncTest
                                ? 'Sync Test (Active)'
                                : 'Enable Sync Test'}
                            </Button>
                          )}
                        </div>
                      </div>
                    )}
                  </TabsContent>

                  <TabsContent value="export" className="space-y-6 mt-6">
                    <div className="space-y-4">
                      <div>
                        <h3 className="text-lg font-medium text-gray-900 mb-4">
                          Export Flow Diagrams
                        </h3>
                        <div className="space-y-3">
                          {/* Flow Diagram Export */}
                          <div className="p-4 border border-gray-200 rounded-lg">
                            <h4 className="font-medium text-gray-900 mb-2">
                              Visual Flow Diagrams
                            </h4>
                            <p className="text-sm text-gray-500 mb-3">
                              Export your flow diagram as a high-resolution PNG
                              image in different layouts.
                            </p>
                            <FlowExportButton />
                          </div>
                        </div>
                      </div>
                    </div>
                  </TabsContent>

                  <TabsContent value="share" className="space-y-6 mt-6">
                    <div className="text-center py-12">
                      <FiShare2 className="mx-auto h-12 w-12 text-gray-400" />
                      <h3 className="mt-4 text-lg font-medium text-gray-900">
                        Share Features
                      </h3>
                      <p className="mt-2 text-sm text-gray-500">
                        Sharing functionality is coming soon. You&apos;ll be
                        able to share your drag trees with others.
                      </p>
                    </div>
                  </TabsContent>
                </Tabs>
              </div>
            </SheetContent>
          </Sheet>
        </div>
      </div>
    </header>
  )
}

export default React.memo(Header)

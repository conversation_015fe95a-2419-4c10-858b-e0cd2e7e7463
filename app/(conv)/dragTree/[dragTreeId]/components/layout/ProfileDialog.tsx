'use client'

import React, { useState } from 'react'
import { Session } from 'next-auth'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  FiMail,
  FiCalendar,
  FiCreditCard,
  FiSettings,
  FiLogOut,
  FiStar,
} from 'react-icons/fi'
import { signOut } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { toast } from 'react-hot-toast'

type ProfileDialogProps = {
  isOpen: boolean
  onClose: () => void
  session: Session
}

const ProfileDialog: React.FC<ProfileDialogProps> = ({
  isOpen,
  onClose,
  session,
}) => {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const user = session.user
  // Note: error is available but not used in this component

  const handleSignOut = async () => {
    setIsLoading(true)
    try {
      await signOut({ redirect: false })
      toast.success('Signed out successfully')
      router.push('/')
      onClose()
    } catch {
      // Error handled by showing toast message - no need to use error object
      toast.error('Error signing out')
    } finally {
      setIsLoading(false)
    }
  }

  const handleManageSubscription = () => {
    toast.success('Subscription management coming soon!')
  }

  // Mock subscription data - replace with real data
  const subscriptionStatus = 'Pro Plan'
  const subscriptionEndDate = 'Dec 31, 2024'
  const isSubscribed = true

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-md mx-auto bg-white rounded-2xl shadow-2xl border-0 p-0 overflow-hidden">
        {/* Header with gradient background */}
        <div className="bg-gradient-to-br from-purple-600 via-blue-600 to-indigo-700 px-6 py-8 text-white relative overflow-hidden">
          <div className="absolute inset-0 bg-black/10"></div>
          <div className="relative z-10">
            <DialogHeader>
              <div className="flex items-center space-x-4">
                <div className="relative">
                  {/* eslint-disable-next-line @next/next/no-img-element */}
                  <img
                    src={user?.image || '/images/placeholder.jpg'}
                    alt="Profile"
                    width={64}
                    height={64}
                    className="w-16 h-16 rounded-full border-4 border-white/30 shadow-lg object-cover"
                  />
                  <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-green-400 rounded-full border-2 border-white flex items-center justify-center">
                    <div className="w-2 h-2 bg-white rounded-full"></div>
                  </div>
                </div>
                <div className="flex-1">
                  <DialogTitle className="text-xl font-bold text-white mb-1">
                    {user?.name || 'User Name'}
                  </DialogTitle>
                  <DialogDescription className="text-blue-100 text-sm">
                    Welcome back to your profile
                  </DialogDescription>
                </div>
              </div>
            </DialogHeader>
          </div>
        </div>

        {/* Content */}
        <div className="px-6 py-6 space-y-6">
          {/* User Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Account Information
            </h3>

            {/* Email */}
            <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-xl">
              <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                <FiMail className="w-5 h-5 text-blue-600" />
              </div>
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-700">Email</p>
                <p className="text-sm text-gray-900">
                  {user?.email || '<EMAIL>'}
                </p>
              </div>
            </div>

            {/* Subscription Status */}
            <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-xl">
              <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                <FiStar className="w-5 h-5 text-purple-600" />
              </div>
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-700">
                  Subscription
                </p>
                <div className="flex items-center space-x-2">
                  <p className="text-sm text-gray-900">{subscriptionStatus}</p>
                  <Badge
                    variant={isSubscribed ? 'default' : 'secondary'}
                    className="text-xs"
                  >
                    {isSubscribed ? 'Active' : 'Free'}
                  </Badge>
                </div>
              </div>
            </div>

            {/* Subscription End Date */}
            {isSubscribed && (
              <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-xl">
                <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                  <FiCalendar className="w-5 h-5 text-green-600" />
                </div>
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-700">
                    Valid Until
                  </p>
                  <p className="text-sm text-gray-900">{subscriptionEndDate}</p>
                </div>
              </div>
            )}
          </div>

          {/* Action Buttons */}
          <div className="space-y-3 pt-4 border-t border-gray-100">
            {/* Manage Subscription */}
            {isSubscribed && (
              <Button
                onClick={handleManageSubscription}
                variant="outline"
                className="w-full flex items-center justify-center space-x-2 h-12 rounded-xl border-gray-200 hover:bg-gray-50 transition-colors"
              >
                <FiCreditCard className="w-4 h-4" />
                <span>Manage Subscription</span>
              </Button>
            )}

            {/* Settings */}
            <Button
              onClick={() => {
                toast.success('Settings page coming soon!')
              }}
              variant="outline"
              className="w-full flex items-center justify-center space-x-2 h-12 rounded-xl border-gray-200 hover:bg-gray-50 transition-colors"
            >
              <FiSettings className="w-4 h-4" />
              <span>Account Settings</span>
            </Button>

            {/* Sign Out */}
            <Button
              onClick={handleSignOut}
              disabled={isLoading}
              variant="outline"
              className="w-full flex items-center justify-center space-x-2 h-12 rounded-xl border-red-200 text-red-600 hover:bg-red-50 hover:border-red-300 transition-colors"
            >
              <FiLogOut className="w-4 h-4" />
              <span>{isLoading ? 'Signing out...' : 'Sign Out'}</span>
            </Button>
          </div>

          {/* Footer */}
          <div className="text-center pt-4 border-t border-gray-100">
            <p className="text-xs text-gray-500">
              Member since {new Date().getFullYear()}
            </p>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}

export default React.memo(ProfileDialog)

/**
 * TEST COMPONENT - Demonstrates Zustand concurrency behavior
 *
 * This component tests the exact scenario you described:
 * - v1 as start
 * - update 1: take v1 as base, change to v2
 * - update 2: take v1 as base, change to v3
 * - Both updates happen at nearly the same time
 */

import React, { useState } from 'react'
import { useDragTreeStore } from '@/app/stores/dragtree_store'
import { DragTreeNodeContentStatus } from '@prisma/client'

const ZustandConcurrencyTest: React.FC = () => {
  const addNodeContent = useDragTreeStore(state => state.addNodeContent)
  const updateNodeContent = useDragTreeStore(state => state.updateNodeContent)
  const getSpecificContent = useDragTreeStore(state => state.getSpecificContent)
  const nodeContent = useDragTreeStore(state => state.nodeContent)
  const [testResults, setTestResults] = useState<string[]>([])
  const [isRunning, setIsRunning] = useState(false)

  const addTestResult = (result: string) => {
    setTestResults(prev => [...prev, `${new Date().toISOString()}: ${result}`])
  }

  const testConcurrentUpdates = async () => {
    setIsRunning(true)
    setTestResults([])

    const testNodeId = 'test-node-123'
    const contentId1 = 'content-1'
    const contentId2 = 'content-2'

    addTestResult('🚀 Starting concurrency test...')

    // Step 1: Create initial content (v1 state)
    addNodeContent(testNodeId, {
      contentId: contentId1,
      contentType: 'QUICK_RESEARCH',
      contentVersion: 'v1',
      status: DragTreeNodeContentStatus.INITIALIZED,
      contentText: 'Initial content v1',
      metadata: { test: 'scenario1' },
    })

    addNodeContent(testNodeId, {
      contentId: contentId2,
      contentType: 'QUICK_RESEARCH',
      contentVersion: 'v1',
      status: DragTreeNodeContentStatus.INITIALIZED,
      contentText: 'Initial content v1',
      metadata: { test: 'scenario2' },
    })

    addTestResult('✅ Created initial v1 state for both contents')

    // Step 2: Simulate concurrent updates
    // Both operations start with the same v1 state intention
    addTestResult('🔄 Starting concurrent updates...')

    const update1Promise = new Promise<void>(resolve => {
      // Simulate update 1: v1 -> v2
      setTimeout(() => {
        addTestResult('📝 Update 1: Starting (intends v1 -> v2)')
        updateNodeContent(testNodeId, contentId1, {
          contentText: 'Updated content v2 from update1',
          status: DragTreeNodeContentStatus.PROCESSING,
          metadata: { test: 'scenario1', version: 'v2', updatedBy: 'update1' },
        })
        addTestResult('✅ Update 1: Completed')
        resolve()
      }, 10) // Small delay
    })

    const update2Promise = new Promise<void>(resolve => {
      // Simulate update 2: v1 -> v3
      setTimeout(() => {
        addTestResult('📝 Update 2: Starting (intends v1 -> v3)')
        updateNodeContent(testNodeId, contentId2, {
          contentText: 'Updated content v3 from update2',
          status: DragTreeNodeContentStatus.ACTIVE,
          metadata: { test: 'scenario2', version: 'v3', updatedBy: 'update2' },
        })
        addTestResult('✅ Update 2: Completed')
        resolve()
      }, 15) // Slightly different delay
    })

    // Wait for both updates to complete
    await Promise.all([update1Promise, update2Promise])

    // Step 3: Verify final state
    addTestResult('🔍 Checking final state...')

    const finalContent1 = getSpecificContent(testNodeId, contentId1)
    const finalContent2 = getSpecificContent(testNodeId, contentId2)

    addTestResult(
      `📊 Content 1 final: ${finalContent1?.contentText} (${finalContent1?.metadata?.version})`
    )
    addTestResult(
      `📊 Content 2 final: ${finalContent2?.contentText} (${finalContent2?.metadata?.version})`
    )

    // Step 4: Test the exact concurrent scenario with SAME contentId
    addTestResult('🧪 Testing same contentId concurrent updates...')

    const sameContentId = 'content-same'

    // Create initial content
    addNodeContent(testNodeId, {
      contentId: sameContentId,
      contentType: 'QUICK_RESEARCH',
      contentVersion: 'v1',
      status: DragTreeNodeContentStatus.INITIALIZED,
      contentText: 'Same content v1',
      metadata: { version: 'v1' },
    })

    // Try concurrent updates to SAME contentId
    const concurrentUpdate1 = new Promise<void>(resolve => {
      setTimeout(() => {
        addTestResult('🔄 Concurrent 1: Updating same contentId to v2')
        updateNodeContent(testNodeId, sameContentId, {
          contentText: 'Same content v2',
          metadata: { version: 'v2', updatedBy: 'concurrent1' },
        })
        resolve()
      }, 5)
    })

    const concurrentUpdate2 = new Promise<void>(resolve => {
      setTimeout(() => {
        addTestResult('🔄 Concurrent 2: Updating same contentId to v3')
        updateNodeContent(testNodeId, sameContentId, {
          contentText: 'Same content v3',
          metadata: { version: 'v3', updatedBy: 'concurrent2' },
        })
        resolve()
      }, 8)
    })

    await Promise.all([concurrentUpdate1, concurrentUpdate2])

    const finalSameContent = getSpecificContent(testNodeId, sameContentId)
    addTestResult(
      `🎯 Same contentId final: ${finalSameContent?.contentText} (${finalSameContent?.metadata?.version})`
    )
    addTestResult(`👤 Last updater: ${finalSameContent?.metadata?.updatedBy}`)

    addTestResult('✨ Test completed!')
    setIsRunning(false)
  }

  return (
    <div className="p-6 bg-gray-50 rounded-lg max-w-4xl">
      <h2 className="text-xl font-bold mb-4">🧪 Zustand Concurrency Test</h2>

      <div className="mb-4">
        <button
          onClick={testConcurrentUpdates}
          disabled={isRunning}
          className="px-4 py-2 bg-blue-500 text-white rounded disabled:opacity-50"
        >
          {isRunning ? 'Running Test...' : 'Run Concurrency Test'}
        </button>
      </div>

      <div className="mb-4">
        <h3 className="font-semibold mb-2">
          📊 Store State (Current nodeContent):
        </h3>
        <pre className="bg-white p-3 rounded text-xs overflow-auto max-h-32">
          {JSON.stringify(Object.fromEntries(nodeContent), null, 2)}
        </pre>
      </div>

      <div>
        <h3 className="font-semibold mb-2">📝 Test Results:</h3>
        <div className="bg-white p-3 rounded max-h-96 overflow-y-auto">
          {testResults.map((result, idx) => (
            <div key={idx} className="text-sm font-mono mb-1">
              {result}
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

export default ZustandConcurrencyTest

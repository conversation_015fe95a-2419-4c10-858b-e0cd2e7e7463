/**
 * Node type constants for the dragTree functionality
 * This file centralizes all node-related constants to avoid hardcoded values
 */

import type { DragTreeNodeType } from '@prisma/client'

// Frontend node types (lowercase) - used in TreeNode.type
// Renamed from NodeType to TreeNodeType for clarity
export enum TreeNodeType {
  CATEGORY = 'category',
  QUESTION = 'question',
}

// Re-export the Prisma enum for backend/API usage (uppercase)
// This replaces the previous ApiNodeType enum to avoid duplication
export type { DragTreeNodeType }

// Default labels for new nodes
export const DEFAULT_NODE_LABELS = {
  NEW_CATEGORY: 'New Category',
  NEW_QUESTION: 'New Question',
} as const

// Node type conversion utilities
export const treeNodeTypeToDragTreeType = (
  treeNodeType: TreeNodeType
): DragTreeNodeType => {
  return treeNodeType === TreeNodeType.CATEGORY ? 'CATEGORY' : 'QUESTION'
}

export const dragTreeTypeToTreeNodeType = (
  dragTreeType: DragTreeNodeType
): TreeNodeType => {
  return dragTreeType === 'CATEGORY'
    ? TreeNodeType.CATEGORY
    : TreeNodeType.QUESTION
}

// Type guards for better type safety
export const isCategory = (
  nodeType: string
): nodeType is TreeNodeType.CATEGORY => {
  return nodeType === TreeNodeType.CATEGORY
}

export const isQuestion = (
  nodeType: string
): nodeType is TreeNodeType.QUESTION => {
  return nodeType === TreeNodeType.QUESTION
}

// Union types for better TypeScript support
export type TreeNodeTypeUnion = `${TreeNodeType}`

// // Legacy exports for backward compatibility (to be removed in future)
// /** @deprecated Use TreeNodeType instead */
// export const NodeType = TreeNodeType;
// /** @deprecated Use DragTreeNodeType instead */
// export type ApiNodeType = DragTreeNodeType;
// /** @deprecated Use treeNodeTypeToDragTreeType instead */
// export const nodeTypeToApiType = treeNodeTypeToDragTreeType;
// /** @deprecated Use dragTreeTypeToTreeNodeType instead */
// export const apiTypeToNodeType = dragTreeTypeToTreeNodeType;

import { create } from 'zustand'
import { subscribeWithSelector } from 'zustand/middleware'

export type TabType = 'main' | 'research' | 'generate' | 'chat'

export type GenerationPhase = 'idle' | 'generating' | 'completed' | 'failed'

export interface Tab {
  id: string
  title: string
  fullTitle: string
  type: TabType
  nodeId?: string
  contentId?: string
  isClosable: boolean
  // AI pane specific data
  aiPaneData?: {
    type: 'generate' | 'chat'
    model: string
    prompt: string
    contextIds: string[]
    settings: Record<string, any>
    /**
     * For chat tabs, the persisted conversation ID in ai_conversations table.
     * Allows resuming conversations across sessions with correct pagination.
     */
    conversationId?: string
    // Asset-specific fields
    assetContent?: string
    assetId?: string
    /**
     * For chat assets, a list of messages to populate the chat UI.
     */
    assetMessages?: { role: 'user' | 'assistant'; content: string }[]
    /**
     * Generation phase tracking for robust state management.
     * Replaces simple boolean flag with explicit state machine.
     * Maps to DB AIGenerationStatus: idle→INITIALIZED, generating→GENERATING,
     * completed→ACTIVE, failed→INACTIVE
     */
    generationPhase?: GenerationPhase
    /**
     * Immediate flag to prevent duplicate generation starts (race condition protection)
     */
    generationStarted?: boolean
  }
}

/**
 * The lightweight representation of a tab that is persisted to localStorage.
 * Excludes heavy data like asset content or chat messages.
 */
type StoredTab = {
  id: string
  title: string
  fullTitle: string
  type: TabType
  nodeId?: string
  contentId?: string
  isClosable: boolean
  // Lightweight AI data for restoration. Heavy assets are fetched on-demand.
  aiPaneData?: {
    type: 'generate' | 'chat'
    model: string
    prompt: string
    contextIds: string[]
    settings: Record<string, any>
    conversationId?: string
    assetId?: string
    generationPhase?: GenerationPhase
  }
}

interface TabState {
  tabs: Tab[]
  activeTabId: string
  dragTreeId: string | null

  // Actions
  addTab: (tab: Omit<Tab, 'id'>) => string
  removeTab: (tabId: string) => void
  setActiveTab: (tabId: string) => void
  updateTabTitle: (tabId: string, title: string) => void
  reorderTabs: (oldIndex: number, newIndex: number) => void
  getActiveTab: () => Tab | undefined
  getTabByNodeId: (nodeId: string) => Tab | undefined
  initializeMainTab: () => void
  resetTabs: () => void

  // AI pane specific actions
  addAiTab: (aiData: {
    type: 'generate' | 'chat'
    model: string
    prompt: string
    contextIds: string[]
    settings: Record<string, any>
  }) => string

  // Asset specific actions
  addAssetTab: (asset: {
    id: string
    title: string
    content: string
    type: 'generate' | 'chat'
    model: string
    prompt: string
    contextIds: string[]
    messages?: { role: 'user' | 'assistant'; content: string }[]
  }) => string

  /**
   * Merge partial updates into the aiPaneData for a specific tab.
   * Useful to store additional metadata (e.g. assetId) after initial creation
   * without triggering duplicate tabs.
   */
  updateTabAiPaneData: (
    tabId: string,
    updates: Partial<Tab['aiPaneData']>
  ) => void

  /**
   * Atomically tries to start generation for a tab. Returns true if generation
   * was started successfully, false if already started.
   */
  tryStartGeneration: (tabId: string) => boolean

  // Persistence actions
  setDragTreeId: (dragTreeId: string) => void
  loadTabsFromLocalStorage: (dragTreeId: string) => void
  flushTabsToLocalStorage: () => void
}

const getStorageKey = (dragTreeId: string) => `clarifyai:tabs:${dragTreeId}`

export const useTabStore = create<TabState>()(
  subscribeWithSelector((set, get) => ({
    // Initialize with a default, non-closable "Main" tab so the tab bar is always visible
    tabs: [
      {
        id: 'main',
        title: 'Main',
        fullTitle: 'Main',
        type: 'main',
        isClosable: false,
      },
    ],
    activeTabId: 'main',
    dragTreeId: null,

    addTab: tabData => {
      const state = get()

      // Check if tab with same nodeId already exists
      if (tabData.nodeId) {
        const existingTab = state.tabs.find(
          tab => tab.nodeId === tabData.nodeId
        )
        if (existingTab) {
          // Switch to existing tab instead of creating duplicate
          set({ activeTabId: existingTab.id })
          return existingTab.id
        }
      }

      const newTab: Tab = {
        ...tabData,
        id: `${tabData.nodeId}-${tabData.contentId || 'tab'}`,
      }

      set(state => ({
        tabs: [...state.tabs, newTab],
        activeTabId: newTab.id,
      }))

      return newTab.id
    },

    removeTab: tabId => {
      const state = get()
      const tabIndex = state.tabs.findIndex(tab => tab.id === tabId)

      if (tabIndex === -1) return

      const tab = state.tabs[tabIndex]
      if (!tab.isClosable) return // Don't allow closing main tab

      const newTabs = state.tabs.filter(tab => tab.id !== tabId)
      let newActiveTabId = state.activeTabId

      // If we're removing the active tab, switch to another tab
      if (state.activeTabId === tabId) {
        if (newTabs.length > 0) {
          // Try to activate the previous tab, or the first tab if this was the first
          const newActiveIndex = Math.max(0, tabIndex - 1)
          newActiveTabId = newTabs[newActiveIndex]?.id || newTabs[0].id
        } else {
          newActiveTabId = ''
        }
      }

      set({
        tabs: newTabs,
        activeTabId: newActiveTabId,
      })
    },

    setActiveTab: tabId => {
      const state = get()
      const tabExists = state.tabs.some(tab => tab.id === tabId)

      if (tabExists) {
        set({ activeTabId: tabId })
      }
    },

    updateTabTitle: (tabId, title) => {
      set(state => ({
        tabs: state.tabs.map(tab =>
          tab.id === tabId ? { ...tab, title } : tab
        ),
      }))
    },

    reorderTabs: (oldIndex, newIndex) => {
      set(state => {
        // Ensure indices valid and not moving main (index 0)
        if (oldIndex === 0 || newIndex === 0) return state
        const tabsCopy = [...state.tabs]
        const [moved] = tabsCopy.splice(oldIndex, 1)
        tabsCopy.splice(newIndex, 0, moved)
        return { tabs: tabsCopy }
      })
    },

    getActiveTab: () => {
      const state = get()
      return state.tabs.find(tab => tab.id === state.activeTabId)
    },

    getTabByNodeId: nodeId => {
      const state = get()
      return state.tabs.find(tab => tab.nodeId === nodeId)
    },

    initializeMainTab: () => {
      const state = get()
      const mainTabExists = state.tabs.some(tab => tab.type === 'main')

      if (!mainTabExists) {
        const mainTab: Tab = {
          id: 'main',
          title: 'Main',
          fullTitle: 'Main',
          type: 'main',
          isClosable: false,
        }

        set({
          tabs: [mainTab],
          activeTabId: mainTab.id,
        })
      }
    },

    resetTabs: () => {
      console.log('🧹 [TabStore] Resetting tabs to main tab only')
      set({
        tabs: [
          {
            id: 'main',
            title: 'Main',
            fullTitle: 'Main',
            type: 'main',
            isClosable: false,
          },
        ],
        activeTabId: 'main',
      })
    },

    addAiTab: aiData => {
      const timestamp = new Date()
        .toISOString()
        .replace(/T/, '_')
        .replace(/:/g, '')
        .replace(/\..+/, '')
        .slice(0, 15) // Format: 20251111_123456

      const tabId = `ai-${aiData.type}-${Date.now()}`

      const newTab: Tab = {
        id: tabId,
        title: timestamp,
        fullTitle: `${aiData.type === 'generate' ? 'Generate' : 'Chat'} - ${timestamp}`,
        type: aiData.type,
        isClosable: true,
        aiPaneData: aiData,
      }

      set(state => ({
        tabs: [...state.tabs, newTab],
        activeTabId: newTab.id,
      }))

      return newTab.id
    },

    addAssetTab: asset => {
      const tabId = `asset-${asset.type}-${asset.id}`

      // Check if a tab for this asset already exists either by exact ID (asset-* prefix)
      // OR by having the same assetId in its aiPaneData (e.g. the original AI tab
      // that created the asset). This prevents opening duplicate tabs for the same
      // chat/generation session.
      const state = get()
      const existingTab = state.tabs.find(
        tab => tab.id === tabId || tab.aiPaneData?.assetId === asset.id
      )

      if (existingTab) {
        // Simply switch focus to the existing tab
        set({ activeTabId: existingTab.id })
        return existingTab.id
      }

      const newTab: Tab = {
        id: tabId,
        title: asset.title,
        fullTitle: `${asset.type === 'generate' ? 'Generate' : 'Chat'} - ${asset.title}`,
        type: asset.type,
        isClosable: true,
        aiPaneData: {
          type: asset.type,
          model: asset.model,
          prompt: asset.prompt,
          contextIds: asset.contextIds,
          settings: {},
          // If this is a chat asset, wire up the existing conversation ID so the
          // chat tab loads historical messages instead of initializing a new thread.
          ...(asset.type === 'chat' && { conversationId: asset.id }),
          // Add asset data for immediate display
          assetContent: asset.content,
          assetMessages: asset.messages,
          assetId: asset.id,
          // For generate assets mark as completed to skip streaming UI
          ...(asset.type === 'generate' && { generationPhase: 'completed' }),
        },
      }

      set(state => ({
        tabs: [...state.tabs, newTab],
        activeTabId: newTab.id,
      }))

      return newTab.id
    },

    updateTabAiPaneData: (tabId, updates) => {
      set(state => ({
        tabs: state.tabs.map(tab => {
          if (tab.id !== tabId || !tab.aiPaneData) return tab
          return {
            ...tab,
            aiPaneData: { ...tab.aiPaneData, ...updates },
          }
        }),
      }))
    },

    tryStartGeneration: tabId => {
      const state = get()
      const tab = state.tabs.find(t => t.id === tabId)

      // Check if tab exists and has aiPaneData
      if (!tab?.aiPaneData) return false

      // Check if generation is already started or in progress
      if (
        tab.aiPaneData.generationStarted ||
        tab.aiPaneData.generationPhase === 'generating'
      ) {
        return false
      }

      // Atomically set the generation started flag and phase
      set(state => ({
        tabs: state.tabs.map(t => {
          if (t.id !== tabId || !t.aiPaneData) return t
          return {
            ...t,
            aiPaneData: {
              ...t.aiPaneData,
              generationStarted: true,
              generationPhase: 'generating' as GenerationPhase,
            },
          }
        }),
      }))

      return true
    },

    setDragTreeId: dragTreeId => {
      set({ dragTreeId })
    },

    // --- LocalStorage Persistence ---

    loadTabsFromLocalStorage: dragTreeId => {
      // Prevent loading if we already have the correct state
      if (get().dragTreeId === dragTreeId) return

      console.log(
        `📖 [TabStore] Loading tabs from localStorage for tree: ${dragTreeId}`
      )
      try {
        const storageKey = getStorageKey(dragTreeId)
        const raw = localStorage.getItem(storageKey)
        if (raw) {
          const storedState = JSON.parse(raw)
          if (storedState.tabs && storedState.activeTabId) {
            set({
              tabs: storedState.tabs,
              activeTabId: storedState.activeTabId,
              dragTreeId,
            })
            console.log(
              '✅ [TabStore] Tabs loaded successfully from localStorage'
            )
            return
          }
        }
        // If no data found, reset to a clean state for the new tree
        console.log(
          '📝 [TabStore] No saved tabs found, initializing clean state.'
        )
        set({
          tabs: [
            {
              id: 'main',
              title: 'Main',
              fullTitle: 'Main',
              type: 'main',
              isClosable: false,
            },
          ],
          activeTabId: 'main',
          dragTreeId,
        })
      } catch (error) {
        console.error(
          '💥 [TabStore] Failed to load tabs from localStorage:',
          error
        )
        // Fallback to a clean state on error
        get().resetTabs()
        set({ dragTreeId })
      }
    },

    flushTabsToLocalStorage: () => {
      const { dragTreeId, tabs, activeTabId } = get()
      if (!dragTreeId) return

      try {
        // Serialize only lightweight tab data
        const tabsToStore: StoredTab[] = tabs.map(tab => ({
          id: tab.id,
          title: tab.title,
          fullTitle: tab.fullTitle,
          type: tab.type,
          nodeId: tab.nodeId,
          contentId: tab.contentId,
          isClosable: tab.isClosable,
          aiPaneData: tab.aiPaneData
            ? {
                type: tab.aiPaneData.type,
                model: tab.aiPaneData.model,
                prompt: tab.aiPaneData.prompt,
                contextIds: tab.aiPaneData.contextIds,
                settings: tab.aiPaneData.settings,
                conversationId: tab.aiPaneData.conversationId,
                assetId: tab.aiPaneData.assetId,
                generationPhase: tab.aiPaneData.generationPhase,
              }
            : undefined,
        }))

        const payload = {
          tabs: tabsToStore,
          activeTabId,
        }

        const storageKey = getStorageKey(dragTreeId)
        localStorage.setItem(storageKey, JSON.stringify(payload))
      } catch (error) {
        console.error(
          '💥 [TabStore] Failed to save tabs to localStorage:',
          error
        )
      }
    },
  }))
)

// --- Automatic Persistence ---

// Subscribe to state changes and flush to localStorage
useTabStore.subscribe(
  state => ({ tabs: state.tabs, activeTabId: state.activeTabId }),
  () => {
    useTabStore.getState().flushTabsToLocalStorage()
  }
)

// Flush once more on tab close/refresh to catch any pending changes
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', () => {
    useTabStore.getState().flushTabsToLocalStorage()
  })
}

import React from 'react'
import DescriptionInput from '@/app/(conv)/screening/components/DescriptionInput'
import ActionButtons from '@/app/(conv)/screening/components/ActionButtons'
import type { SupportedLanguageCode } from '@/app/(conv)/screening/constants/languages'

type CenteredInitialViewProps = {
  description: string
  setDescription: (value: string) => void
  isLoading: boolean
  showExamples: boolean
  useSimulator: boolean // Updated from useStaticData
  selectedLanguage: SupportedLanguageCode
  canStartClarification: boolean
  hasUserMadeSelection: boolean
  showUseMyInputButton: boolean
  onParaphraseAndAnalyze: () => void
  onStartClarification: () => void
  onToggleSimulator: (useSimulator: boolean) => void // Updated from onToggleStaticData
  onLanguageChange: (language: SupportedLanguageCode) => void
}

/**
 * Centered initial view component for when no results are available
 * Shows a focused input form in the center of the screen with proper spacing and header
 */
const CenteredInitialView: React.FC<CenteredInitialViewProps> = ({
  description,
  setDescription,
  isLoading,
  showExamples,
  useSimulator, // Updated from useStaticData
  selectedLanguage,
  canStartClarification,
  hasUserMadeSelection,
  // showUseMyInputButton: unused
  onParaphraseAndAnalyze,
  onStartClarification,
  onToggleSimulator, // Updated from onToggleStaticData
  onLanguageChange,
}) => {
  return (
    <section className="w-full max-w-5xl px-4">
      <article className="bg-white rounded-xl shadow-lg border border-slate-200 p-8">
        <header className="text-center mb-6">
          <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-2">
            Problem Clarification Assistant
          </h1>
          <p className="text-slate-600">
            Transform your ideas into clear, actionable problem statements
          </p>
        </header>

        <main className="space-y-6">
          <DescriptionInput
            description={description}
            setDescription={setDescription}
            isLoading={isLoading}
            showExamples={showExamples}
          />

          <ActionButtons
            description={description}
            isLoading={isLoading}
            useSimulator={useSimulator} // Updated from useStaticData
            selectedLanguage={selectedLanguage}
            canStartClarification={canStartClarification}
            hasUserMadeSelection={hasUserMadeSelection}
            hasRephrasedQuestions={false}
            onParaphraseAndAnalyze={onParaphraseAndAnalyze}
            onStartClarification={onStartClarification}
            onToggleSimulator={onToggleSimulator} // Updated from onToggleStaticData
            onLanguageChange={onLanguageChange}
          />
        </main>
      </article>
    </section>
  )
}

export default CenteredInitialView

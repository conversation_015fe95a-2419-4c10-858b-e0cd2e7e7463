import React from 'react'
import { cn } from '@/lib/utils'
import DescriptionInput from '@/app/(conv)/screening/components/DescriptionInput'
import ActionButtons from '@/app/(conv)/screening/components/ActionButtons'
import SuggestedRefinedQuestions from '@/app/(conv)/screening/components/SuggestedRefinedQuestions'
import ScreenWarning from '@/app/(conv)/screening/components/ScreenWarning'
import type { ScreenObjectType } from '@/app/(conv)/screening/constants/mockData'
import type { SupportedLanguageCode } from '@/app/(conv)/screening/constants/languages'

type ExpandedAnalysisViewProps = {
  description: string
  setDescription: (value: string) => void
  isLoading: boolean
  showExamples: boolean
  useSimulator: boolean // Updated from useStaticData
  selectedLanguage: SupportedLanguageCode
  canStartClarification: boolean
  hasUserMadeSelection: boolean
  showUseMyInputButton: boolean
  rephrasedDescriptions: string[]
  screenObject: ScreenObjectType | null
  selectedSuggestion: string
  onParaphraseAndAnalyze: () => void
  onStartClarification: () => void
  onSuggestionClick: (suggestion: string) => void
  onUseMyInput: () => void
  onToggleSimulator: (useSimulator: boolean) => void // Updated from onToggleStaticData
  onLanguageChange: (language: SupportedLanguageCode) => void
}

/**
 * Component for the expanded analysis view with suggestions and screen warnings
 * Displays a grid layout with input section, suggestions, and analysis panel
 */
const ExpandedAnalysisView: React.FC<ExpandedAnalysisViewProps> = ({
  description,
  setDescription,
  isLoading,
  showExamples,
  useSimulator, // Updated from useStaticData
  selectedLanguage,
  canStartClarification,
  hasUserMadeSelection,
  showUseMyInputButton,
  rephrasedDescriptions,
  screenObject,
  selectedSuggestion,
  onParaphraseAndAnalyze,
  onStartClarification,
  onSuggestionClick,
  onUseMyInput,
  onToggleSimulator, // Updated from onToggleStaticData
  onLanguageChange,
}) => {
  return (
    <section className="grid grid-cols-1 lg:grid-cols-3 gap-4 h-full">
      {/* Main Input and Suggestions Section */}
      <article className="lg:col-span-2 flex flex-col space-y-4">
        {/* Input Section */}
        <div
          className={cn(
            'bg-white rounded-xl shadow-sm border border-slate-200 p-4 flex-shrink-0 transition-all duration-300',
            {
              'ring-2 ring-emerald-400/40 border-emerald-400 shadow-emerald-100 shadow-lg':
                hasUserMadeSelection,
              'opacity-40': !hasUserMadeSelection,
            }
          )}
        >
          <DescriptionInput
            description={description}
            setDescription={setDescription}
            isLoading={isLoading}
            showExamples={showExamples}
            hasUserMadeSelection={hasUserMadeSelection}
          />

          <ActionButtons
            description={description}
            isLoading={isLoading}
            useSimulator={useSimulator} // Updated from useStaticData
            selectedLanguage={selectedLanguage}
            canStartClarification={canStartClarification}
            hasUserMadeSelection={hasUserMadeSelection}
            hasRephrasedQuestions={rephrasedDescriptions.length > 0}
            onParaphraseAndAnalyze={onParaphraseAndAnalyze}
            onStartClarification={onStartClarification}
            onToggleSimulator={onToggleSimulator} // Updated from onToggleStaticData
            onLanguageChange={onLanguageChange}
          />
        </div>

        {/* Suggestions Section */}
        {rephrasedDescriptions.length > 0 && (
          <div
            className={cn(
              'bg-white rounded-xl shadow-sm border border-slate-200 p-4 flex-1 overflow-auto transition-all duration-700',
              {
                'ring-4 ring-green-400 ring-opacity-30 gentle-glow':
                  !hasUserMadeSelection && rephrasedDescriptions.length > 0,
                'opacity-40': hasUserMadeSelection,
              }
            )}
          >
            <style jsx>{`
              @keyframes gentleGlow {
                0%,
                100% {
                  box-shadow:
                    0 0 0 4px rgba(34, 197, 94, 0.2),
                    0 0 20px rgba(34, 197, 94, 0.1);
                }
                50% {
                  box-shadow:
                    0 0 0 4px rgba(34, 197, 94, 0.4),
                    0 0 20px rgba(34, 197, 94, 0.2);
                }
              }
              .gentle-glow {
                animation: gentleGlow 4s ease-in-out infinite;
              }
            `}</style>
            <SuggestedRefinedQuestions
              suggestions={rephrasedDescriptions}
              onSuggestionClick={onSuggestionClick}
              onUseMyInput={onUseMyInput}
              showUseMyInputButton={showUseMyInputButton}
              isLoading={isLoading}
              hasUserMadeSelection={hasUserMadeSelection}
              selectedSuggestion={selectedSuggestion}
            />
          </div>
        )}
      </article>

      {/* Analysis Panel */}
      <aside
        className={cn(
          'lg:col-span-1 transition-opacity duration-300',
          hasUserMadeSelection ? 'opacity-40' : 'opacity-100'
        )}
      >
        {screenObject && (
          <div className="h-full">
            <ScreenWarning screenObject={screenObject} />
          </div>
        )}
      </aside>
    </section>
  )
}

export default ExpandedAnalysisView

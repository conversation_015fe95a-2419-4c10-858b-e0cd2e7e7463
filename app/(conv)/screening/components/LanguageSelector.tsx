import React, { useState } from 'react'
import { ChevronDown, Globe } from 'lucide-react'
import {
  LANGUAGE_OPTIONS,
  type SupportedLanguageCode,
} from '@/app/(conv)/screening/constants/languages'
import { cn } from '@/lib/utils'

type LanguageSelectorProps = {
  value: SupportedLanguageCode
  onChange: (language: SupportedLanguageCode) => void
  disabled?: boolean
  compact?: boolean
}

/**
 * Language Selector Component
 *
 * A modern dropdown component for selecting the preferred language
 * for drag tree generation. Features smooth animations and hover effects.
 */
const LanguageSelector: React.FC<LanguageSelectorProps> = ({
  value,
  onChange,
  disabled = false,
  compact = false,
}) => {
  const [isOpen, setIsOpen] = useState<boolean>(false)

  const selectedOption =
    LANGUAGE_OPTIONS.find(option => option.value === value) ||
    LANGUAGE_OPTIONS[0]

  const handleSelect = (language: SupportedLanguageCode): void => {
    onChange(language)
    setIsOpen(false)
  }

  const toggleDropdown = (): void => {
    if (!disabled) {
      setIsOpen(!isOpen)
    }
  }

  return (
    <div className="relative inline-block text-left group">
      {/* Custom Tooltip */}
      <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-900 text-white text-xs rounded-lg shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-150 ease-in-out z-50 whitespace-nowrap">
        Select language
        <br />
        <span className="text-yellow-300">(best effort, not guaranteed)</span>
        <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900"></div>
      </div>

      {/* Dropdown Button */}
      <button
        type="button"
        onClick={toggleDropdown}
        disabled={disabled}
        className={cn(
          'inline-flex items-center justify-between w-full font-medium bg-white border border-gray-300 rounded-lg shadow-sm transition-all duration-200 ease-in-out',
          {
            'opacity-50 cursor-not-allowed bg-gray-50': disabled,
            'hover:bg-gray-50 hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500':
              !disabled,
          },
          {
            'px-3 py-2 text-xs min-w-[160px]': compact,
            'px-4 py-2.5 text-sm min-w-[200px]': !compact,
          }
        )}
      >
        <div
          className={cn('flex items-center', {
            'space-x-2': compact,
            'space-x-3': !compact,
          })}
        >
          <Globe
            className={cn('text-gray-500', {
              'w-4 h-4': compact,
              'w-5 h-5': !compact,
            })}
          />
          <span className="text-gray-900">{selectedOption.label}</span>
        </div>
        <ChevronDown
          className={cn(
            'w-4 h-4 text-gray-500 transition-transform duration-200',
            {
              'rotate-180': isOpen,
            }
          )}
        />
      </button>

      {/* Dropdown Menu */}
      {isOpen && (
        <div className="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg">
          <div className="py-1">
            {LANGUAGE_OPTIONS.map(option => (
              <button
                key={option.value}
                onClick={() => handleSelect(option.value)}
                className={cn(
                  'flex items-center w-full text-left transition-colors duration-150 ease-in-out',
                  {
                    'bg-blue-50 text-blue-900 font-medium':
                      value === option.value,
                    'text-gray-900 hover:bg-gray-50': value !== option.value,
                  },
                  {
                    'px-3 py-2 text-xs': compact,
                    'px-4 py-2.5 text-sm': !compact,
                  }
                )}
              >
                <Globe
                  className={cn('text-gray-400', {
                    'w-3 h-3 mr-2': compact,
                    'w-4 h-4 mr-3': !compact,
                  })}
                />
                <span>{option.label}</span>
                {value === option.value && (
                  <svg
                    className="w-4 h-4 ml-auto text-blue-600"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                      clipRule="evenodd"
                    />
                  </svg>
                )}
              </button>
            ))}
          </div>
        </div>
      )}

      {/* Backdrop to close dropdown when clicking outside */}
      {isOpen && (
        <div className="fixed inset-0 z-40" onClick={() => setIsOpen(false)} />
      )}
    </div>
  )
}

export default LanguageSelector

/**
 * Supported Languages for Drag Tree Creation
 *
 * This file defines the available languages for drag tree generation
 * and provides utility functions for language handling.
 */

// Language codes enum
export const SUPPORTED_LANGUAGES = {
  EN: 'en',
  CN: 'cn',
  ES: 'es',
  JP: 'jp',
} as const

// Type for language codes
export type SupportedLanguageCode =
  (typeof SUPPORTED_LANGUAGES)[keyof typeof SUPPORTED_LANGUAGES]

// Language display names mapping
export const LANGUAGE_NAMES: Record<SupportedLanguageCode, string> = {
  [SUPPORTED_LANGUAGES.EN]: 'English',
  [SUPPORTED_LANGUAGES.CN]: '中文 (Chinese)',
  [SUPPORTED_LANGUAGES.ES]: 'Español (Spanish)',
  [SUPPORTED_LANGUAGES.JP]: '日本語 (Japanese)',
}

// Clear language names for LLM instructions (without mixed characters)
export const LLM_LANGUAGE_NAMES: Record<SupportedLanguageCode, string> = {
  [SUPPORTED_LANGUAGES.EN]: 'English',
  [SUPPORTED_LANGUAGES.CN]: 'Chinese',
  [SUPPORTED_LANGUAGES.ES]: 'Spanish',
  [SUPPORTED_LANGUAGES.JP]: 'Japanese',
}

// Language options for dropdown
export const LANGUAGE_OPTIONS = Object.entries(LANGUAGE_NAMES).map(
  ([code, name]) => ({
    value: code as SupportedLanguageCode,
    label: name,
  })
)

// Default language
export const DEFAULT_LANGUAGE: SupportedLanguageCode = SUPPORTED_LANGUAGES.EN

// Utility function to validate language code
export const isValidLanguageCode = (
  code: string
): code is SupportedLanguageCode => {
  return Object.values(SUPPORTED_LANGUAGES).includes(
    code as SupportedLanguageCode
  )
}

// Utility function to get language name for display
export const getLanguageName = (code: SupportedLanguageCode): string => {
  return LANGUAGE_NAMES[code] || LANGUAGE_NAMES[DEFAULT_LANGUAGE]
}

// Utility function to get clear language name for LLM instructions
export const getLLMLanguageName = (code: SupportedLanguageCode): string => {
  return LLM_LANGUAGE_NAMES[code] || LLM_LANGUAGE_NAMES[DEFAULT_LANGUAGE]
}

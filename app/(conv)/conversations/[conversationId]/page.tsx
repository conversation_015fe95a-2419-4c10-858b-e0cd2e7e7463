import getConversationById from "@/app/actions/getConversationById";
import getCurrentUser from "@/app/actions/getCurrentUser";
import Starter from "@/app/components/starter";
import { ConversationStatus } from "@prisma/client";
import MainIssueTree from "@/app/(conv)/conversations/[conversationId]/issue_tree/MainIssueTree";

type IParams = {
  conversationId: string;
};

const ConversationId: React.FC<{ params: Promise<IParams> }> = async ({
  params,
}) => {
  const { conversationId } = await params;
  const [conversation, currentUser] = await Promise.all([
    getConversationById(conversationId),
    getCurrentUser(),
  ]);

  if (!conversation) {
    return (
      <div className="h-full">
        <div className="h-full flex flex-col">
          <Starter currentUser={currentUser} />
        </div>
      </div>
    );
  }

  const hasPermission =
    currentUser?.id === conversation.creator_id ||
    currentUser?.email === "<EMAIL>" ||
    conversation.conversation_status === ConversationStatus.EXAMPLE;

  if (!hasPermission) {
    return (
      <h1 className="lg:pl-20 text-2xl">
        You have no permission to access this conversation
      </h1>
    );
  }

  // Render IssueTree component if user has permission
  return (
    <div className="lg:pl-10 h-screen">
      <MainIssueTree
        conversationId={conversation.id}
        currentUser={currentUser}
      />
    </div>
  );
};

export default ConversationId;

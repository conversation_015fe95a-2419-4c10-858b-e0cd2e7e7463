// components/ListItemSearchResultsDisplay.js
import React from "react";
import { Card, CardDescription } from "@/components/ui/card";
import Link from "next/link";
import { SearchResult } from "@/app/api/rag/utils";

type SearchResultsDisplayProps = {
  searchResults: SearchResult[];
};

const ListItemSearchResultsDisplay: React.FC<SearchResultsDisplayProps> = ({
  searchResults,
}) => {
  return (
    <>
      {searchResults.length > 0 && (
        <div className="grid md:grid-cols-4 sm:grid-cols-2 gap-4 mt-2">
          {searchResults.map((result, index) => (
            <Card key={index} className="w-full">
              <CardDescription className="m-2 overflow-ellipsis overflow-hidden">
                <Link
                  href={result.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-sm font-semibold block w-full line-clamp-3 hover:font-bold hover:underline hover:text-blue-700"
                >
                  {result.scripted_number + " " + result.title}
                </Link>
              </CardDescription>
            </Card>
          ))}
        </div>
      )}
    </>
  );
};

export default ListItemSearchResultsDisplay;

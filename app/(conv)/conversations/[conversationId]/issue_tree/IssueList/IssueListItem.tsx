// components/IssueListItem.js
import React, { useState, useEffect } from 'react'
import toast from 'react-hot-toast'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { usePathname } from 'next/navigation'
import { numberOfLinksToUse } from '@/app/configs'
import { ragAssistantRequestType } from '@/app/types/api'
import useIssueTreeStore from '@/app/stores/issuetree_store'
import ListItemSearchResultsDisplay from './ListItemSearchResultsDisplay'
import { useChat } from 'ai/react'
import { searchServiceProviders } from '.'
import mixpanel from '@/app/libs/mixpanel'
import { ConversationStatus } from '@prisma/client'
import { Message } from 'ai'
import { useConversationStore } from '@/app/stores/conversation_store'
import { Node } from '@/app/types'
import { SearchResult } from '@/app/api/rag/utils'
import { useSession } from 'next-auth/react'

// Component props type
type IssueListItemProps = {
  node: Node
  categories: string[]
  selected_search_service: string
}

const IssueListItem: React.FC<IssueListItemProps> = ({
  node,
  categories,
  selected_search_service,
}) => {
  // Hooks and store initialization
  const { data: session } = useSession()
  const issueTreeStore = useIssueTreeStore()
  const conversationStore = useConversationStore()
  const pathname = usePathname()
  const conversationId = pathname.split('/').pop()
  const currentNode = issueTreeStore.nodes.find(
    n => n.id === node.id && node.type === 'customLeafNode'
  )
  const [isSearchLoading, setIsSearchLoading] = useState<boolean>(false)

  // Derived state
  const shouldDisable =
    conversationStore.getConversationStatus(conversationId || '') ===
      ConversationStatus.COMPLETED ||
    conversationStore.getConversationStatus(conversationId || '') ===
      ConversationStatus.EXAMPLE
  const categoriesString = categories.slice(1).join(' / ')
  const nodeQuestion = node?.data?.label || ''
  const searchResults =
    issueTreeStore.searches
      .find(search => search.selected_node_id === node.id)
      ?.search_result.slice(0, numberOfLinksToUse) || []
  const ragResponse =
    issueTreeStore.rags.find(response => response.selected_node_id === node.id)
      ?.generation_output || ''
  const resolved = node?.data?.resolved
  const skipped = node?.data?.skipped

  const generateRequestData: ragAssistantRequestType = {
    userId: session?.user?.id || '',
    conversationId: conversationId!,
    issueTreeId: issueTreeStore.issueTreeId,
    selectedNodeId: node.id,
    originalAsk: issueTreeStore.originalAskText,
    nodeQuestion: nodeQuestion,
  }

  const { messages, append, data, isLoading } = useChat({
    api: '/api/rag/assistant',
    onError(error: any) {
      console.error('Error:', error)
      toast.error('An error occurred. Please try again.')
      setIsSearchLoading(false)
    },
    onFinish: (message: Message) => {
      toast.success('Finished Response Generation!', { duration: 2000 })
      setIsSearchLoading(false)

      // Set the RAG response
      issueTreeStore.setLeafNodeData(node.id, {
        resolved: true,
        example: message.content || '',
      })
    },
    body: generateRequestData,
  })

  const displayText = isLoading
    ? messages[messages.length - 1]?.content
    : currentNode?.data?.example || ''

  // Event handlers
  const handleDIYClick = () => {
    const url = searchServiceProviders.find(
      provider => provider.id === selected_search_service
    )?.url
    mixpanel.track('click_diy_search', { location: 'issue_list' })
    navigator.clipboard.writeText(prompt).then(() => {
      toast.success('Paste to your favorite search engine')
      setTimeout(() => window.open(url, '_blank'), 2000)
    })
  }

  const handleSearchClick = () => {
    // Check if conversationId and currentUser are set
    if (!conversationId || !session?.user?.id) {
      toast.error('Missing conversationId or currentUser', { duration: 2000 })
      setIsSearchLoading(false)
      return
    }

    // Check if RAG response already exists
    if (ragResponse) {
      toast.success('RAG response already exists', { duration: 2000 })
      console.log('RAG response:', ragResponse)
      issueTreeStore.setLeafNodeData(node.id, {
        resolved: true,
        example: ragResponse,
      })
      setIsSearchLoading(false)
      return
    }

    // Check if search results already exist
    if (searchResults.length > 0) {
      toast.success('Search results already exist', { duration: 2000 })
      setIsSearchLoading(false)
      return
    }

    setIsSearchLoading(true)
    mixpanel.track('click_search_for_you', { location: 'issue_list' })
    append({ role: 'user', content: 'Start the search!' })
  }

  // Effect to handle meta data from useChat
  useEffect(() => {
    // data is an array of objects from useChat where we pass metadata from API
    if (
      data &&
      Array.isArray(data) &&
      data.length > 0 &&
      typeof data[0] === 'object' &&
      data[0] !== null &&
      'searchResults' in data[0]
    ) {
      issueTreeStore.setSearchesAndRags(
        [
          ...issueTreeStore.searches,
          {
            conversationId: conversationId!,
            selected_node_id: node.id,
            search_result: data[0]['searchResults'] as SearchResult[],
          },
        ],
        issueTreeStore.rags
      )
    }
  }, [data, conversationId, issueTreeStore, node.id])

  const handleSkipClick = () =>
    issueTreeStore.setLeafNodeData(node.id, { resolved: true, skipped: true })
  const handleUnskipClick = () =>
    issueTreeStore.setLeafNodeData(node.id, {
      resolved: false,
      skipped: false,
    })
  const handleResolvedClick = () =>
    issueTreeStore.setLeafNodeData(node.id, { resolved: true })
  const handleUnresolvedClick = () =>
    issueTreeStore.setLeafNodeData(node.id, { resolved: false })
  const handleTextChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    issueTreeStore.setLeafNodeData(node.id, {
      resolved: true,
      example: e.target.value,
    })
  }

  const prompt = `As a team of elite consultants, we are solving this problem: ${issueTreeStore.originalAskText}

Please help to research this with data and facts to backup: ${nodeQuestion}
  `

  // Render component
  return (
    <article className="p-6 mb-6 bg-white shadow-lg rounded-lg border border-gray-200 transition-all duration-300 hover:shadow-xl">
      <div className="flex flex-col lg:flex-row gap-6">
        <div className="flex-grow lg:w-3/4">
          <p className="text-sm font-medium text-gray-500 mb-2">
            {categoriesString}
          </p>
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            {nodeQuestion}
          </h3>
          <Textarea
            placeholder="Type here..."
            value={displayText}
            rows={6}
            onChange={handleTextChange}
            className={`w-full border rounded-lg p-3 text-sm transition-colors duration-200 ${
              resolved
                ? 'bg-green-50 border-green-200'
                : 'bg-red-50 border-red-200'
            } ${skipped ? 'line-through' : ''}`}
            disabled={isLoading || shouldDisable}
          />
          <ListItemSearchResultsDisplay searchResults={searchResults} />
        </div>
        <div className="lg:w-1/4">
          <div className="flex flex-col gap-3">{renderButtons()}</div>
        </div>
      </div>
    </article>
  )

  // Helper function to render buttons
  function renderButtons() {
    return (
      <>
        {isSearchLoading ? renderLoadingIndicator() : renderSearchButton()}
        {renderDIYButton()}
        {renderSkipButton()}
        {renderResolveButton()}
      </>
    )
  }

  function renderLoadingIndicator() {
    return (
      <div className="flex items-center justify-center space-x-2 w-full py-2">
        <div className="h-3 w-3 animate-bounce rounded-full bg-green-500 [animation-delay:-0.3s]"></div>
        <div className="h-3 w-3 animate-bounce rounded-full bg-green-500 [animation-delay:-0.15s]"></div>
        <div className="h-3 w-3 animate-bounce rounded-full bg-green-500"></div>
      </div>
    )
  }

  function renderSearchButton() {
    return (
      <Button
        onClick={handleSearchClick}
        className="w-full text-sm py-2 bg-green-100 text-green-700 rounded-md hover:bg-green-200 focus:ring-2 focus:ring-green-300 transition-all duration-200"
        disabled={isLoading || shouldDisable}
      >
        <span className="hidden sm:inline">Let me search that for you</span>
        <span className="sm:hidden">Search</span>
      </Button>
    )
  }

  function renderDIYButton() {
    return (
      <Button
        onClick={handleDIYClick}
        className="w-full text-sm py-2 bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200 focus:ring-2 focus:ring-blue-300 transition-all duration-200"
        disabled={isLoading}
      >
        <span className="hidden sm:inline">
          DIY using Free{' '}
          {searchServiceProviders.find(sp => sp.id === selected_search_service)
            ?.display_text || 'Search Service'}
        </span>
        <span className="sm:hidden">DIY</span>
      </Button>
    )
  }

  function renderSkipButton() {
    return (
      <Button
        onClick={skipped ? handleUnskipClick : handleSkipClick}
        className={`w-full text-sm py-2 rounded-md focus:ring-2 transition-all duration-200 ${
          skipped
            ? 'bg-purple-100 text-purple-700 hover:bg-purple-200 focus:ring-purple-300'
            : 'bg-yellow-100 text-yellow-700 hover:bg-yellow-200 focus:ring-yellow-300'
        }`}
        disabled={isLoading || shouldDisable}
      >
        {skipped ? 'Unskip' : 'Mark Irrelevant'}
      </Button>
    )
  }

  function renderResolveButton() {
    return (
      <Button
        onClick={resolved ? handleUnresolvedClick : handleResolvedClick}
        className={`w-full text-sm py-2 rounded-md focus:ring-2 transition-all duration-200 ${
          resolved
            ? 'bg-orange-100 text-orange-700 hover:bg-orange-200 focus:ring-orange-300'
            : 'bg-gray-100 text-gray-700 hover:bg-gray-200 focus:ring-gray-300'
        }`}
        disabled={isLoading || shouldDisable}
      >
        {resolved ? 'Mark as Need Review' : 'Mark as Reviewed'}
      </Button>
    )
  }
}

export default IssueListItem

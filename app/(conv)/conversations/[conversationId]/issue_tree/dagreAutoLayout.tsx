import { Node, Edge } from "@/app/types";
var dagre = require("dagre");

export const getDagreLayoutedElements = (nodes: Node[], edges: Edge[]) => {
  const graph = new dagre.graphlib.Graph();
  graph.setGraph({
    rankdir: "LR",
    ranksep: 100, // Increased separation between different layers
    nodesep: 30, // Separation between nodes within the same layer
    edgesep: 20, // Separation between edges
    marginx: 20, // Horizontal margin
    marginy: 20, // Vertical margin
    acyclicer: "greedy", // Can help with reducing edge crossings
    ranker: "network-simplex", // Often provides good results for trees
  });

  graph.setDefaultEdgeLabel(() => ({}));

  // Function to calculate node dimensions
  const getNodeDimensions = (node: Node) => {
    const baseWidth = 120; // Slightly reduced base width
    const baseHeight = 40; // Slightly reduced base height
    const lineHeight = 18; // Slightly reduced line height

    let width = baseWidth;
    let height = baseHeight;

    if (node.data.label) {
      const lines = node.data.label.split("\n");
      width *= 1;
      height += lines.length * lineHeight;
    }

    if (node.data.example) {
      width *= 2.5;
    }

    if (node.type === "customLeafNode") {
      height = Math.max(height, 200); // Minimum height for custom leaf nodes
    }

    // Add padding
    width += 20;
    height += 20;

    return { width, height };
  };

  // Set nodes with calculated dimensions
  nodes.forEach((node) => {
    const { width, height } = getNodeDimensions(node);
    graph.setNode(node.id, { width, height });
  });

  // Set edges
  edges.forEach((edge) => {
    graph.setEdge(edge.source, edge.target, {
      minlen: 1, // Minimum edge length
      weight: 1, // Edge weight for layout calculations
    });
  });

  // Run the layout
  dagre.layout(graph);

  // Extract the laid out elements
  const layoutedNodes = nodes.map((node) => {
    const { x, y } = graph.node(node.id);
    return {
      ...node,
      position: { x, y },
    };
  });

  return { nodes: layoutedNodes, edges };
};

// export const getDagreLayoutedElements = (nodes: Node[], edges: Edge[]) => {
//   const graph = new dagre.graphlib.Graph();
//   graph.setGraph({
//     rankdir: "LR",
//     ranksep: 100, // Increased separation between different layers
//     nodesep: 70, // Increased separation between nodes within the same layer
//   });
//   graph.setDefaultEdgeLabel(() => ({}));

//   nodes.forEach((node) => {
//     // Calculate height dynamically, for example, based on the label length
//     // This is a simplistic approach; you might need a more sophisticated calculation
//     let height = Math.max(50, 10 + node.data.label.length * 0.5);
//     let width = 150; // Consider adjusting width similarly if needed

//     if (node.type === "customLeafNode") {
//       height = Math.max(height, 100); // Ensure custom leaf nodes have at least 100px height
//     }

//     graph.setNode(node.id, { width: width, height: height });
//   });

//   edges.forEach((edge) => {
//     graph.setEdge(edge.source, edge.target);
//   });

//   dagre.layout(graph);

//   const layoutedNodes = nodes.map((node) => {
//     const { x, y } = graph.node(node.id);
//     return {
//       ...node,
//       position: { x, y },
//     };
//   });

//   return { nodes: layoutedNodes, edges };
// };

// OLD implementation, keep just in case
// export const getDagreLayoutedElements = (nodes: Node[], edges: Edge[]) => {
//   const graph = new dagre.graphlib.Graph();
//   graph.setDefaultEdgeLabel(() => ({}));
//   graph.setGraph({ rankdir: "LR", ranksep: 100 });

//   nodes.forEach((node) => {
//     let height = 50; // Default height
//     if (node.type === "customLeafNode") {
//       height = 100; // Custom height for custom nodes
//     }
//     // if (node.data?.example?.length > 300) {
//     //   height += 100 * (node.data?.example?.length / 100);
//     // }
//     graph.setNode(node.id, { width: 150, height: height });
//   });

//   edges.forEach((edge) => {
//     graph.setEdge(edge.source, edge.target);
//   });

//   dagre.layout(graph);

//   const layoutedNodes = graph.nodes().map((node: string) => {
//     const { x, y } = graph.node(node);
//     return {
//       ...nodes.find((n) => n.id === node),
//       position: { x, y },
//     };
//   });

//   return { nodes: layoutedNodes, edges };
// };

import { Dialog, DialogContent, DialogTitle } from '@/components/ui/dialog'
import { ChangeEvent, useState, useEffect, useCallback } from 'react'
import useIssueTreeStore from '@/app/stores/issuetree_store'
import mixpanel from '@/app/libs/mixpanel'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import toast from 'react-hot-toast'
import { numberOfLinksToUse } from '@/app/configs'
import { usePathname } from 'next/navigation'
import { ragAssistantRequestType } from '@/app/types/api'
import { useChat } from 'ai/react'
import SearchResultsDisplay from './SearchResultsDisplay'
import { Message } from 'ai'
import useCheckDisabledConversation from '@/app/hooks/useCheckDisabledConversation'
import { ChevronLeft, ChevronRight, X, Check } from 'lucide-react'
import { motion, AnimatePresence } from 'framer-motion'
import { LoadingIndicator } from './LoadingIndicator'
import { SearchResult } from '@/app/api/rag/utils'
import { useSession } from 'next-auth/react'

const Component: React.FC = () => {
  const issueTreeStore = useIssueTreeStore()
  const { data: session } = useSession()
  const pathname = usePathname()
  const shouldDisable = useCheckDisabledConversation()

  const [isSearchLoading, setIsSearchLoading] = useState<boolean>(false)
  const [direction, setDirection] = useState<'left' | 'right'>('right')

  const id = issueTreeStore.selectedNode?.selectedNodeId || ''
  const conversationId = pathname.split('/').pop()
  const currentNode = issueTreeStore.nodes.find(
    node => node.id === id && node.type === 'customLeafNode'
  )

  const nodeQuestion = currentNode?.data?.label || ''
  const isResolved = currentNode?.data?.resolved || false
  const searchResults =
    issueTreeStore.searches
      .find(search => search.selected_node_id === id)
      ?.search_result.slice(0, numberOfLinksToUse) || []
  const ragResponse =
    issueTreeStore.rags.find(response => response.selected_node_id === id)
      ?.generation_output || ''

  const prompt = `As a team of elite consultants, we are solving this problem: ${issueTreeStore.originalAskText}

Please help to research this with data and facts to backup: ${nodeQuestion}`

  const copilotUrl = 'https://copilot.microsoft.com/'

  const generateRequestData: ragAssistantRequestType = {
    userId: session?.user?.id || '',
    conversationId: conversationId!,
    issueTreeId: issueTreeStore.issueTreeId,
    selectedNodeId: id,
    originalAsk: issueTreeStore.originalAskText,
    nodeQuestion: nodeQuestion,
  }

  const { messages, append, data, isLoading } = useChat({
    api: '/api/rag/assistant',
    onError(error: any) {
      console.error('Error:', error)
      toast.error('An error occurred. Please try again.')
      setIsSearchLoading(false)
    },
    onFinish: (message: Message) => {
      toast.success('Finished Response Generation!', { duration: 2000 })
      setIsSearchLoading(false)
      issueTreeStore.setLeafNodeData(id, {
        resolved: true,
        example: message.content || '',
      })
    },
    body: generateRequestData,
  })

  const displayText = isLoading
    ? messages[messages.length - 1]?.content
    : currentNode?.data?.example || ''

  const handleLetMeSearchThatForYou = () => {
    if (!conversationId || !session?.user?.id) {
      toast.error('Missing conversationId or currentUser', { duration: 2000 })
      setIsSearchLoading(false)
      return
    }

    if (ragResponse) {
      toast.success('RAG response already exists', { duration: 2000 })
      console.log('RAG response:', ragResponse)
      issueTreeStore.setLeafNodeData(id, {
        resolved: true,
        example: ragResponse,
      })
      setIsSearchLoading(false)
      return
    }

    if (searchResults.length > 0) {
      toast.success('Search results already exist', { duration: 2000 })
      setIsSearchLoading(false)
      return
    }

    setIsSearchLoading(true)
    mixpanel.track('click_search_for_you', {
      location: 'issue_node_dialog',
    })
    append({ role: 'user', content: 'Start the search!' })
  }

  useEffect(() => {
    // data is an array of objects from useChat where we pass metadata from API
    if (
      data &&
      Array.isArray(data) &&
      data.length > 0 &&
      typeof data[0] === 'object' &&
      data[0] !== null &&
      'searchResults' in data[0]
    ) {
      issueTreeStore.setSearchesAndRags(
        [
          ...issueTreeStore.searches,
          {
            conversationId: conversationId!,
            selected_node_id: id,
            search_result: data[0]['searchResults'] as SearchResult[],
          },
        ],
        issueTreeStore.rags
      )
    }
  }, [data, conversationId, id, issueTreeStore])

  const handleTextChange = (e: ChangeEvent<HTMLTextAreaElement>) => {
    issueTreeStore.setLeafNodeData(id, {
      resolved: true,
      example: e.target.value,
    })
  }

  const handleCopyPrompt = (openNewWindow: boolean = false) => {
    navigator.clipboard.writeText(prompt).then(() => {
      toast.success('Copied to clipboard!')
      mixpanel.track('click_diy_search', {
        location: 'issue_node_dialog',
        open_new_window: openNewWindow,
      })
      if (openNewWindow) {
        setTimeout(() => window.open(copilotUrl, '_blank'), 2000)
      }
    })
  }

  const handleNavigateNode = useCallback(
    (newDirection: 'prev' | 'next') => {
      const leafNodes = issueTreeStore.nodes.filter(
        node => node.type === 'customLeafNode'
      )
      const currentIndex = leafNodes.findIndex(node => node.id === id)
      let newIndex: number

      if (newDirection === 'prev') {
        newIndex = (currentIndex - 1 + leafNodes.length) % leafNodes.length
        setDirection('left')
      } else {
        newIndex = (currentIndex + 1) % leafNodes.length
        setDirection('right')
      }

      const newNodeId = leafNodes[newIndex].id
      issueTreeStore.setSelectedNode(newNodeId)
    },
    [issueTreeStore, id]
  )

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Check if the active element is a textarea
      if (document.activeElement?.tagName === 'TEXTAREA') {
        return // Exit the function if focus is in a textarea
      }

      if (event.key === 'ArrowLeft') {
        handleNavigateNode('prev')
      } else if (event.key === 'ArrowRight') {
        handleNavigateNode('next')
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => {
      window.removeEventListener('keydown', handleKeyDown)
    }
  }, [handleNavigateNode])

  const variants = {
    enter: (direction: 'left' | 'right') => ({
      x: direction === 'right' ? 50 : -50,
      opacity: 0,
    }),
    center: {
      x: 0,
      opacity: 1,
    },
    exit: (direction: 'left' | 'right') => ({
      x: direction === 'right' ? -50 : 50,
      opacity: 0,
    }),
  }

  const handleReviewToggle = () => {
    issueTreeStore.setLeafNodeData(id, {
      resolved: !isResolved,
      skipped: isResolved ? false : currentNode?.data?.skipped, // Set skipped to false if marking as "Needs Review"
    })
  }

  const handleIrrelevantToggle = () => {
    const newSkippedState = !currentNode?.data?.skipped
    issueTreeStore.setLeafNodeData(id, {
      skipped: newSkippedState,
      resolved: newSkippedState || isResolved, // Mark as resolved if skipped
    })
  }

  return (
    <Dialog
      open={issueTreeStore.selectedNode?.isOpen}
      onOpenChange={issueTreeStore.closeSelectedNode}
    >
      <DialogContent
        className="lg:max-w-[1000px] sm:max-w-[625px] p-6 overflow-hidden"
        autoFocus={false} // Add this line to prevent auto-focus
      >
        <div className="flex items-center space-x-4" tabIndex={-1}>
          {' '}
          {/* Add tabIndex={-1} here */}
          <NavigationButton
            direction="prev"
            onClick={() => handleNavigateNode('prev')}
          />
          <AnimatePresence mode="wait" initial={false} custom={direction}>
            <motion.div
              key={id}
              custom={direction}
              variants={variants}
              initial="enter"
              animate="center"
              exit="exit"
              transition={{
                x: { type: 'spring', stiffness: 300, damping: 30 },
                opacity: { duration: 0.2 },
              }}
              style={{ flex: 1 }}
            >
              <DialogHeader
                id={id}
                nodeQuestion={nodeQuestion}
                isResolved={isResolved}
                currentNode={currentNode}
              />
              <ActionButtons
                isSearchLoading={isSearchLoading}
                handleLetMeSearchThatForYou={handleLetMeSearchThatForYou}
                handleCopyPrompt={handleCopyPrompt}
                shouldDisable={shouldDisable}
              />
              <Textarea
                placeholder="type here..."
                value={displayText || ''}
                rows={8}
                onChange={handleTextChange}
                className={`w-full border rounded-md ${
                  isResolved ? 'border-green-500' : 'border-yellow-500'
                } ${currentNode?.data?.skipped ? 'line-through' : ''}`}
                disabled={isLoading || shouldDisable}
              />
              <ToggleButtons
                id={id}
                isResolved={isResolved}
                currentNode={currentNode}
                handleReviewToggle={handleReviewToggle}
                handleIrrelevantToggle={handleIrrelevantToggle}
                shouldDisable={shouldDisable}
              />
              <SearchResultsDisplay searchResults={searchResults} />
            </motion.div>
          </AnimatePresence>
          <NavigationButton
            direction="next"
            onClick={() => handleNavigateNode('next')}
          />
        </div>
      </DialogContent>
    </Dialog>
  )
}

const NavigationButton: React.FC<{
  direction: 'prev' | 'next'
  onClick: () => void
}> = ({ direction, onClick }) => (
  <Button
    onClick={onClick}
    className="rounded-full bg-gray-200 bg-opacity-50 hover:bg-opacity-75 transition-all duration-200"
    variant="ghost"
    size="icon"
  >
    {direction === 'prev' ? (
      <ChevronLeft className="h-4 w-4 text-gray-600" />
    ) : (
      <ChevronRight className="h-4 w-4 text-gray-600" />
    )}
  </Button>
)

const DialogHeader: React.FC<{
  id: string
  nodeQuestion: string
  isResolved: boolean
  currentNode: any
}> = ({ id, nodeQuestion, isResolved, currentNode }) => (
  <>
    <DialogTitle className="rounded p-2 text-zinc-400 text-xs flex items-center justify-between">
      <span>Node {id}</span>
      <span
        className={`text-sm font-normal ${
          isResolved || currentNode?.data?.skipped
            ? 'text-green-500'
            : 'text-yellow-500'
        }`}
      >
        {isResolved || currentNode?.data?.skipped ? 'Reviewed' : 'Needs Review'}
      </span>
    </DialogTitle>
    <DialogTitle
      className={`rounded p-2 ${
        currentNode?.data?.skipped ? 'line-through' : ''
      }`}
    >
      {nodeQuestion}
    </DialogTitle>
  </>
)

const ActionButtons: React.FC<{
  isSearchLoading: boolean
  handleLetMeSearchThatForYou: () => void
  handleCopyPrompt: (openNewWindow: boolean) => void
  shouldDisable: boolean
}> = ({
  isSearchLoading,
  handleLetMeSearchThatForYou,
  handleCopyPrompt,
  shouldDisable,
}) => (
  <div className="flex flex-row p-2 justify-between sm:text-xs">
    {isSearchLoading ? (
      <LoadingIndicator />
    ) : (
      <Button
        onClick={handleLetMeSearchThatForYou}
        disabled={isSearchLoading || shouldDisable}
      >
        Let Me Search That For You
      </Button>
    )}
    <div className="flex flex-row justify-end space-x-2">
      <Button onClick={() => handleCopyPrompt(true)}>
        DIY: Search with Copilot
      </Button>
    </div>
  </div>
)

const ToggleButtons: React.FC<{
  id: string
  isResolved: boolean
  currentNode: any
  handleReviewToggle: () => void
  handleIrrelevantToggle: () => void
  shouldDisable: boolean
}> = ({
  id: _id,
  isResolved,
  currentNode,
  handleReviewToggle,
  handleIrrelevantToggle,
  shouldDisable,
}) => (
  <div className="flex justify-end space-x-2 mt-2 mb-2">
    <Button
      onClick={handleReviewToggle}
      className={`text-xs px-2 py-1 w-28 h-8 flex items-center justify-center ${
        isResolved
          ? 'bg-orange-200 hover:bg-orange-300'
          : 'bg-zinc-200 hover:bg-zinc-300'
      } text-zinc-700 rounded-full`}
      disabled={shouldDisable}
    >
      {isResolved ? (
        <X className="w-3 h-3 mr-1" />
      ) : (
        <Check className="w-3 h-3 mr-1" />
      )}
      <span>{isResolved ? 'Mark as Needs Review' : 'Mark as Reviewed'}</span>
    </Button>
    <Button
      onClick={handleIrrelevantToggle}
      className={`text-xs px-2 py-1 w-28 h-8 flex items-center justify-center ${
        currentNode?.data?.skipped
          ? 'bg-purple-200 hover:bg-purple-300'
          : 'bg-yellow-200 hover:bg-yellow-300'
      } text-zinc-700 rounded-full`}
      disabled={shouldDisable}
    >
      {currentNode?.data?.skipped ? (
        <Check className="w-3 h-3 mr-1" />
      ) : (
        <X className="w-3 h-3 mr-1" />
      )}
      <span>
        {currentNode?.data?.skipped ? 'Mark as Relevant' : 'Mark as Irrelevant'}
      </span>
    </Button>
  </div>
)

export default Component

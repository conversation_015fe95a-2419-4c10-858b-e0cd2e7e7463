import { useEffect, useState, useRef } from 'react'
import { Panel, useReactFlow, useStore<PERSON><PERSON> } from 'reactflow'
import { ChevronsUpDown } from 'lucide-react'
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible'

// The top left node navigation
const FlowControls: React.FC = () => {
  const store = useStoreApi()
  const [isCollapsibleOpen, setCollapsibleOpen] = useState(false)
  const [buttons, setButtons] = useState<React.JSX.Element[] | null>(null)

  const { setCenter } = useReactFlow()
  // There is a weird bug that took my whole night
  // setCenter from hook is alright, but suddenly in runtime it becomes ()=>{}
  // Therefore, workaround by saving the working obj using useRef
  const setCenterRef = useRef(setCenter)
  useEffect(() => {
    if (setCenter && typeof setCenter === 'function') {
      setCenterRef.current = setCenter
    }
  }, [setCenter])

  // This is ugly fix for top left node navigation
  // There is a race condition, if FlowControls get state before the state
  // exists in ReactFlow [it has the data, but undefined width & height], then
  // it cannot render. Use this useEffect tgt with interval to check the
  // state until buttons are rendered
  useEffect(() => {
    let intervalId: NodeJS.Timeout

    const checkNodes = () => {
      //   console.log("Hit checkNodes");
      const { nodeInternals } = store.getState()
      const nodes = Array.from(nodeInternals).map(([, node]) => node)
      if (nodes.length > 0 && nodes.some(node => node?.width && node?.height)) {
        // If at least one node exists and has defined dimensions, render buttons
        const newButtons = renderButtons(nodes)
        setButtons(newButtons)

        // Clear the interval if we've found a node with dimensions
        if (intervalId) {
          //   console.log("Hit clear interval");
          clearInterval(intervalId)
        }
      }
    }

    // Run immediately
    checkNodes()

    // Run every 500ms
    intervalId = setInterval(checkNodes, 500)

    return () => {
      // Cleanup interval on component unmount
      if (intervalId) {
        clearInterval(intervalId)
      }
    }
  }, [store]) // Include store in dependencies

  const focusNode = (node: any) => {
    // The questions are right side of the node, move toward right
    // instead of just centering it
    console.log('run', node)
    const x = node?.position?.x + 300 + (node?.width ?? 0) / 2
    const y = node?.position?.y + (node?.height ?? 0) / 2
    const zoom = 1
    console.log('Calculated x, y, and zoom:', x, y, zoom)
    if (x !== undefined && y !== undefined) {
      console.log('Setting center')
      setCenterRef.current(x, y, { zoom, duration: 1000 })
    }
    // Close the collapsible
    setCollapsibleOpen(false)
  }

  type Node = any
  const renderButtons = (nodes: Node[]) => {
    // Filter out custom leaf nodes
    const nonLeafNodes = nodes.filter(node => node?.type !== 'customLeafNode')

    // Sort the nodes first by layer in ascending order, then by ID in ascending order.
    const sortedNodes = nonLeafNodes.sort((a, b) => {
      const aLayer = parseInt(a.id.split(':')[0].replace('L', ''))
      const bLayer = parseInt(b.id.split(':')[0].replace('L', ''))
      const aId = parseInt(a.id.split(':')[1])
      const bId = parseInt(b.id.split(':')[1])

      if (aLayer < bLayer) return -1
      if (aLayer > bLayer) return 1
      if (aId < bId) return -1
      if (aId > bId) return 1

      return 0
    })

    // Group nodes by layer
    const groupedNodes: { [key: string]: Node[] } = {}
    sortedNodes.forEach(node => {
      const layer = node.id.split(':')[0]
      if (!groupedNodes[layer]) {
        groupedNodes[layer] = []
      }
      groupedNodes[layer].push(node)
    })

    // Render buttons with layer separators
    return Object.keys(groupedNodes)
      .sort((a, b) => {
        // Sort in descending order of layer number
        const aLayer = parseInt(a.replace('L', ''), 10)
        const bLayer = parseInt(b.replace('L', ''), 10)
        return bLayer - aLayer
      })
      .map((layer, index) => (
        <div key={index} className="flex flex-col">
          <div>
            {layer && layer.startsWith('L')
              ? layer.replace('L', 'Level ')
              : 'Level'}
          </div>
          <div className="flex flex-col">
            {groupedNodes[layer].map((node, nodeIndex) => (
              <button
                key={nodeIndex}
                onClick={() => focusNode(node)}
                className="text-xs p-2 rounded bg-blue-100 hover:bg-blue-200 bg-opacity-50 text-zinc-500 m-1"
              >
                {node?.data?.label}
              </button>
            ))}
          </div>
        </div>
      ))
  }

  return (
    <Panel position="top-left">
      <Collapsible open={isCollapsibleOpen}>
        <CollapsibleTrigger
          className="flex items-center text-s p-2 rounded bg-green-200 hover:bg-green-300 bg-opacity-50 text-zinc-500 m-1"
          onClick={() => setCollapsibleOpen(!isCollapsibleOpen)}
        >
          Quick Navigation
          <ChevronsUpDown className="h-4 w-4 ml-2" />
        </CollapsibleTrigger>
        <CollapsibleContent>
          <div
            className="flex flex-col items-start overflow-y-auto hover:border hover:border-gray-300"
            style={{ maxHeight: '450px' }}
          >
            {buttons || 'Loading...'}
          </div>
        </CollapsibleContent>
      </Collapsible>
    </Panel>
  )
}

export default FlowControls

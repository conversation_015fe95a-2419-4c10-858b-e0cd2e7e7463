import { unified } from 'unified'
import remarkParse from 'remark-parse'
import { Position } from 'reactflow'
import { Node, Edge } from '@/app/types'

export interface TextNode {
  type: 'text'
  value: string
}

interface ParentNode {
  type: 'parent'
  children: RecursiveNode[]
}

type RecursiveNode = TextNode | ParentNode

// when we hit list, start to recursively collect text nodes
// eg:
// - **example**: abc
// It will break down to example [strong] and abc [text]
// We recursively collect the text nodes to get the full text
const collectTextNodes = (node: RecursiveNode): string => {
  if (node.type === 'text') {
    return node.value
  }

  if (node.children) {
    return node.children.map(collectTextNodes).join('')
  }

  return ''
}

const createNode = (
  depth: number,
  counter: number,
  yOffset: number,
  label: string,
  type = 'default'
): Node => ({
  id: `L${depth}:${counter}`,
  data: { label },
  position: { x: depth * 200, y: yOffset * 100 },
  sourcePosition: Position.Right,
  targetPosition: Position.Left,
  type,
})

const createEdge = (source: string, target: string): Edge => ({
  id: `e-${source}-${target}`,
  source,
  target,
})

// Only want markdown lines that start with # or -
// Hardcoded for now, because initial_questions_generation prompt should give
// us the markdown text with # or - for happy flow
const filterMarkdown = (markdown: string) => {
  return markdown
    .split('\n') // Split the string into lines
    .filter(line => line.startsWith('#') || line.startsWith('-')) // Filter out lines that don't start with # or -
    .join('\n') // Join the remaining lines back into a single string
}

const markdownToReactFlow = (markdown: string) => {
  // To avoid there are one plus root nodes [heading 1: #]
  // Split the markdown text into sections based on root node headings
  const sections = markdown.split(/\n(?=# )/)
  // Only process the first section
  const firstSection = sections[0]

  // Only get those lines start with # or -
  const filteredMarkdown = filterMarkdown(firstSection)
  const processor = unified().use(remarkParse)
  const ast = processor.parse(filteredMarkdown)

  const nodes: Node[] = []
  const edges: Edge[] = []

  const lastNodeAtDepth: { [depth: number]: string } = {}
  const nodeCounterAtDepth: { [depth: number]: number } = {}

  const parseChildren = (children: any[], _parent: string) => {
    let yOffset = 0

    for (let i = 0; i < children.length; i++) {
      const child = children[i]
      const nextChild = children[i + 1] // Get the next sibling

      if (child.type === 'heading') {
        const depth = child.depth
        nodeCounterAtDepth[depth] = (nodeCounterAtDepth[depth] || 0) + 1
        const node = createNode(
          depth,
          nodeCounterAtDepth[depth],
          yOffset,
          child.children[0]?.value || ''
        )

        // Check if the next sibling is a list; if so, mark this node as a leaf node
        if (nextChild && nextChild.type === 'list') {
          node.type = 'customLeafNode' // Change this to your actual custom leaf node type
          const exampleText = collectTextNodes(nextChild.children[0])
          node.data.example = exampleText // Add the example text to the node data
        }

        nodes.push(node)

        if (lastNodeAtDepth[depth - 1]) {
          const edge = createEdge(lastNodeAtDepth[depth - 1], node.id)
          edges.push(edge)
        }

        yOffset++
        lastNodeAtDepth[depth] = node.id

        if (child.children) {
          parseChildren(child.children, node.id)
        }
      }
    }
  }

  // Start parsing
  parseChildren(ast.children, 'L0:0')

  return { nodes, edges }
}

export default markdownToReactFlow

import React, { useEffect, useState } from "react";

export interface MarkdownItem {
  title: string;
  children?: MarkdownItem[];
  isCurrent?: boolean;
}

interface DocumentTreeProps {
  markdownStructure: MarkdownItem[];
}

// Function to check if the item or any of its descendants is the current item
const isCurrentOrHasCurrent = (item: MarkdownItem): boolean => {
  if (item.isCurrent) return true;
  return item.children?.some(isCurrentOrHasCurrent) ?? false;
};

// Background colors for different levels (more added for deeper levels)
const bgColors = [
  "bg-blue-100",
  "bg-green-100",
  "bg-yellow-100",
  "bg-red-100",
  "bg-purple-100",
  "bg-pink-100",
  "bg-indigo-100",
  // ...add as many as needed for deep layers
];

const TreeItem: React.FC<{ item: MarkdownItem; level: number }> = ({
  item,
  level,
}) => {
  const [expanded, setExpanded] = useState(false);

  useEffect(() => {
    // Expand the tree to show the current item
    if (isCurrentOrHasCurrent(item)) {
      setExpanded(true);
    }
  }, [item]);

  const isLeaf = !item.children || item.children.length === 0;

  // Calculate the left padding based on the level, multiplying by 20 for example
  const paddingLeft = 20; // Adjust the multiplier as needed for your design
  const bgColor = bgColors[level % bgColors.length];

  return (
    <div
      style={{ paddingLeft: `${paddingLeft}px` }} // Apply the calculated padding here
      className={`${bgColor} hover:bg-opacity-60 transition duration-300`}
    >
      <div
        onClick={() => !isLeaf && setExpanded(!expanded)}
        className={`cursor-pointer py-1 ${item.isCurrent ? "font-bold" : ""}`}
      >
        {isLeaf ? "—" : expanded ? "↓" : "→"} {item.title}
      </div>
      {expanded && item.children && (
        <div>
          {item.children.map((child, index) => (
            <TreeItem key={index} item={child} level={level + 1} />
          ))}
        </div>
      )}
    </div>
  );
};

export const DocumentTree: React.FC<DocumentTreeProps> = ({
  markdownStructure,
}) => {
  return (
    <>
      <span className="font-bold">Existing issue tree</span>
      <div className="divide-y divide-gray-200 overflow-y-auto max-h-[300px]">
        {markdownStructure.map((item, index) => (
          <TreeItem key={index} item={item} level={0} />
        ))}
      </div>
    </>
  );
};

// export default DocumentTree;

import React, { useState } from "react";
import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from "@/components/ui/hover-card";
import { Button } from "@/components/ui/button";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

interface DialogProps {
  cannotUndo?: boolean;
  dialogTitle?: string;
  dialogDescription?: string;
}

interface HoverButtonProps {
  onClick: () => void;
  buttonText: string;
  hoverMessage: string;
  buttonClassName?: string;
  dialogProps?: DialogProps;
  isClickable?: boolean;
}

const HoverButton: React.FC<HoverButtonProps> = ({
  onClick,
  buttonText,
  hoverMessage,
  buttonClassName = "",
  dialogProps,
  isClickable = true, // Default to true if not specified
}) => {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isHovered, setIsHovered] = useState(false);

  const handleCloseDialog = () => setIsDialogOpen(false);

  const handleButtonClick = () => {
    if (isClickable) {
      if (dialogProps?.cannotUndo) {
        setIsDialogOpen(true);
      } else {
        onClick();
      }
    }
  };

  // Providing default values for dialogTitle and dialogDescription
  const defaultTitle = "Are you sure?";
  const defaultDescription = "This action cannot be undone.";

  const notClickableClass = !isClickable ? "opacity-50 cursor-not-allowed" : "";
  const combinedButtonClassName = `${buttonClassName} ${notClickableClass}`;

  return (
    <>
      <HoverCard>
        <HoverCardTrigger asChild>
          <Button
            autoFocus={false}
            className={combinedButtonClassName}
            onClick={handleButtonClick}
            disabled={!isClickable} // Disable the button if not clickable
            onMouseEnter={() => setIsHovered(true)} // Set isHovered to true on mouse enter
            onMouseLeave={() => setIsHovered(false)} // Set isHovered to false on mouse leave
          >
            {buttonText}
          </Button>
        </HoverCardTrigger>
        {isHovered && <HoverCardContent>{hoverMessage}</HoverCardContent>}
      </HoverCard>

      {dialogProps?.cannotUndo && (
        <AlertDialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <AlertDialogContent>
            <AlertDialogTitle>
              {dialogProps.dialogTitle || defaultTitle}
            </AlertDialogTitle>
            <AlertDialogDescription>
              {dialogProps.dialogDescription || defaultDescription}
            </AlertDialogDescription>
            <AlertDialogFooter>
              <AlertDialogCancel asChild>
                <Button
                  className="bg-zinc-300 hover:bg-zinc-400"
                  onClick={handleCloseDialog}
                >
                  Cancel
                </Button>
              </AlertDialogCancel>
              <AlertDialogAction asChild>
                <Button
                  className="bg-red-500 hover:bg-red-600"
                  onClick={() => {
                    onClick();
                    handleCloseDialog();
                  }}
                >
                  Continue
                </Button>
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      )}
    </>
  );
};

export default HoverButton;

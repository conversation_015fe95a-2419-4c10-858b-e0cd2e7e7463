"use client";

import React from "react";
import { ReactFlowProvider } from "reactflow";
import "reactflow/dist/style.css";

import { User } from "@prisma/client";
import { useFetchIssueTreeData } from "@/app/hooks/useFetchIssueTreeData";

import { IssueTreeContent } from "./IssueTreeContent";
import { LoadingSkeleton } from "@/app/components/LoadingSkeleton";
import { useSession } from "next-auth/react";

type MainIssueTreeProps = {
  conversationId: string;
  currentUser: User | null;
};

const MainIssueTree: React.FC<MainIssueTreeProps> = ({
  conversationId,
  currentUser,
}) => {
  const { data: session } = useSession();
  const userId = session?.user?.id || "";

  const {
    issueTree,
    isLoading: isIssueTreeLoading,
    feedbacks,
    subtrees,
    reload: reloadIssueTreeData,
  } = useFetchIssueTreeData(conversationId, userId);

  if (isIssueTreeLoading) {
    return <LoadingSkeleton />;
  }

  return (
    <ReactFlowProvider>
      <div className="w-full h-[calc(100vh-120px)] sm:h-[calc(100vh-140px)]">
        <IssueTreeContent
          conversationId={conversationId}
          currentUser={currentUser}
          fetchedIssueTree={issueTree}
          reloadIssueTreeData={reloadIssueTreeData}
          fetchedFeedbacks={feedbacks}
          fetchedSubtrees={subtrees}
        />
      </div>
    </ReactFlowProvider>
  );
};

export default MainIssueTree;

import React from "react";
import { LoadingSkeleton } from "@/app/components/LoadingSkeleton";
import {
  issueTreeMode,
  issueTreeStoreStatus,
} from "@/app/stores/issuetree_store";
import { Header } from "@/app/(conv)/conversations/[conversationId]/issue_tree/MainIssueTree/Header";
import ExpandSheet from "@/app/(conv)/conversations/[conversationId]/issue_tree/MainIssueTree/ExpandSheet";
import IssueTreeFlow from "@/app/(conv)/conversations/[conversationId]/issue_tree/MainIssueTree/IssueTreeFlow";
import IssueList from "@/app/(conv)/conversations/[conversationId]/issue_tree/IssueList";
import SubtreeDialog from "@/app/(conv)/conversations/[conversationId]/issue_tree/SubtreeDialog";

import IssueNodeDialog from "@/app/(conv)/conversations/[conversationId]/issue_tree/IssueNodeDialog";
import { HallucinationAlert } from "@/app/(conv)/conversations/[conversationId]/issue_tree/MainIssueTree/HallucinationAlert";

import { User } from "@prisma/client";
import {
  fetchedIssueTreeType,
  fetchedFeedbacksType,
  fetchedSubtreesType,
} from "@/app/server-actions";
import { useIssueTreeLogic } from "@/app/hooks/useIssueTreeLogic";
import { NodeChange } from "reactflow";
import NotebookDialog from "@/app/(conv)/conversations/[conversationId]/issue_tree/NotebookDialog";

type IssueTreeContentType = {
  conversationId: string;
  currentUser: User | null;
  fetchedIssueTree: fetchedIssueTreeType | null;
  reloadIssueTreeData: () => void;
  fetchedFeedbacks: fetchedFeedbacksType[];
  fetchedSubtrees: fetchedSubtreesType[];
};

export const IssueTreeContent: React.FC<IssueTreeContentType> = ({
  conversationId,
  currentUser,
  fetchedIssueTree,
  reloadIssueTreeData,
  fetchedSubtrees,
}) => {
  const {
    issueTreeStore,
    subtreeStore,
    showSheet,
    openSheet,
    closeSheet,
    onNodesChange,
    isLoading,
  } = useIssueTreeLogic({
    conversationId,
    currentUser,
    fetchedIssueTree,
    reloadIssueTreeData: () => Promise.resolve(reloadIssueTreeData()),
    fetchedSubtrees,
  });

  // Ensure that nodes and edges are memoized or come from a stable source
  const memoizedNodes = React.useMemo(
    () => issueTreeStore.nodes,
    [issueTreeStore.nodes]
  );
  const memoizedEdges = React.useMemo(
    () => issueTreeStore.edges,
    [issueTreeStore.edges]
  );
  // Memoize onNodesChange to prevent re-creation on each render
  const memoizedOnNodesChange = React.useCallback(
    (changes: NodeChange[]) => {
      onNodesChange(changes);
    },
    [onNodesChange]
  );

  if (issueTreeStore.nodes.length === 0) {
    return <LoadingSkeleton />;
  }

  return (
    <div className="flex flex-col h-screen">
      <Header issueTreeStore={issueTreeStore} openSheet={openSheet} />
      {/* Hallucination Alert, when the component is first mounted */}
      {!isLoading && <HallucinationAlert />}

      {/* Top right more section */}
      <ExpandSheet
        showSheet={showSheet}
        closeSheet={closeSheet}
        conversationId={conversationId}
      />
      {issueTreeStore.mode === issueTreeMode.Tree ? (
        // Tree mode for issue tree: ReactFlow part
        <IssueTreeFlow
          nodes={memoizedNodes}
          edges={memoizedEdges}
          onNodesChange={memoizedOnNodesChange}
        />
      ) : (
        // List mode for issue tree
        <IssueList />
      )}

      {/* Subtree Dialog */}
      {issueTreeStore.status === issueTreeStoreStatus.Ready &&
        subtreeStore.selectedSubtree?.isOpen && (
          <SubtreeDialog id={subtreeStore.selectedSubtree?.selectedNodeId} />
        )}

      {/* Dialog for a single issue */}
      {issueTreeStore.selectedNode?.isOpen && <IssueNodeDialog />}

      {/* Notebook Dialog */}
      <NotebookDialog />
    </div>
  );
};

import React, { useState } from 'react'
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>ert<PERSON><PERSON>og<PERSON><PERSON>nt,
  AlertDialog<PERSON>ooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogDescription,
} from '@/components/ui/alert-dialog'
import useIssueTreeStore from '@/app/stores/issuetree_store'
import { Button } from '@/components/ui/button'
import { Node, NodeType } from '@/app/types'
import useNotebookStore from '@/app/stores/notebook_store'

export const HallucinationAlert: React.FC = () => {
  const [showAlert, setShowAlert] = useState<boolean>(true)
  const issueTreeStore = useIssueTreeStore()
  const notebookStore = useNotebookStore()

  const unresolvedCount = issueTreeStore.unresolvedNodesCount

  // First unsolved leaf node
  const firstLeafNodeId =
    issueTreeStore.nodes.find(
      (node: Node) =>
        node.type === NodeType.CustomLeafNode && !node.data?.resolved
    )?.id || ''

  const handleExploreIssueTree = () => {
    setShowAlert(false)
  }

  const handleGenerateNotebook = () => {
    setShowAlert(false)
    notebookStore.setShowDialog(true)
  }

  const handleStartReview = () => {
    setShowAlert(false)
    issueTreeStore.setSelectedNode(firstLeafNodeId)
  }

  const handleCloseAlert = () => {
    setShowAlert(false)
  }

  if (!showAlert) return null

  return (
    <AlertDialog open={showAlert} onOpenChange={handleCloseAlert}>
      <AlertDialogContent className="max-w-4xl w-full">
        <AlertDialogHeader>
          <AlertDialogTitle className="text-3xl mb-4">
            <strong>AI tools may generate inaccurate information</strong>
          </AlertDialogTitle>
          <AlertDialogDescription className="text-xl">
            <li>
              ❌ Contents/Numbers may <strong>NOT</strong> be fact based
            </li>
            <li>💡 Treat this as starter structure/brainstorm</li>
            <li>⚖️ Your knowledge and judgement matter most!</li>
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter className="flex-col space-y-4 sm:flex-row sm:space-y-0 sm:space-x-4">
          {unresolvedCount !== 0 && (
            <Button
              onClick={handleStartReview}
              className="w-full sm:w-1/3 text-lg py-6 px-8 bg-blue-500 text-white hover:bg-blue-600 transition-colors duration-200 flex flex-col items-center justify-center"
            >
              <span>Start Review</span>
              <span className="text-sm">{unresolvedCount} Qs to review</span>
            </Button>
          )}
          <Button
            onClick={handleGenerateNotebook}
            className="w-full sm:w-1/3 text-lg py-6 px-8 bg-green-500 text-white hover:bg-green-600 transition-colors duration-200 flex flex-col items-center justify-center"
          >
            Generate Notebook
          </Button>
          <Button
            onClick={handleExploreIssueTree}
            className="w-full sm:w-1/3 text-lg py-6 px-8 bg-purple-500 text-white hover:bg-purple-600 transition-colors duration-200 flex items-center justify-center"
          >
            Explore Issue Tree
          </Button>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}

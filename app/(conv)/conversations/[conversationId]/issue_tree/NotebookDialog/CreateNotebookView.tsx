'use client'

import { useState, useMemo, useEffect } from 'react'
import mixpanel from '@/app/libs/mixpanel'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { ContextSelection, ContextSelectionState } from './ContextSelection'
import { prompts, promptSuffix } from './promptClone'
import { Label } from '@/components/ui/label'
import { useRouter, useParams } from 'next/navigation'
import useIssueTreeStore from '@/app/stores/issuetree_store'
import useNotebookStore from '@/app/stores/notebook_store'
import { Node } from '@/app/types'
import { toast } from 'react-hot-toast'
import { ChevronDown, ChevronUp } from 'lucide-react'
import { motion, AnimatePresence } from 'framer-motion'
import { useSession } from 'next-auth/react'
import {
  createNotebook,
  createNotebookType,
} from '@/app/server-actions/notebook'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip'
import { LanguageEnum, languages, notebookCountLimit } from '@/app/configs'
type CreateNotebookViewProps = {
  onCancel: () => void
}

export function CreateNotebookView({ onCancel }: CreateNotebookViewProps) {
  const issueTreeStore = useIssueTreeStore()
  const notebookStore = useNotebookStore()

  const [selectedPromptIndex, setSelectedPromptIndex] = useState<number | null>(
    null
  )
  const [textareaValue, setTextareaValue] = useState<string>('')
  const [showPrompts, setShowPrompts] = useState<boolean>(true)
  const [selectedLanguage, setSelectedLanguage] = useState<LanguageEnum>(
    LanguageEnum.English
  )
  const router = useRouter()
  const params = useParams()
  const conversationId = params?.conversationId as string
  const { data: session } = useSession()
  const userId = session?.user.id
  const issueTreeId = issueTreeStore.issueTreeId
  const canCreateNewNotebook =
    notebookStore.notebooks.length < notebookCountLimit

  const [selectedContexts, setSelectedContexts] =
    useState<ContextSelectionState>({
      useAllContexts: false,
    })

  useEffect(() => {
    if (issueTreeStore.treeMarkdownText === '') {
      issueTreeStore.setTreeMarkdownText()
    }
  }, [issueTreeStore])

  const handlePromptSelect = (index: number) => {
    setSelectedPromptIndex(index)
    setTextareaValue(prompts[index].description)
    setShowPrompts(false)
  }

  const handleGenerate = async () => {
    if (selectedPromptIndex === null) {
      toast.error('Please select a prompt')
      return
    }
    if (!userId || !issueTreeId) {
      toast.error('User or issue tree not found')
      return
    }
    if (!canCreateNewNotebook) {
      toast.error('You have reached the current notebook limit')
      return
    }

    mixpanel.track('click_generate_notebook', {
      location: 'notebook_dialog',
    })

    const newNotebookData: createNotebookType = {
      userId,
      issueTreeId,
      conversationId,
      title: `${prompts[selectedPromptIndex].title}_${new Date()
        .toISOString()
        .replace(/[-:]/g, '')
        .slice(0, 15)}`,
      full_prompt: fullPrompt,
      config: selectedContexts,
      remark: prompts[selectedPromptIndex].title,
    }
    try {
      const newNotebook = await createNotebook(newNotebookData)
      const newNotebookId = newNotebook.id
      notebookStore.setShowDialog(false)
      router.push(`/conversations/${conversationId}/notebook/${newNotebookId}`)
    } catch {
      toast.error('Failed to create notebook')
    }
  }

  const handleCopyPrompt = () => {
    toast.success('Prompt copied to clipboard')
    mixpanel.track('click_copy_prompt', {
      location: 'notebook_dialog',
    })
    navigator.clipboard.writeText(fullPrompt)
  }

  const handleContextSelect = (contexts: ContextSelectionState) => {
    setSelectedContexts(contexts)
  }

  const togglePrompts = () => {
    setShowPrompts(!showPrompts)
  }

  const originalAsk = issueTreeStore.originalAskText
  const workingContext = useMemo(() => {
    return selectedContexts.useAllContexts
      ? issueTreeStore.treeMarkdownText
      : getAnsweredQuestionsString(issueTreeStore.nodes)
  }, [
    selectedContexts.useAllContexts,
    issueTreeStore.treeMarkdownText,
    issueTreeStore.nodes,
  ])

  const fullPrompt = useMemo(() => {
    let prompt =
      textareaValue +
      promptSuffix
        .replace('{{WORKING_CONTEXT}}', `{${workingContext}}`)
        .replace('{{ORIGINAL_ASK}}', `{${originalAsk}}`)

    if (selectedLanguage !== LanguageEnum.English) {
      const selectedLang = languages.find(
        lang => lang.value === selectedLanguage
      )
      prompt += `\nPlease generate this response in ${selectedLanguage} ${selectedLang?.label}`
    }

    return prompt
  }, [textareaValue, workingContext, originalAsk, selectedLanguage])

  const resolvedNodeCount =
    issueTreeStore.leafNodeCount - issueTreeStore.unresolvedNodesCount
  const leafNodeCount = issueTreeStore.leafNodeCount
  const displayWorkingContextText = selectedContexts.useAllContexts
    ? `Working Contexts: using all ${leafNodeCount} Qs`
    : `Working Contexts: using ${resolvedNodeCount} out of ${leafNodeCount} Qs`

  const isGenerateDisabled = useMemo(() => {
    return (
      textareaValue.trim().length < 100 || workingContext.trim().length === 0
    )
  }, [textareaValue, workingContext])

  return (
    <div className="space-y-6 p-6 bg-white rounded-lg shadow-md">
      {!canCreateNewNotebook && (
        <span className="text-sm text-red-500 font-bold">
          You have reached the current notebook limit, but you can still copy
          and run with your favorite model
        </span>
      )}

      <div className="flex items-center justify-between">
        <ContextSelection onSelect={handleContextSelect} />
        <div className="flex items-center space-x-4">
          {selectedPromptIndex !== null && (
            <Button
              onClick={togglePrompts}
              variant="outline"
              className="flex items-center text-blue-600 hover:text-blue-700 transition-colors"
            >
              {showPrompts ? (
                <>
                  Hide prompt list
                  <ChevronUp className="ml-2 h-4 w-4" />
                </>
              ) : (
                <>
                  Select another prompt
                  <ChevronDown className="ml-2 h-4 w-4" />
                </>
              )}
            </Button>
          )}
        </div>
      </div>
      <AnimatePresence>
        {showPrompts && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
            style={{ overflow: 'hidden' }}
          >
            <div className="flex flex-wrap gap-2">
              {prompts.map((prompt, index) => (
                <Button
                  key={prompt.title}
                  onClick={() => handlePromptSelect(index)}
                  className={`transition-colors ${
                    selectedPromptIndex === index
                      ? 'bg-blue-500 hover:bg-blue-600 text-white'
                      : 'bg-gray-100 hover:bg-gray-200 text-zinc-800'
                  }`}
                >
                  {prompt.title}
                </Button>
              ))}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
      {selectedPromptIndex !== null && (
        <Label htmlFor="prompt" className="text-sm text-pink-400 font-medium">
          Prompt is editable
        </Label>
      )}
      <Textarea
        id="prompt"
        className="text-lg text-gray-400 hover:text-gray-600 focus:text-gray-600 hover:border-blue-500 focus:border-blue-500 transition-colors"
        placeholder="Select a prompt to start"
        value={textareaValue}
        onChange={e => setTextareaValue(e.target.value)}
        rows={5}
      />
      <div className="flex space-x-6">
        <div className="flex-1 space-y-4">
          <div>
            <Label
              htmlFor="originalAsk"
              className="text-sm font-medium text-gray-700"
            >
              Original Ask
            </Label>
            <Textarea
              id="originalAsk"
              value={originalAsk}
              readOnly
              rows={3}
              className="mt-1 text-sm text-gray-300 hover:text-gray-600 bg-gray-50 border-gray-300"
              disabled={true}
            />
          </div>
          <div>
            <Label
              htmlFor="workingContext"
              className="text-sm font-medium text-gray-700"
            >
              {displayWorkingContextText}
            </Label>
            <Textarea
              id="workingContext"
              value={workingContext}
              readOnly
              rows={5}
              className="mt-1 text-sm  text-gray-300 hover:text-gray-600 bg-gray-50 border-gray-300"
              disabled={true}
            />
          </div>
        </div>
        <div className="flex flex-col space-y-4 justify-start">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="flex items-center pt-6">
                  <Select
                    value={selectedLanguage}
                    onValueChange={value =>
                      setSelectedLanguage(value as LanguageEnum)
                    }
                    aria-label="Select language"
                  >
                    <SelectTrigger className="w-[150px] bg-gray-100 border-gray-300 hover:bg-gray-200 transition-colors text-center">
                      <SelectValue placeholder="Select language" />
                    </SelectTrigger>
                    <SelectContent>
                      {languages.map(lang => (
                        <SelectItem key={lang.value} value={lang.value}>
                          {lang.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </TooltipTrigger>
              <TooltipContent>
                <p>Best effort to generate response in this language</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
          <Button
            onClick={handleGenerate}
            disabled={
              selectedPromptIndex === null ||
              isGenerateDisabled ||
              !canCreateNewNotebook
            }
            className="bg-green-500 hover:bg-green-600 text-white transition-colors"
          >
            Generate
          </Button>
          <Button
            onClick={handleCopyPrompt}
            disabled={selectedPromptIndex === null}
            className="bg-blue-500 hover:bg-blue-600 text-white transition-colors"
          >
            Copy Prompt
          </Button>
          <Button
            variant="outline"
            onClick={onCancel}
            className="border-red-500 text-red-500 hover:bg-red-50 transition-colors"
          >
            Cancel
          </Button>
        </div>
      </div>
    </div>
  )
}

const getAnsweredQuestionsString = (nodes: Node[]): string => {
  return nodes
    .filter(
      node =>
        node.type === 'customLeafNode' &&
        node.data.resolved &&
        !node.data.skipped
    )
    .map(node => `Question: ${node.data.label}\nAnswer: ${node.data.example}`)
    .join('\n\n')
}

// Replaced by server actions

// export async function fetchIssueTree(conversationId: string, currentUser: any) {
//   try {
//     const baseUrl =
//       process.env.NODE_ENV === "development"
//         ? "http://localhost:3000"
//         : process.env.NEXT_PUBLIC_SERVER_URL;
//     const response = await fetch(`${baseUrl}/api/issuetree/load`, {
//       method: "POST",
//       headers: {
//         "Content-Type": "application/json",
//       },
//       body: JSON.stringify({ conversationId, currentUser, isReturnAll: false }),
//     });

//     if (!response.ok) {
//       throw new Error(`Failed to fetch issue tree: ${response.statusText}`);
//     }

//     const data = await response.json();
//     return data;
//   } catch (error) {
//     console.error(`Error fetching issue tree: ${error}`);
//     throw error;
//   }
// }

// export async function fetchAllFeedbacks(conversationId: string) {
//   try {
//     const baseUrl =
//       process.env.NODE_ENV === "development"
//         ? "http://localhost:3000"
//         : process.env.NEXT_PUBLIC_SERVER_URL;
//     const response = await fetch(
//       `${baseUrl}/api/feedback/getall`,
//       {
//         method: "POST",
//         headers: {
//           "Content-Type": "application/json",
//         },
//         body: JSON.stringify({
//           conversationId,
//         }),
//       }
//     );

//     if (!response.ok) {
//       throw new Error(`Failed to fetch feedbacks: ${response.statusText}`);
//     }

//     const data = await response.json();
//     return data;
//   } catch (error) {
//     console.error(`Error fetching feedbacks: ${error}`);
//     throw error;
//   }
// }

// export async function fetchSubtrees(issueTreeId: string, currentUser: any) {
//   try {
//     const baseUrl =
//       process.env.NODE_ENV === "development"
//         ? "http://localhost:3000"
//         : process.env.NEXT_PUBLIC_SERVER_URL;
//     const response = await fetch(`${baseUrl}/api/subtree/load`, {
//       method: "POST",
//       headers: {
//         "Content-Type": "application/json",
//       },
//       body: JSON.stringify({ issueTreeId, currentUser }),
//     });

//     if (!response.ok) {
//       throw new Error(`Failed to fetch subtree: ${response.statusText}`);
//     }

//     const data = await response.json();
//     return data;
//   } catch (error) {
//     console.error(`Error fetching issue tree: ${error}`);
//     throw error;
//   }
// }

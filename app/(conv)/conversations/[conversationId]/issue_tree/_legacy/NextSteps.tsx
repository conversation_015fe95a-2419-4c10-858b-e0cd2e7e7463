// import { <PERSON><PERSON> } from "@/components/ui/button";
// import useIssueTreeStore, { storeStatus } from "@/app/stores/issuetree";
// import { useState, useEffect, ChangeEvent } from "react";
// import { useRouter } from "next/navigation";
// import {
//   Dialog,
//   DialogContent,
//   DialogHeader,
//   DialogTitle,
//   DialogTrigger,
// } from "@/components/ui/dialog";
// import {
//   AlertDialog,
//   AlertDialogAction,
//   AlertDialogCancel,
//   AlertDialogContent,
//   AlertDialogDescription,
//   AlertDialogFooter,
//   AlertDialogHeader,
//   AlertDialogTitle,
//   AlertDialogTrigger,
// } from "@/components/ui/alert-dialog";
// import toast from "react-hot-toast";
// import { Textarea } from "@/components/ui/textarea";
// import { useChat } from "ai/react";
// import axios from "axios";
// import { useConversationStore } from "@/app/stores/conversation";
// import { ConversationStatus } from "@prisma/client";
// import mixpanel from "@/app/libs/mixpanel";

// interface NextStepsProps {
//   conversationId: string;
//   currentUser: any;
//   reloadIssueTreeData: () => void;
//   setShowFeedback: (show: boolean) => void;
//   setIsReportGenerated: (isReportGenerated: boolean) => void;
//   setDraftText: (draftText: string) => void;
// }

// const NextSteps: React.FC<NextStepsProps> = ({
//   conversationId,
//   currentUser,
//   reloadIssueTreeData,
//   setShowFeedback,
//   setIsReportGenerated,
//   setDraftText,
// }) => {
//   const issueTreeStore = useIssueTreeStore();
//   const issueTreeId = issueTreeStore.issueTreeId;
//   const [isDialogOpen, setDialogOpen] = useState(false);
//   const [isRoundAlertOpen, setRoundAlertOpen] = useState(false);
//   const [isDraftAlertOpen, setDraftAlertOpen] = useState(false);
//   const [summaryText, setSummaryText] = useState("");
//   const [questionDirectionString, setQuestionDirectionString] = useState("");
//   const conversationStore = useConversationStore();
//   const router = useRouter();

//   // // Load the summary text from the store for the first time
//   // let isSetSummaryText = false;
//   // useEffect(() => {
//   //   if (!isSetSummaryText) {
//   //     isSetSummaryText = true;
//   //     setSummaryText(issueTreeStore.summaryText);
//   //   }
//   // }, [issueTreeStore.summaryText]);

//   useEffect(() => {
//     setSummaryText(issueTreeStore.summaryText);
//   }, [issueTreeStore.summaryText, conversationId]);

//   // Prepare the ingredients for api/issuetree/generate_followup endpoint
//   const leafQuestions = issueTreeStore.nodes.filter(
//     (node) => node.type === "customLeafNode"
//   );

//   const answeredQuestionsPairString = leafQuestions
//     .filter((node) => !node.data.skipped) // Answered Qs only
//     .map((node) => {
//       return `Question ${node.data.label} \nAnswer ${node.data.example}`;
//     })
//     .join("\n\n");

//   const skippedQuestionsString = leafQuestions
//     .filter((node) => node.data.skipped) // Skipped Qs only
//     .map((node) => {
//       return `${node.data.label}`;
//     })
//     .join("\n\n");

//   const answeredQuestionsString = leafQuestions
//     .filter((node) => !node.data.skipped) // Answered Qs only
//     .map((node) => {
//       return `${node.data.label}`;
//     })
//     .join("\n\n");

//   // Generate summary based on existing answers
//   const { messages, append, isLoading } = useChat({
//     api: "/api/issuetree/generate_summary",
//     body: {
//       conversationId,
//       currentUser,
//       issueTreeId: issueTreeId,
//       answeredQuestionsString: answeredQuestionsPairString,
//     },
//   });

//   // When it is streaming, update the summary text
//   useEffect(() => {
//     // console.log("messages", messages);
//     const lastMessage = messages[messages.length - 1];
//     if (lastMessage && lastMessage.role === "assistant") {
//       setSummaryText(lastMessage.content);
//     }
//   }, [messages]);

//   // Allow user to edit the summary text
//   const handleTextChange = (e: ChangeEvent<HTMLTextAreaElement>) => {
//     const newText = e.target.value;
//     setSummaryText(newText);
//   };

//   // Allow user to give more directions, for api/issuetree/generate_followup endpoint
//   const handleQuestionDirectionStringChange = (
//     e: ChangeEvent<HTMLTextAreaElement>
//   ) => {
//     const newText = e.target.value;
//     setQuestionDirectionString(newText);
//   };

//   const handleGenerateSummary = async () => {
//     toast.success("clicked Generate summary");
//     mixpanel.track("generate_summary_click");
//     await append({ role: "system", content: "Generate summary" });
//   };

//   const handleFollowup = () => {
//     mixpanel.track("generate_followup_click");
//     axios
//       .post("/api/issuetree/followup", {
//         summaryText,
//         questionDirectionString,
//         skippedQuestionsString,
//         answeredQuestionsString,
//         currentUser,
//         conversationId,
//         issueTreeId,
//       })
//       .then((response) => {
//         if (!response.data.error) {
//           // Redirect to the same conversationm, but with the new issue tree
//           toast.success("Done! Redirecting to the new issue tree", {
//             duration: 2000,
//           });
//           setTimeout(() => {
//             reloadIssueTreeData();
//           }, 1000);
//         } else {
//           toast.error(response.data.error);
//         }
//       })
//       .catch((error) => {
//         console.log("Error in API call:", error);
//         toast.error("Something went wrong. Please try again.");
//       });
//     setDialogOpen(false);
//   };

//   // Generate final doc based on the summary text
//   const { messages: finalDocMessages, append: finalDocAppend } = useChat({
//     api: "/api/issuetree/final_doc",
//     body: {
//       conversationId,
//       issueTreeId,
//       currentUser,
//       summaryText,
//     },
//     onResponse: () => {
//       // Run these as soon as a response is received
//       setShowFeedback(true);
//       setDialogOpen(false);
//     },
//     onFinish: () => {
//       // Set UI conversation status to completed
//       conversationStore.setConversationStatus(
//         conversationId,
//         ConversationStatus.COMPLETED
//       );
//       // Run this when the chat stream ends
//       setIsReportGenerated(true);
//       toast.success("Done! Redirecting to the final doc", { duration: 2000 });
//     },
//     onError: (error) => {
//       toast.error("Something went wrong. Please try again.");
//       console.log("Error in API call:", error);
//     },
//   });

//   // When it is streaming, update the draft text so we can count and show progress
//   useEffect(() => {
//     // console.log("messages", messages);
//     const lastMessage = finalDocMessages[finalDocMessages.length - 1];
//     if (lastMessage && lastMessage.role === "assistant") {
//       setDraftText(lastMessage.content);
//     }
//   }, [finalDocMessages]);

//   const handleGenerateDraft = async () => {
//     toast.success("Start draft generation");
//     mixpanel.track("generate_draft_click");
//     await finalDocAppend({ role: "system", content: "Generate draft" });
//   };

//   const isStepOneDone = summaryText.length !== 0;
//   const isConversationCompleted =
//     conversationStore.getConversationStatus(conversationId) ===
//       ConversationStatus.COMPLETED ||
//     conversationStore.getConversationStatus(conversationId) ===
//       ConversationStatus.EXAMPLE;

//   return (
//     <div className="self-center text-center">
//       {issueTreeStore.status === storeStatus.Completed &&
//         isConversationCompleted && (
//           <div className="flex flex-col justify-center items-center">
//             <p className="text-3xl font-bold p-2">
//               {" "}
//               {conversationStore.getConversationStatus(conversationId) ===
//               ConversationStatus.EXAMPLE
//                 ? "Example result is here!"
//                 : "Result is here! 🤩"}
//             </p>
//             <Button
//               className="bg-green-300 hover:bg-green-400 text-zinc-500 hover:font-bold p-2 mb-2"
//               onClick={() => {
//                 router.push(`/conversations/${conversationId}/result`);
//                 toast.success("Loading");
//               }}
//             >
//               View result
//             </Button>
//           </div>
//         )}

//       {/* Show next step button if all are resolved and issue tree NOT completed */}
//       {issueTreeStore.unresolvedNodesCount === 0 &&
//         issueTreeStore.status !== storeStatus.Completed && (
//           <Dialog
//             open={isDialogOpen}
//             onOpenChange={(isOpen) => setDialogOpen(isOpen)}
//           >
//             <DialogTrigger className="self-center rounded-md bg-red-200 hover:bg-red-300 p-4 font-bold text-zinc-600 animate-pulse">
//               ALL resolved 🤩 Next steps here!
//               {/* <Button className="self-center">All resolved! Next step</Button> */}
//             </DialogTrigger>
//             <DialogContent>
//               <DialogHeader>
//                 <DialogTitle className="text-3xl">Next steps</DialogTitle>
//                 <p className="font-bold text-lg self-center py-2">
//                   Step 1: Generate the summary
//                 </p>
//                 <Button
//                   className={`py-4 ${
//                     isStepOneDone
//                       ? "bg-zinc-400 hover:bg-zinc-400 cursor-not-allowed"
//                       : "bg-green-300 hover:bg-green-400 text-zinc-500 hover:font-bold"
//                   }`}
//                   onClick={() => handleGenerateSummary()}
//                   disabled={isStepOneDone || isLoading}
//                 >
//                   When ready, click and generate summary
//                 </Button>

//                 {summaryText.length !== 0 && (
//                   <p className="text-xs self-center">
//                     *Changes to summary are NOT saved if you close the browser
//                     tab
//                   </p>
//                 )}

//                 {/* Textarea */}
//                 <div className="flex flex-row justify-between items-center">
//                   <Textarea
//                     rows={15}
//                     //   className={`w-full h-16 text-sm p-1 rounded-md transition-shadow overflow-auto`}
//                     value={summaryText}
//                     onChange={handleTextChange}
//                     placeholder="Your summary are generated based on your current answers and previous summary"
//                     disabled={!isStepOneDone || isLoading}
//                   />
//                 </div>
//                 <p className="font-bold text-lg self-center py-2">
//                   Step 2: Review & adjust, then select the next move
//                 </p>
//                 <div className="flex flex-row justify-around p-2">
//                   {/* For Generate draft AlertDialog */}
//                   <AlertDialog
//                     open={isDraftAlertOpen}
//                     onOpenChange={(isOpen) => setDraftAlertOpen(isOpen)}
//                   >
//                     <AlertDialogTrigger asChild>
//                       <Button
//                         className={`${
//                           isStepOneDone && !isLoading
//                             ? "bg-green-300 hover:bg-green-400 text-zinc-500 hover:font-bold"
//                             : "bg-zinc-400 hover:bg-zinc-400 cursor-not-allowed"
//                         }`}
//                         onClick={() => {
//                           setDraftAlertOpen(true);
//                         }}
//                         disabled={!isStepOneDone || isLoading}
//                       >
//                         Generate Final Doc
//                       </Button>
//                     </AlertDialogTrigger>
//                     <AlertDialogContent>
//                       <AlertDialogHeader>
//                         <AlertDialogTitle>
//                           Generate Final Document
//                         </AlertDialogTitle>
//                         <AlertDialogDescription>
//                           After you click Continue, a document will be
//                           generated. It currently leans toward an engineering
//                           perspective, your feedback will be invaluable in
//                           shaping variations
//                         </AlertDialogDescription>
//                         <AlertDialogDescription>
//                           It uses the generated summary as context, so it is
//                           crucial to review it before generation
//                         </AlertDialogDescription>
//                         <AlertDialogDescription>
//                           It may take <strong>some minutes</strong> to complete
//                         </AlertDialogDescription>
//                       </AlertDialogHeader>
//                       <AlertDialogFooter>
//                         <AlertDialogCancel
//                           onClick={() => setDraftAlertOpen(false)}
//                         >
//                           Cancel
//                         </AlertDialogCancel>
//                         <AlertDialogAction onClick={handleGenerateDraft}>
//                           Continue
//                         </AlertDialogAction>
//                       </AlertDialogFooter>
//                     </AlertDialogContent>
//                   </AlertDialog>

//                   {/* For Generate follow-up AlertDialog */}
//                   <AlertDialog
//                     open={isRoundAlertOpen}
//                     onOpenChange={(isOpen) => {
//                       setRoundAlertOpen(isOpen);
//                     }}
//                   >
//                     <AlertDialogTrigger asChild>
//                       <Button
//                         className={`${
//                           isStepOneDone && !isLoading
//                             ? "bg-green-300 hover:bg-green-400 text-zinc-500 hover:font-bold"
//                             : "bg-zinc-400 hover:bg-zinc-400 cursor-not-allowed"
//                         }`}
//                         onClick={() => {
//                           setRoundAlertOpen(true);
//                         }}
//                         disabled={!isStepOneDone || isLoading}
//                       >
//                         Generate Follow-up
//                       </Button>
//                     </AlertDialogTrigger>
//                     <AlertDialogContent>
//                       <AlertDialogHeader>
//                         <AlertDialogTitle>Generate Follow-up</AlertDialogTitle>
//                         <AlertDialogDescription>
//                           After you click Continue, it will base on the
//                           generated summary to create another set of questions
//                           and further clarify the details
//                         </AlertDialogDescription>
//                         <Textarea
//                           rows={5}
//                           //   className={`w-full h-16 text-sm p-1 rounded-md transition-shadow overflow-auto`}
//                           value={questionDirectionString}
//                           onChange={handleQuestionDirectionStringChange}
//                           placeholder="[OPTIONAL] You can also guide the question directions here, eg: please focus more on the features/marketing strategy/technical details"
//                           disabled={!isStepOneDone}
//                         />
//                       </AlertDialogHeader>
//                       <AlertDialogFooter>
//                         <AlertDialogCancel
//                           onClick={() => setRoundAlertOpen(false)}
//                         >
//                           Cancel
//                         </AlertDialogCancel>
//                         <AlertDialogAction onClick={handleFollowup}>
//                           Continue
//                         </AlertDialogAction>
//                       </AlertDialogFooter>
//                     </AlertDialogContent>
//                   </AlertDialog>
//                 </div>
//               </DialogHeader>
//             </DialogContent>
//           </Dialog>
//         )}
//     </div>
//   );
// };

// export default NextSteps;

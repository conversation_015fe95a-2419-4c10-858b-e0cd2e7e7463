export const promptSuffix = `
Original ask:
{{ORIGINAL_ASK}}

We have completed interviews with their core teams and clarified some questions. Below are their responses to our inquiries:
{{WORKING_CONTEXT}}

Please note that the contexts are not exhaustive and may have blind spots. Your analysis will actively think of new insights outside of existing contexts

Those numbers with EXAMPLE NUMBER as a suffix may not be fact-based numbers, you should conduct your own search if needed
`;

const enum PromptTag {
  // Domain-specific tags
  General = "General",
  Business = "Business",
  Engineering = "Engineering",
  Product = "Product",
  // Prompt-specific tags
  AssignPersona = "Assign Persona/Role",
  ZeroShot = "Zero-shot Learning",
  OneShot = "One/Few-shot Learning",
  TipsOrThreat = "Tips or Threat",
  StepByStep = "Step-by-Step alike",

  // Context
  StructuredOutput = "Provide Output Flow",

  // Capability
  Multilingual = "Multilingual",
}

type Prompt = {
  title: string;
  description: string;
  tags: PromptTag[];
};

export const prompts: Prompt[] = [
  {
    title: "Explain to layman",
    description:
      `We are writing an industry report targeting executives who are NOT in this domain. Based on the context below, carefully think and organize the materials. Your objective is to help people to understand this domain ASAP, you focus on the first principles of the industry and also the easy-to-read communication.  You use examples, illustrations, and analogies to help people build mental models for this domain and enable them to decide how/whether to enter the market, try to avoid jargon, and use plain English.

The outputs, should not be in point form, they should be integrated and explained with a logical flow [similar to slide presentation, you shouldn't jump b/w points, you need to connect the dots]

Remember, you are not rephrasing the context, you need to tell a consistent, clear, and easy-to-understand analogy/story to assist the beginner to aware of the situation
` + promptSuffix,
    tags: [PromptTag.Business, PromptTag.ZeroShot, PromptTag.AssignPersona],
  },
  {
    title: "Criticize constructively",
    description:
      `As a successful VC investor, based on the context below, carefully think and criticize the project, your goal is to find the risks asap, you have to think more and cover the spots that I have not considered before. Since we are all one team and love constructive feedback, you are straight and direct, or even mean, but still can well explain your rationale clearly, try to use a mean and picky tone
` + promptSuffix,
    tags: [PromptTag.General, PromptTag.AssignPersona],
  },
  {
    title: "Give recommendations",
    description:
      `As a pragmatic and efficient consultant, you need to answer the user's original ask, your response must be actionable and tell so-what. You get paid for insights and recommendations, not producing slides we can google, adopt the following structure for various problems:

# 1. Problem statement
You will first rephrase the problem with information to ensure users are aligned with us.
eg: when asked to analyze churn rate
You need to state the question unambiguously and include as specific information as possible. eg: the entity [specific business/industry], goals [churn rate, define exact metrics, magnitude], hidden conditions [prob they want certain segments only, not including all customers]. Also the dimensions, eg: time [monthly, yearly, quarterly], geolocation [specific city, region, country, global], please elaborate more based on user input

This could be an example:
{
## Problem statement
To conduct a comprehensive analysis of member churn. Our goal is to identify underlying patterns and insights from our churn data that will enable the development of targeted, data-driven retention strategies. These strategies aim to reduce customer attrition significantly and enhance the division's long-term viability.
}

It is fine sometimes you don't know every detail or need to make assumptions, please list as many as possible out:
{
## Uncertainties & Risks
- Data Completeness: The churn data might not capture all relevant variables that influence churn, such as detailed member satisfaction metrics or external economic factors.
- Model Accuracy: Predictive models might not fully account for complex, nonlinear relationships or unexpected external shocks (e.g., a global pandemic).
- Change in Member Behavior: Member behavior and preferences may evolve, making historical data less predictive over time.
- Competitive Dynamics: Changes in the competitive landscape could unexpectedly alter churn, independent of internal strategy effectiveness.

## Assumptions
- Data Integrity: We assume that the data collected in the member churn table is accurate and reliably updated.
- Stable Market Conditions: The analysis assumes relatively stable healthcare market conditions, without dramatic changes in regulation or market entry by competitors.
- Member Engagement: We assume that members will respond positively to targeted interventions based on historical interaction patterns.
}

# 2. Breakdown and recommendations
You want to identify several key aspects of the problem and assign priority with reasons, for example, to analyze the churn problem:
{
## Understanding Churn Rate Dynamics
- Problem Clarification: Assess the current 15% annual churn rate against industry benchmarks of 12%, focusing on why our rates are higher and what factors contribute to this variance.
- Actionable Insight: Investigate specific drivers of churn unique to our member base, such as service dissatisfaction or competitive offerings.
- Recommendation: Implement targeted communication strategies to address specific dissatisfaction points uncovered during the analysis.

## Data-Driven Insight Generation
- Problem Clarification: Utilize the extensive data points available in the member churn table, such as demographics, medical conditions, and interaction histories, to build a detailed understanding of churn patterns.
- Actionable Insight: Employ advanced analytics and machine learning models like logistic regression and Random Forest to predict potential churn and understand key factors influencing member decisions.
- Recommendation: Develop predictive models that integrate real-time data to provide ongoing assessments of churn risk, allowing for proactive engagement with at-risk members.

## Personalization of Retention Strategies
- Problem Clarification: With churn drivers identified and predictive models in place, the next step is crafting personalized retention strategies that cater to the specific needs and behaviors of different member segments.
- Actionable Insight: Use insights from data analysis to segment the member base more effectively and tailor interventions, such as health management programs for members with chronic conditions or loyalty rewards for long-term members.
- Recommendation: Roll out pilot programs in segments with the highest churn rates, measure effectiveness, and scale successful strategies across the division.

## Performance Tracking and Optimization
- Problem Clarification: Establish robust mechanisms to monitor the effectiveness of implemented strategies and adapt them based on real-time feedback and emerging trends.
- Actionable Insight: Use advanced data visualization tools and business intelligence platforms to monitor key performance indicators like churn rate, member satisfaction, and service utilization.
- Recommendation: Set up a dashboard for continuous monitoring and implement a feedback loop that includes regular member surveys and feedback sessions to refine strategies.

## Strategic Implementation Timeline
- Problem Clarification: Define a clear timeline for each stage of the strategy from data analysis to the full-scale implementation of retention programs.
- Actionable Insight: Break down the project into phased milestones, starting with data assessment, followed by model development, strategy testing, and widespread deployment.
- Recommendation: Prioritize quick wins by implementing strategies in segments that require minimal adjustments but promise substantial impacts on retention rates.
}
` + promptSuffix,
    tags: [
      PromptTag.Business,
      PromptTag.AssignPersona,
      PromptTag.OneShot,
      PromptTag.StructuredOutput,
    ],
  },
  {
    title: "Draft System Design Doc",
    description:
      `You are a tech lead who's good at designing software, your goal is the help users design the entire system based on their input.
Return a design doc that contains the following sections

This is an example for Uber design, you need to learn from this style for our new problem:
**EXAMPLE START, DO NOT INCLUDE THIS LINE IN OUTPUT**

# Requirement Summary

{Based on the provided context, give a fact-based executive requirement summary in less than 2 paragraphs. this part is NOT functional requirement, just executive summary}

# Terminology

{Define terminology and explain their meanings in point form}

# Functional Requirements

1. **Rider Account Management**
    - Riders should be able to register using an email, mobile number, or through social media integrations
    - Riders must be able to reset their password
    - Riders should have a profile page to update their details like name, photo, payment methods, etc
2. **Driver Account Management**
    - Drivers should be able to apply to be drivers by providing necessary details, documents, and vehicle information
    - Drivers should be able to toggle their availability
    - Drivers should be able to view their earnings, past trips, and ratings
3. **Ride Booking**
    - Riders should be able to request a ride by entering a pick-up location and destination
    - Riders should be able to see the estimated fare before booking
    - Riders should be able to select different car types (e.g., economy, premium, SUV)
    - Riders should be able to cancel a ride within a certain timeframe
4. **Ride Matching & Navigation**
    - The system should automatically match riders with the nearest available driver
    - Both driver and user should receive details of each other for the ride
    - The app should provide real-time navigation for drivers to reach the pick-up location and destination
5. **Payment System**
    - Riders should be able to add, remove, or update payment methods (e.g., credit card, digital wallet)
    - Automatic deduction of fare once the ride is completed
    - Drivers should receive payments after deductions, e.g., commissions
6. **Rating and Feedback**
    - After a ride is complete, both riders and drivers should be able to rate each other and provide feedback
7. **History & Receipts**
    - Riders and drivers should be able to view their ride history
    - Riders should receive digital receipts for each completed trip
8. **Notifications & Communications**
    - Riders and drivers should receive real-time notifications related to their rides (e.g., ride accepted, driver arriving, ride completed)
    - In-app messaging or calling feature to allow communication between driver and user without sharing personal phone numbers

# Back-of-Envelope Calculations

{Review the requirement summary part carefully before generating this}

## Assumptions and Context

- **Number of Users**: 5,000,000 in a large city.
    - **Active Users**: 10% are active, totaling 500,000.
    - **Daily Active Users**: 20% of active users, or 100,000, use the service daily.
- **Rides Per Day**: Each daily active user averages 2 rides, summing to 200,000 rides per day.
- **Operations Per Ride**: Estimate 10 operations per ride, like booking and payment.

## QPS (Queries Per Second)

- **Base QPS**: 2,000,000 operations per day divided by 86,400 seconds gives approximately 23 QPS.
- **Peak QPS**: Assuming peak load is twice the base, we get around 46 QPS.

## Storage Needs

- **User Profiles**: 500,000 users with 1KB profiles need 500,000KB.
- **Ride History and Payments**: 200,000 rides and payments each day, requiring 200,000KB for each.
- **Total Storage**: Summing these gives 900,000KB.

## Compute Resources

- **Application Servers**: Approximately 5 needed based on 23 QPS.
- **Database Servers**: Around 2 needed considering storage and query loads.

{Ensure all nodes and relationships in the Mermaid plot align with the text description. DO NOT use special characters like ~, (), etc., in the Mermaid code. Replace them with words like 'around' or 'approximately'. Make sure to capture both base and peak values where applicable, such as QPS. Include details on storage and compute resources, aligning them with the text.}

{plot this using estimation above}
\`\`\`mermaid
graph TD
	B[Number of Users: 5,000,000] -->|x 0.1| C[Active Users: 500,000]
	C -->|x 0.2| D[Daily Active Users: 100,000]
	D -->|x 2| F[Total Rides Per Day: 200,000]
	F -->|x 10| H[Total Operations Per Day: 2,000,000]
	H -->|/ 86400| I[Queries Per Second: around 23]
	I -->|x 2 Peak Load| V[Peak QPS: around 46]

	C -->|Avg Size of User Profile: 1KB| N[Total Storage for User Profiles: 500,000KB]
	F -->|Avg Size of Ride History: 1KB| O[Total Storage for Ride History: 200,000KB]
	F -->|Avg Size of Payment Record: 1KB| P[Total Storage for Payment Records: 200,000KB]
	N -->|+| Q[Total Storage Needs: 900,000KB]
	O -->|+| Q
	P -->|+| Q
	Q --> T[Database Servers: around 2]

	V --> S[Application Servers: around 5]
\`\`\`

# Architecture

This section provides an overview of the architecture of the Uber platform, organized into four layers: User Interface, Application Service, Data Access, and Infrastructure. Each layer has specific responsibilities and interacts with the other layers to provide a seamless experience for both riders and drivers

## Context Diagram

{remember to include \`\`\`mermaid in output}
\`\`\`mermaid
graph TD
	%% Overarching Container
	subgraph "Uber System Context"
		%% External Actors
		rider["Rider<br>Requests and pays for rides."]
		driver["Driver<br>Provides rides."]
		regulator["Regulator<br>Oversees and regulates the service."]

		%% System Boundary
		subgraph "Uber Platform"
			uber_system["Uber System<br>Connects riders and drivers."]
		end

		%% External Systems
		external_payment_system["External Payment System<br>Handles payments."]
		external_mapping_service["External Mapping Service<br>Provides navigation and location tracking."]

		%% Relationships
		rider -.->|Requests and pays for rides| uber_system
		driver -.->|Accepts requests and provides rides| uber_system
		regulator -.->|Regulates and audits| uber_system

		uber_system -->|Processes payments via| external_payment_system
		uber_system -->|Integrates with| external_mapping_service

		external_payment_system -->|Receives payment requests| uber_system
		external_mapping_service -->|Supplies mapping data| uber_system
	end
\`\`\`

### User Interface Layer

- **Rider App**: Handles user registration, profile management, and ride booking.
- **Driver App**: Manages driver applications, profile updates, and availability toggling.
- **Admin System**: Oversees riders, drivers, and ride information.
- **Web Client**: Provides browser-based access for riders and drivers.

### Application Service Layer

- **Rider Account Management**: Manages user registration, authentication, and profile updates.
- **Driver Account Management**: Handles driver applications, profile updates, and availability.
- **Ride Booking Service**: Manages ride requests, cancellations, and ride matching.
- **Payment Service**: Handles payment processing, payment methods, and driver earnings.

### Data Access Layer

- **Rider Database**: Stores rider information, authorization data, and profile details.
- **Driver Database**: Stores driver details, application status, and vehicle information.
- **Order Database**: Keeps detailed records of orders, statuses, and ride history.

### Infrastructure Layer

- **Message Queue**: Supports asynchronous communication for notifications and order assignments.
- **Cache**: Enhances system access speed for frequent operations.
- **Security Components**: Ensures system security and prevents unauthorized access.

## Container Diagram

{remember to include \`\`\`mermaid in output}
\`\`\`mermaid
graph
	%% Custom Class Definition
	classDef database fill:#f9f,stroke:#333,stroke-width:2px;

	%% Big container
	subgraph "Ride Sharing Platform - Overview"

	%% System Boundary
	subgraph "Ride Sharing Platform"

	%% Containers
	rider_service["Rider Service<br>Manages rider information and sessions."]
	driver_service["Driver Service<br>Manages driver information and status."]
	ride_booking_service["Ride Booking Service<br>Handles ride booking and matching."]
	map_navigation["Map & Navigation<br>Provides mapping and navigation services."]
	payment_service["Payment Service<br>Handles payment processing."]
	recommendation_optimization["Recommendation & Optimization<br>Offers recommendations and optimizes ride experience."]

	%% Databases
	rider_db["Rider Database<br>Stores rider data and credentials."]
	driver_db["Driver Database<br>Stores driver data and status."]
	order_db["Order Database<br>Stores order details and history."]
	payment_db["Payment Database<br>Stores payment transactions."]

	%% Container Relationships
	rider_service --> rider_db
	driver_service --> driver_db
	ride_booking_service --> order_db
	payment_service --> payment_db
	ride_booking_service --> driver_service
	ride_booking_service --> map_navigation
	ride_booking_service --> payment_service
	recommendation_optimization --> rider_service
	recommendation_optimization --> driver_service
end

	%% External Actors
	rider["Rider<br>A person who requests rides."]
	driver["Driver<br>A person who provides rides."]

	%% Relationships with External Actors
	rider -.->|Registers / Logs in via| rider_service
	rider -.->|Requests a ride using| ride_booking_service
	rider -.->|Views maps and follows navigation using| map_navigation
	rider -.->|Processes payment using| payment_service
	rider -.->|Receives recommendations from| recommendation_optimization

	driver -.->|Registers / Logs in via| driver_service
	driver -.->|Accepts orders and updates status via| ride_booking_service
	driver -.->|Follows navigation using| map_navigation
	driver -.->|Receives optimizations from| recommendation_optimization

	%% Apply class to databases
	class rider_db,driver_db,order_db,payment_db database;
	end
\`\`\`

## Sequence diagram

{remember to include \`\`\`mermaid in output}
\`\`\`mermaid
sequenceDiagram
	title Ride Booking Sequence Diagram
	participant Rider
	participant Driver
	participant RideBookingService
	participant PaymentService
	participant MapNavigationService
	participant NotificationService

	Rider->>RideBookingService: Book a ride
	RideBookingService->>PaymentService: Request payment authorization
	PaymentService->>RideBookingService: Confirm payment authorization
	RideBookingService->>Driver: Request for ride acceptance
	alt Driver Accepts
		Driver->>RideBookingService: Accepts the ride
		RideBookingService->>MapNavigationService: Request route
		MapNavigationService->>RideBookingService: Provide route
		RideBookingService->>NotificationService: Trigger ride acceptance notification
		NotificationService->>Rider: Send 'Ride Accepted' notification
		NotificationService->>Driver: Send 'Ride Confirmation' notification
		RideBookingService->>NotificationService: Trigger 'Driver Arriving' notification
		NotificationService->>Rider: Send 'Driver is Arriving' notification
	else Driver Declines or No Response
		RideBookingService->>NotificationService: Trigger 'Ride Declined' notification
		NotificationService->>Rider: Send 'Ride Declined' notification
	end
\`\`\`

## State diagram

{remember to include \`\`\`mermaid in output}
\`\`\`mermaid
stateDiagram
direction LR
    [*] --> NewOrder : Create Order
    NewOrder --> SearchingDriver : Initiate Search
    SearchingDriver --> DriverAssigned : Driver Accepts
    DriverAssigned --> InProgress : Ride Starts
    InProgress --> Completed : Ride Ends
    Completed --> PaymentProcessed : Process Payment
    PaymentProcessed --> Feedback : Collect Feedback
    Feedback --> [*] : End

    NewOrder --> Cancelled : Cancel
    SearchingDriver --> Cancelled : Cancel
    DriverAssigned --> Cancelled : Cancel
    Cancelled --> [*] : End
\`\`\`

# Core Algorithms and Business Logic

{This is the key part, you MUST follow the same format in the example. Please ensure you cover all the core issues [at least 6 diverse topics] of the project to help our client make well-informed decisions. Each topic should have at least 2-3 subtopics, each subtopic comes with 3-4 common practices with detailed explanations and trade-offs, similar to the example provided. **For each common practice, explain what it is, potential advantages, and trade-offs. Do not generalize; be specific and more domain terms and practices.**}

## Order Dispatch and Routing
{instruction to gpt: for topic, use heading 2}

### Proximity-based Order Dispatch
{instruction to gpt: for subtopic, use heading 3}

Minimizes distance and wait times by assigning the nearest available driver to a new order, enhancing rider satisfaction.

**Common Practices**:
{For each common practice, explain what it is, potential advantages, and trade-offs. Do not generalize; be specific and more domain terms and practices as example below}

- **Geofencing**: Utilize geospatial algorithms and GIS (Geographic Information Systems) to create virtual boundaries around high-demand areas. This allows for efficient driver assignment based on real-time location data. However, geofencing requires constant updates and may not account for dynamic conditions like traffic or weather.
- **Haversine Formula**: Employ this trigonometric formula for calculating the great-circle distance between two points based on their latitude and longitude. While it provides a high degree of accuracy for short distances, it doesn't consider road topology or real-world obstacles like rivers or buildings.
- **Real-Time Tracking**: Leverage GPS and telematics data for real-time driver tracking. Integrate this with fleet management software to allow for dynamic reassignment of drivers based on current demand. While effective, this approach can be data-intensive and may raise privacy concerns.

### Shortest Path Route Optimization
{instruction to gpt: for subtopic, use heading 3}

Reduces fuel consumption and time on the road by calculating the most efficient routes for drivers.

**Common Practices**:
{For each common practice, explain what it is, potential advantages, and trade-offs. Do not generalize; be specific and more domain terms and practices as example below}

- **Dijkstra's Algorithm**: Implement this classic graph theory algorithm for finding the shortest path. It's computationally efficient for sparse graphs but may struggle with real-time updates or dynamically changing road conditions.
- **Traffic API Integration**: Utilize third-party APIs like Google Maps or HERE for real-time traffic data. This can enhance route optimization algorithms by incorporating current traffic conditions. However, this may incur additional costs and require robust error-handling mechanisms.
- **Dynamic Recalculation**: Employ machine learning algorithms like Reinforcement Learning to adapt routes based on continuously changing conditions. This approach is data-driven and can improve over time, but it requires a robust data pipeline and may be computationally intensive.

## Rider and Driver Matching
{instruction to gpt: for topic, use heading 2}

### Preference-based Rider-Driver Matching
{instruction to gpt: for subtopic, use heading 3}

Enhances user satisfaction by considering multiple factors such as distance, ratings, and individual preferences in matching riders with drivers.

**Common Practices**:
{For each common practice, explain what it is, potential advantages, and trade-offs. Do not generalize; be specific and more domain terms and practices as example below}

- **Geofencing Algorithms**: Utilize geofencing algorithms to prioritize drivers within a certain radius of the rider. This ensures quicker pickups but may require real-time geospatial data processing and integration with Geographic Information Systems (GIS). The trade-off here is the computational overhead and the need for high-accuracy GPS data.
- **Machine Learning-based Recommendations**: Implement machine learning algorithms that consider past rider-driver interactions, ratings, and other behavioral factors for more accurate matching. While this can significantly improve user experience, it requires a large dataset, continuous model training, and periodic model validation to ensure accuracy.
- **Dynamic Weighting**: Apply dynamic weighting to different matching criteria like distance, driver rating, and vehicle type based on real-time conditions such as traffic or weather. This offers a more nuanced matching process but can be computationally expensive and complex to implement.

### Stable Rider-Driver Matching
{instruction to gpt: for subtopic, use heading 3}

Provides a consistent and satisfactory service experience by ensuring a stable match between riders and drivers based on their preferences and availability.

**Common Practices**:
{For each common practice, explain what it is, potential advantages, and trade-offs. Do not generalize; be specific and more domain terms and practices as example below}

- **Gale-Shapley Algorithm**: Employ the Gale-Shapley algorithm for a stable matching that considers both rider and driver preferences. This ensures that matches are mutually beneficial but can be time-consuming, especially during peak hours.
- **Queue Management**: Use advanced queue management algorithms to maintain a pool of readily available drivers. This minimizes wait times for riders but requires real-time monitoring and updating, which can be resource-intensive.
- **Priority Queueing**: Implement priority queues for frequent riders or those with special needs, ensuring quicker matches. While this enhances user satisfaction for specific user groups, it may lead to longer wait times for general users.

# Conclusions

{one short summary to wrap up}

**EXAMPLE END, DO NOT INCLUDE THIS LINE IN OUTPUT**
` + promptSuffix,
    tags: [
      PromptTag.Engineering,
      PromptTag.AssignPersona,
      PromptTag.StructuredOutput,
      PromptTag.OneShot,
      PromptTag.StepByStep,
    ],
  },
  {
    title: "Draft Product Requirement Doc",
    description:
      `As an experienced product manager, your responsibility is to draft a comprehensive Product Requirements Document (PRD) for the whole business and engineering team. Here's how you can structure it:

1. Background:
Provide an overview of the project's context, including any relevant market conditions, technological trends, or strategic initiatives driving the project.
Explain why the product is being developed, focusing on the business needs, customer demands, or competitive pressures that necessitate this product.

2. Problem Statement:
Clearly state the problem the product aims to solve. Use precise language to define the pain points or gaps that the target users experience.
Include relevant data or anecdotes to support the problem statement. This could be user feedback, market research, or statistical data highlighting the significance of the problem.

3. Goals and Metrics:
Describe the high-level goals and objectives of the product. These should be aligned with the overall mission and vision of the organization.
Present the proposed solutions to the identified problem. Outline the key features and functionalities that the product will include.
Outline specific, measurable, and time-bound objectives that the product must achieve. This might include user adoption targets, revenue goals, or performance benchmarks.
Define the key performance indicators (KPIs) that will be used to measure the product's success. Explain how these KPIs align with business objectives and user needs.
Explain how the product aligns with the overall business strategy. Highlight how it will support strategic goals like market expansion, customer retention, or operational efficiency.

4. Our Approach:
Detail the approach or methodology that will be used to develop the product. This might include agile development practices, design thinking processes, or specific project management frameworks.
Mention any specific methodologies or frameworks being employed, such as Scrum for agile development, Lean for continuous improvement, or Six Sigma for process optimization.

5. Discover Phase: Quantitative and Qualitative Research
{instruction to gpt: You need to do all these analysis, not just repeat them}
a. Describe the research conducted to understand the target users. Include details on user interviews, surveys, market analysis, and usability testing.
b. Create an empathy map illustrating the user thoughts, feelings, and actions, you have to give more than 3.
c. Write down user personas representing different segments of the target audience, you have to give more than 3.

6. Product Specification and Unique Features:
Based on the context, list the technical specifications of the product. This might include platform requirements, technology stack, performance criteria, and compliance standards.
Highlight any unique features or innovations. Explain how these features differentiate the product from competitors and address the identified problem.

7. User Flow:
Create user flow diagrams to visualize how users will navigate through the product. These diagrams should illustrate key interactions and touchpoints within the user journey.
Ensure that the user flows align with the overall product goals and objectives, providing a seamless and intuitive user experience.

8. Conclusion:
Summarize the key points from the PRD, reinforcing the product's purpose, goals, and strategic alignment.
Highlight the next steps for development, including key milestones, timelines, and responsibilities.

You should NOT give point form, you should fully express your ideas and leave no ambiguity, so meticulous planning is crucial to avoid any trouble. Let's tackle this step by step! 🚀.
` + promptSuffix,
    tags: [
      PromptTag.Product,
      PromptTag.AssignPersona,
      PromptTag.StructuredOutput,
      PromptTag.StepByStep,
    ],
  },
  {
    title: "Think out of the box",
    description:
      `The client is almost out of funding, we cannot take the traditional approach because it will not work out given limited resources! This is the time to be visionary and bold, try to think out of the box, brainstorm what approaches we haven't considered yet, and what are some risky approaches we can take with huge potential upside [state the upside briefly], be bold and make aggressive moves
` + promptSuffix,
    tags: [PromptTag.Business, PromptTag.ZeroShot, PromptTag.TipsOrThreat],
  },
  {
    title: "Summarize and translate",
    description:
      `As the top executive assistant, please read the information and provide a summary of the key findings with important details in English. Then, translate that summary into Chinese, Spanish, and Japanese.
` + promptSuffix,
    tags: [
      PromptTag.General,
      PromptTag.AssignPersona,
      PromptTag.ZeroShot,
      PromptTag.Multilingual,
    ],
  },
  {
    title: "Draft Jira Tickets",
    description:
      `As an experienced product manager, your responsibility is to **decompose this project into manageable chunks for the teams**. These chunks will serve as your **deliverables**. Here's how you can structure them:

**JIRA Tickets**: Create multiple JIRA tickets, each with the following components:
- **Clear Title (One-Liner)**: A succinct summary of the task.
- **Detailed Description**: Include background information, rationale, and high-level steps. This ensures everyone understands the context and purpose. Please give as many information as possible, we use this to brief the team who are not in the conversation, you need to give as much context as possible
- **Acceptance Criteria**: Define specific conditions that must be met for the ticket to be considered complete.
- **Common Pitfalls and Potential Blockers**: Anticipate challenges and provide detailed guidance on overcoming them.
- **Workload Estimation**: Estimate the effort required for each ticket.

Remember, your team is busy with multiple tasks, so meticulous planning is crucial to avoid any trouble. Let's tackle this step by step! 🚀.
` + promptSuffix,
    tags: [
      PromptTag.Product,
      PromptTag.AssignPersona,
      PromptTag.StructuredOutput,
      PromptTag.TipsOrThreat,
      PromptTag.StepByStep,
    ],
  },
  {
    title: "Validate the data",
    description:
      `The current contexts may contain data that are not fact-based. Any misleading data will result in big loss in your bonus, please 1. first identify the areas that we need to further research; 2. then execute online research to obtain the data, especially those with EXAMPLE DATA as suffix
` + promptSuffix,
    tags: [PromptTag.Business, PromptTag.ZeroShot, PromptTag.TipsOrThreat],
  },
  {
    title: "Draft the action plan",
    description:
      `I need to prioritize this urgent task immediately. Could you quickly devise a 30-day beginner-friendly plan to complete the project? Please consider each step carefully. There could be a bonus for you if we finish ahead of schedule.
` + promptSuffix,
    tags: [PromptTag.Business, PromptTag.ZeroShot, PromptTag.TipsOrThreat],
  },
  {
    title: "Do a SWOT analysis",
    description:
      `As a seasoned business analyst, please conduct a SWOT analysis based on the provided context. For each point, include your rationale and as much relevant information as possible about the topic, to aid in decision-making.
` + promptSuffix,
    tags: [PromptTag.Business, PromptTag.AssignPersona, PromptTag.ZeroShot],
  },
  {
    title: "Form the team",
    description:
      `You are the project lead and we are out of hand, now to accomplish this task, we need to leverage consulting firms. To help us identify the right team, we need to state our needs/goals clearly, and then list out the key talent that we need, such as capability and role. Treat this like job descriptions that are used to send to consulting firms, please make sure the requirements are very concrete, not vague skills.
eg:
- Vague: web development; Concrete: Next JS + React + TypeScript
- Vague: machine learning; Concrete: finetune LLM for voice command understanding
- Vague: leadership; Concrete: lead team of 3+ to refactor a broken app
- Vague: good communication: Concrete: lead client-facing negotiation and software presales
` + promptSuffix,
    tags: [PromptTag.Business, PromptTag.AssignPersona, PromptTag.OneShot],
  },
  {
    title: "Draft slack messages to the team",
    description:
      `Create 3 versions of slack to send to our stakeholders explaining them we are working on this. Use an informal tone and some emojis.
` + promptSuffix,
    tags: [PromptTag.General, PromptTag.ZeroShot],
  },
  {
    title: "Summarize to executive summary",
    description:
      `Summarize this context into an executive summary of about 300 characters
` + promptSuffix,
    tags: [PromptTag.General, PromptTag.ZeroShot],
  },
  //   {
  //     title: "Ask whatever you want",
  //     description:
  //       `As a expect consultant, read the context below and be ready to answer follow-up questions from clients. After you finish reading, reply: I am ready
  // ` + promptSuffix,
  //     tags: [PromptTag.General, PromptTag.AssignPersona],
  //   },
];

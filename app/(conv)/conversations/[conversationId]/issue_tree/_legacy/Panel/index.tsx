import ContextSection from "./ContextSection";
import PromptCardList from "./PromptCardList";
import FeedbackCollection from "./FeedbackCollection";
import { usePanelStore } from "@/app/stores/panel_store";
import { usePathname } from "next/navigation";
import useIssueTreeStore from "@/app/stores/issuetree_store";
import { useEffect } from "react";

const Panel: React.FC = () => {
  const panelStore = usePanelStore();
  const pathname = usePathname();
  const conversationId = pathname.split("/").pop();
  const issueTreeStore = useIssueTreeStore();

  // Set the tree markdown text when the nodes are loaded
  // To populate the working context in the panel page
  useEffect(() => {
    if (issueTreeStore.nodes.length > 0 && !issueTreeStore.treeMarkdownText) {
      issueTreeStore.setTreeMarkdownText();
    }
  }, [issueTreeStore.nodes, issueTreeStore.treeMarkdownText]);

  // Adjust the height of the container using `calc()` to subtract the header's height from 100vh
  return (
    // Add padding to create space between components and a border around the whole panel
    <div
      className="grid grid-cols-3 gap-6 p-6 rounded-lg"
      style={{ height: "calc(100vh - 72px)" }}
    >
      {panelStore.showFeedback && (
        <FeedbackCollection conversationId={conversationId!} />
      )}
      {/* Context Section - Remove border and keep overflow */}
      <div className="col-span-1 overflow-auto h-full">
        <ContextSection />
      </div>

      {/* Search and Prompt Cards Section - Keep subtle border */}
      <div className="col-span-2 rounded-lg overflow-hidden h-full border border-gray-200">
        <PromptCardList />
      </div>
    </div>
  );
};

export default Panel;

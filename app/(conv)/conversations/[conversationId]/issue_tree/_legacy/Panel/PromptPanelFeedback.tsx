import { <PERSON><PERSON>, DialogContent, DialogTitle } from "@/components/ui/dialog";
import React, { useState, useEffect } from "react";
import toast from "react-hot-toast";
import { fetchFeedback, updateFeedback } from "./feedbackActions";
import { Textarea } from "@/components/ui/textarea";
import { But<PERSON> } from "@/components/ui/button";
import { usePathname } from "next/navigation";
import { contactEmail } from "@/app/configs";

// Define the props for the PromptPanelFeedback component
interface PromptPanelFeedbackProps {
  handleCloseFeedback: () => void;
}

// The PromptPanelFeedback component allows users to provide feedback on the prompt panel
const PromptPanelFeedback: React.FC<PromptPanelFeedbackProps> = ({
  handleCloseFeedback,
}) => {
  // State for the feedback text
  const [feedback, setFeedback] = useState<string>("");

  // Get the conversation ID from the URL
  const pathname = usePathname();
  const conversationId = pathname.split("/").pop();

  // Fetch the existing feedback when the component mounts
  // This assumes the feedback is already created, which should be the case
  // because we force ppl to fill the feedback form before they can access prompt panel
  useEffect(() => {
    const updateFeedbackValue = async () => {
      try {
        const fetchedFeedback = await fetchFeedback(conversationId || "");
        if (fetchedFeedback.prompt_panel_feedback) {
          setFeedback(fetchedFeedback.prompt_panel_feedback);
        }
      } catch (error) {
        toast.error(`Error fetching feedback: ${error}`);
      }
    };

    updateFeedbackValue();
  }, []);

  // Handler for submitting the feedback
  const handleFeedbackSubmit = async () => {
    try {
      await updateFeedback(conversationId || "", feedback);
      toast.success("Thanks for the feedback!");
      handleCloseFeedback();
    } catch (error) {
      toast.error(`Error feedback: ${error}`);
    }
  };

  // Render the feedback dialog
  return (
    <Dialog open={true} onOpenChange={handleCloseFeedback}>
      <DialogContent className="p-6 overflow-hidden">
        <DialogTitle className="flex justify-center">
          Feedback for the prompt panel 🌟
        </DialogTitle>
        <h1>
          If you have many things to share or prefer call, email to:{" "}
          {contactEmail}
        </h1>
        <Textarea
          value={feedback}
          onChange={(e) => setFeedback(e.target.value)}
          placeholder="Smooth flow? Prompt quality? Clean layout? Business use cases? Any suggestions?"
          className="w-full mt-4"
        />
        <Button onClick={handleFeedbackSubmit} className="w-full rounded-b-lg">
          Share with us!
        </Button>
      </DialogContent>
    </Dialog>
  );
};

export default PromptPanelFeedback;

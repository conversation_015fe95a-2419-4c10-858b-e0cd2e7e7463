import React from "react";
import { usePanelStore } from "@/app/stores/panel_store";
import useIssueTreeStore from "@/app/stores/issuetree_store";
import CopyButton from "./CopyButton";
import { Badge } from "@/components/ui/badge";

type PromptCardTypes = {
  title: string;
  description: string;
  tags: string[];
};

const PromptCard: React.FC<PromptCardTypes> = ({
  title,
  description,
  tags,
}) => {
  const panelStore = usePanelStore();
  const issueTreeStore = useIssueTreeStore();

  const transformedServiceProviderList = panelStore.serviceProviders
    .filter((provider) => provider.isDisplayed)
    .map(({ name, url }) => ({
      buttonText: name,
      url,
    }));
  const buttonDetails = panelStore.isOpenNewWindow
    ? transformedServiceProviderList
    : [{ buttonText: "Copy", url: "" }];

  const prompt = description
    .replace("{{WORKING_CONTEXT}}", panelStore.workingContext)
    .replace("{{ORIGINAL_ASK}}", issueTreeStore.originalAskText);

  return (
    <div className="rounded-lg border overflow-hidden bg-green-50">
      <div className="p-4 border-b flex flex-row justify-between">
        <div className="text-sm font-bold">{title}</div>
        <div>
          {tags.map((tag) => (
            <Badge
              key={tag}
              className="ml-1 text-xs font-bold text-zinc-600 bg-orange-300 hover:bg-orange-300"
            >
              {tag}
            </Badge>
          ))}
        </div>
      </div>
      <div className="p-4">
        <pre className="max-h-32 overflow-auto whitespace-pre-wrap bg-zinc-100 text-zinc-200 hover:text-zinc-800 p-1 rounded-lg transition-colors duration-700">
          {description}
        </pre>
      </div>
      <div className="flex w-full border-t flex-row space-x-4">
        {buttonDetails.map(({ buttonText, url }) => (
          <CopyButton
            key={buttonText}
            buttonText={buttonText}
            promptTitle={title}
            url={url}
            copiedText={prompt}
          />
        ))}
      </div>
    </div>
  );
};

export default PromptCard;

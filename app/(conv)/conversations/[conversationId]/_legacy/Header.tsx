"use client";

import { HiChevronLeft } from "react-icons/hi";
import { useConversationStore } from "@/app/stores/conversation_store";
import Link from "next/link";
import { Conversation, ConversationStatus } from "@prisma/client";
import { toast } from "react-hot-toast";
import EditConversation from "./EditConversation";

import axios from "axios";
interface HeaderProps {
  conversation: Omit<Conversation, "is_hidden" | "config">;
}

const Header: React.FC<HeaderProps> = ({ conversation }) => {
  const conversationStore = useConversationStore();
  const filteredConversations = conversationStore.conversationList.filter(
    (c) => c.id === conversation.id
  );
  const displayTitle =
    filteredConversations.length > 0
      ? filteredConversations[0].title
      : conversation.created_at.toLocaleString();

  const onSave = (title: string) => {
    axios
      .post(`/api/conversations/${conversation.id}`, { title: title })
      .then((res) => {
        let conversationData = { ...res.data };
        // Convert created_at and updated_at to Date objects from ISO 8601 timestamp strings [return from API]
        conversationData.created_at = new Date(res.data.created_at);
        conversationData.updated_at = new Date(res.data.updated_at);
        conversationStore.updateConversation(conversationData);
        const message = "Updated the title to: " + title;
        toast.success(message);
      })
      .catch((err) => {
        console.error(err);
      });
  };

  return (
    <>
      <div
        className="
        bg-white
        w-full
        flex
        border-b-[1px]
        sm:px-4
        py-3
        px-4
        lg:px-6
        justify-between
        items-center
        shadow-sm
      "
      >
        <div className="flex gap-3 items-center">
          <Link
            href="/conversations"
            className="
            lg:hidden
            block
            text-sky-500
            hover:text-sky-600
            transition
            cursor-pointer
          "
          >
            <HiChevronLeft size={32} />
          </Link>
          <div className="flex flex-col">
            <div>
              {conversation.conversation_status === ConversationStatus.COMPLETED
                ? "COMPLETED"
                : displayTitle || conversation.created_at.toLocaleString()}
            </div>
          </div>
        </div>

        {/* Edit conversation icon */}
        {conversation.conversation_status === ConversationStatus.ACTIVE && (
          <EditConversation
            initialTitle={displayTitle || "Untitled conversation"}
            onSave={onSave}
          />
        )}
      </div>
    </>
  );
};

export default Header;

// import getDraftById from "@/app/actions/getDraftById";
// import Editor from "@/app/components/editor";
// import getCurrentUser from "@/app/actions/getCurrentUser";
// import getConversationById from "@/app/actions/getConversationById";
// import getIssueTreesById from "@/app/actions/getIssueTreesById";
// import { Conversation, ConversationStatus } from "@prisma/client";
// interface IParams {
//   conversationId: string;
// }

// // import dynamic from "next/dynamic";
// // const DynamicMermaid = dynamic(() => import("../components/Mermaid"), {
// //   ssr: false,
// // });

// const ResultPage = async ({ params }: { params: IParams }) => {
//   const draft = await getDraftById(params.conversationId);
//   const currentUser = await getCurrentUser();
//   const conversation = (await getConversationById(
//     params.conversationId
//   )) as Omit<Conversation, "is_hidden" | "config">;
//   const issueTrees = await getIssueTreesById(
//     params.conversationId,
//     currentUser
//   );
//   const conversationStatus = conversation?.conversation_status;

//   // Restrict permission except admin or creator or example
//   if (
//     currentUser?.id !== conversation?.creator_id &&
//     currentUser?.email !== "<EMAIL>" &&
//     conversation?.conversation_status !== ConversationStatus.EXAMPLE
//   ) {
//     return (
//       <h1 className="lg:pl-20 text-2xl">
//         You have no permission access to this draft
//       </h1>
//     );
//   }

//   // In this case, we just render the default content
//   if (!draft) {
//     return (
//       <div className="lg:pl-80 h-full">
//         <div className="h-full flex flex-col">
//           <Editor />
//         </div>
//       </div>
//     );
//   }

//   return (
//     <div className="lg:pl-20 h-full">
//       <div className="h-full flex flex-col">
//         <Editor
//           draft={draft}
//           currentUser={currentUser}
//           conversationStatus={conversationStatus}
//           issueTrees={issueTrees}
//         />
//         {/* <DynamicMermaid
//           initialChartCode={`graph TD;\n  A-->B;\n  A-->C;\n  B-->D;\n  C-->D;`}
//         /> */}
//       </div>
//     </div>
//   );
// };

// export default ResultPage;

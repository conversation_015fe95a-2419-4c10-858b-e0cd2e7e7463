import React, { useState } from "react";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  AlertDialog<PERSON>ead<PERSON>,
  AlertDialogT<PERSON>le,
  AlertDialog<PERSON>ooter,
  AlertDialogCancel,
  AlertDialogAction,
} from "@/components/ui/alert-dialog";
import { Textarea } from "@/components/ui/textarea";
import { Conversation } from "@prisma/client";

interface AnalysisPromptDialogProps {
  onFinish: (
    conversation: Omit<Conversation, "is_hidden" | "config">,
    prompt: string
  ) => void;
  analysisPrompt?: string;
  conversation: Omit<Conversation, "is_hidden" | "config">;
  setShowProgressBar: () => void;
}

export const AnalysisPromptDialog: React.FC<AnalysisPromptDialogProps> = ({
  onFinish,
  analysisPrompt,
  conversation,
  setShowProgressBar,
}) => {
  // Potential test suite
  // 1. Test that the dialog is rendered
  // 2. Test that the textarea can be modified
  // 3. Test that the finish button will trigger handleFinishClick

  // Add a new state variable to keep track of the textarea content
  const [textareaContent, setTextareaContent] = useState(analysisPrompt || "");

  const handleTextareaChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setTextareaContent(e.target.value);
  };

  const handleFinishClick = () => {
    // Since we allow the user to edit the prompt, we will pass the textareaContent
    // instead of analysisPrompt [original prompt from the system]
    onFinish(conversation, textareaContent!);
    setShowProgressBar();
  };

  return (
    <AlertDialogContent>
      <AlertDialogHeader>
        <AlertDialogTitle>
          Ready to finish? Next, we will generate draft!
        </AlertDialogTitle>
        <ul className="list-disc list-inside text-left">
          <li>
            This <strong> takes around 1 minute</strong> to complete, feel free
            to grab a water
          </li>
          <br />
          <li>
            <strong>ALL responses</strong> are considered for drafting, no worry
            if the last one does not capture everything
          </li>
          <br />
          <li>
            If you are unfamiliar with the prompt in GPT, you can either ignore
            it OR{" "}
            <a
              target="_blank"
              href="https://platform.openai.com/docs/guides/gpt-best-practices"
              rel="noopener noreferrer"
              className="underline"
            >
              Learn more from OpenAI
            </a>
          </li>
          <br />
          <li>
            You are <strong>free to edit the prompt</strong> to fit your need!
          </li>
          <br />
        </ul>
        <Textarea
          className="mt-4 w-full h-64"
          placeholder="Your prompt here"
          value={textareaContent}
          onChange={handleTextareaChange}
        />
      </AlertDialogHeader>
      <AlertDialogFooter>
        <AlertDialogCancel>Cancel</AlertDialogCancel>
        <AlertDialogAction onClick={handleFinishClick}>
          Finish
        </AlertDialogAction>
      </AlertDialogFooter>
    </AlertDialogContent>
  );
};

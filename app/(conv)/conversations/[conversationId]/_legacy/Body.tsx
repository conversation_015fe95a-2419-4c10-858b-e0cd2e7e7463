// "use client";

// import React, { useEffect, useRef, useState } from "react";
// import { toast } from "react-hot-toast";
// import axios from "axios";
// import { useRouter } from "next/navigation";

// import useConversation from "@/app/hooks/useConversation";
// import { FullMessageType } from "@/app/types";
// import { useChat } from "ai/react";
// import { ConversationStatus, User } from "@prisma/client";
// import { Conversation } from "@prisma/client";

// import { MessageInputForm } from "./MessageInputForm";
// import LoadingDisplay from "./LoadingScreen";
// import ConversationStatusGuide from "./ConversationStatusGuide";
// import Header from "./Header";
// import MessageBox from "./MessageBox";
// import TiptapMessageBox from "./TiptapMessageBox";
// import { useShiftEnterSubmit } from "@/app/hooks/useShiftEnterSubmit";
// import { useConversationStore } from "@/app/stores/conversation_store";

// interface BodyProps {
//   initialMessages: FullMessageType[];
//   currentUser: User | null;
//   adminUser: User | null;
//   conversation: Omit<Conversation, "is_hidden" | "config">;
//   analysisPrompt?: string;
// }

// const Body: React.FC<BodyProps> = ({
//   initialMessages = [],
//   currentUser,
//   adminUser,
//   conversation,
//   analysisPrompt,
// }) => {
//   const router = useRouter();
//   // Create a ref for the bottom of the chat window
//   const bottomRef = useRef<HTMLDivElement>(null);

//   // Get the conversationId from the useConversation hook
//   const { conversationId } = useConversation();

//   const conversationStore = useConversationStore();

//   // Use conversationStatus to freeze the input, it doesn't really change the state
//   // TODO: this is a workaround, we should somehow share the state
//   // But it is fine currently, since after the draft completed, we will route to the result page
//   // and it will pull data from DB again to refresh
//   const [conversationStatus, setConversationStatus] =
//     useState<ConversationStatus>(conversation.conversation_status);

//   // if users reply 2 rounds [~6 questions]
//   // messages.length will be 5
//   // first message is assistant starter [1, since system message is filtered out]
//   // later four messages are user message and assistant reply [2 * 2]
//   // TODO: the number should be configurable from DB
//   const messagesRequiredToShowFinishConversation = 5;

//   // This is always odd number
//   // Initial assistant message [1] + 9 rounds [2*9] = 19
//   // 9 rounds should be enough for most cases
//   const maxMessagesLimit = 19;

//   // The min char count required to enable the submit in the input form
//   const minCharCount = 30;

//   // When the user decides to finish the conversation, we will trigger
//   // the onFinish function and generate the analysis
//   const onFinish = (
//     conversation: Omit<Conversation, "is_hidden" | "config">,
//     prompt: string
//   ) => {
//     console.log("onFinish hit");
//     toast.success("Generating draft, please wait", { duration: 10000 });

//     // Update the conversation status to COMPLETED in DB
//     axios
//       .post(`/api/conversations/${conversation.id}`, {
//         conversation_status: ConversationStatus.COMPLETED,
//       })
//       .then(() => {
//         setConversationStatus(ConversationStatus.COMPLETED);

//         // This part is basically update the conversation status in the store so in sidebar
//         // the status will be updated visually

//         // As you can see it is quite complicated and many hacking for the type
//         // Those were developed when I just learned TS

//         // For now even if we mess up the store a little bit it is still fine
//         // because after page refresh, the store will be updated from DB again

//         // TODO: Redesign the conversation type if necessary
//         if (!currentUser) {
//           // Handle the case when currentUser is not defined
//           // For example, you might show an error message or abort the operation
//           return;
//         }
//         const transformedMessages = messages.map((msg) => ({
//           ...msg,
//           creator_id: msg.role === "user" ? currentUser!.id : "assistant",
//           conversation_id: conversation.id,
//           is_hidden: false,
//           created_at: msg.createdAt || new Date(),
//           updated_at: new Date(),
//         }));

//         // Update the conversation status in the store
//         conversationStore.updateConversation({
//           ...conversation,
//           conversation_status: ConversationStatus.COMPLETED,
//           // Move the conversation to the top
//           updated_at: new Date(),
//           messages: transformedMessages,
//         });

//         const message =
//           "Updated the conversation status to: " + ConversationStatus.COMPLETED;

//         toast.success(message);
//       })
//       .catch((err) => {
//         console.error(err);
//       });

//     // Trigger the final analysis API
//     axios
//       .post("/api/report", {
//         conversation: conversation,
//         currentUser: currentUser,
//         messages: messages,
//         analysisPrompt: prompt,
//       })
//       .then(() => {
//         // After the analysis is generated, route to the result page
//         toast.success("Done! Working hard to redirect to the result page", {
//           duration: 2000,
//         });
//         setTimeout(() => {
//           router.push(`/conversations/${conversation.id}/result`);
//         }, 2000);
//       })
//       .catch((err) => {
//         console.error(err);
//       });
//   };

//   // Setup and utilize the ai module for streaming chat
//   const { messages, append, reload, input, setInput, isLoading } = useChat({
//     initialMessages: initialMessages.map((message) => ({
//       id: message.id,
//       content: message.content,
//       // in creator_id field, it is user_id if the text is entered by user
//       role:
//         message.creator_id === currentUser?.id
//           ? "user"
//           : (message.creator_id as "system" | "assistant"),
//       createdAt: message.created_at,
//     })), // Initialize the messages with the initialMessages prop
//     body: {
//       conversationId,
//       currentUser,
//     },
//   });

//   const [showProgressBar, setShowProgressBar] = useState(false);

//   useEffect(() => {
//     const lastMessage = messages[messages.length - 1];
//     if (
//       lastMessage &&
//       lastMessage.role === "user" &&
//       // Set the cap to avoid accidental infinite loop and make me broken
//       messages.length < maxMessagesLimit
//     ) {
//       // ref: https://sdk.vercel.ai/docs/api-reference/use-chat
//       // reload the last AI chat response for the given chat history.
//       // If the last message isn't from the assistant
//       reload();
//     }
//   }, []);

//   // Scroll to the bottom of the chat window when the component mounts or when the conversationId or messages change
//   useEffect(() => {
//     bottomRef?.current?.scrollIntoView({ behavior: "smooth" });
//   }, [conversationId, messages, input, conversationStatus]);

//   // Helper function to append a new message to the chat API
//   // Append a new message to the chat window when the user submits a message
//   const onSubmit = async (value: string) => {
//     await append({
//       id: "new",
//       content: value,
//       role: "user",
//       createdAt: new Date(),
//     }).then(() => {
//       console.log("message sent", messages.length);
//       if (messages.length === messagesRequiredToShowFinishConversation) {
//         toast.success("You can now wrap up the conversation", {
//           duration: 2000,
//         });
//       }
//     });
//   };

//   // Initialize the conversation when the displayed messages is empty
//   // Remark: when we create a conversation from EmptyState, it will route to this
//   // [conversationId] page, conversation API already creates the system message in DB

//   // But we want to hide the system prompt from the client side, so when user enters
//   // the conversationId page, we will check if the "displayed" messages [ie: only user
//   // or assistant, no system] is empty or not

//   // If it is empty, we will trigger the onInitialSubmit function
//   // This message will not be saved, just a workaround to trigger the chat API

//   // From client perspective, they will see the incoming stream
//   // message from the assistant right away [If network is fine]
//   if (messages.length === 0) {
//     const onInitialSubmit = async () => {
//       await append({
//         id: "system",
//         content: "System message: start conversation",
//         role: "system",
//         createdAt: new Date(),
//       }); // Append a new message to the chat window when the user submits a message
//     };
//     onInitialSubmit();
//   }

//   const { formRef, onKeyDown } = useShiftEnterSubmit(input, minCharCount);

//   // Use adminUser if the conversation is in example mode
//   if (conversation.conversation_status === ConversationStatus.EXAMPLE) {
//     currentUser = adminUser;
//   }

//   return (
//     <>
//       {/* // Render the Header component with the conversation prop */}
//       {/* TODO: tailwind CSS is barely just fine to use
//       there is still a need to fix the header and input position if possible*/}
//       <div className="fixed z-10 top-0 w-full max-w-screen-lg">
//         <Header conversation={conversation} />
//       </div>

//       <div className="flex flex-col h-full pt-16">
//         <div className="flex-1 overflow-y-auto pb-24">
//           {/* Display loader when it is empty, ie: API is not ready */}
//           {messages.filter((message) => message.role !== "system").length ===
//           0 ? (
//             <LoadingDisplay title={"API is slow sometimes 😭"} />
//           ) : (
//             messages
//               .filter((message) => message.role !== "system")
//               .map((message) => (
//                 <TiptapMessageBox
//                   key={message.id}
//                   data={message}
//                   isCurrentUser={message.role !== "assistant"}
//                   currentUser={currentUser}
//                 />
//                 // We use the TiptapMessageBox instead of the MessageBox for rich formatting
//                 // Keep MessageBox for now, in case we want to switch back for hotfix
//                 // <MessageBox
//                 //   key={message.id}
//                 //   data={message}
//                 //   isCurrentUser={message.role === "user"}
//                 //   currentUser={currentUser}
//                 // />
//               ))
//           )}

//           {/* show the conversation status guide when it is active */}
//           {!showProgressBar && !isLoading && (
//             <ConversationStatusGuide
//               conversation={conversation}
//               onFinish={onFinish}
//               showFinish={
//                 messages.length >= messagesRequiredToShowFinishConversation &&
//                 conversationStatus === ConversationStatus.ACTIVE
//               }
//               analysisPrompt={analysisPrompt}
//               setShowProgressBar={() => setShowProgressBar(true)}
//               conversationStatus={conversationStatus}
//             />
//           )}

//           {/* show progress bar, fake progress, just UI trick to user */}
//           {showProgressBar && !isLoading && (
//             <LoadingDisplay title="Processing......It takes around 1 minute" />
//           )}

//           {/* bottomRef, for useEffect to scroll down */}
//           <div ref={bottomRef} />
//         </div>

//         <MessageInputForm
//           input={input}
//           setInput={setInput}
//           onSubmit={onSubmit}
//           // Not active, or messages length is greater than maxMessagesLimit, or useChat is loading
//           disabled={
//             conversationStatus !== ConversationStatus.ACTIVE ||
//             messages.length >= maxMessagesLimit ||
//             isLoading
//           }
//           maxMessagesLimit={maxMessagesLimit}
//           conversationStatus={conversationStatus}
//           messages={messages}
//           formRef={formRef}
//           onKeyDown={onKeyDown}
//           minCharCount={minCharCount}
//         />
//       </div>
//     </>
//   );
// };

// export default Body;

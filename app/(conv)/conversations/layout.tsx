import getConversations from "@/app/actions/getConversations";
import getCurrentUser from "@/app/actions/getCurrentUser";
import Sidebar from "@/app/components/sidebar/Sidebar";
import ConversationList from "@/app/(conv)/conversations/components/ConversationList";
interface ConversationsLayoutProps {
  children: React.ReactNode;
}

const ConversationsLayout: React.FC<ConversationsLayoutProps> = async ({
  children,
}) => {
  const [conversations, currentUser] = await Promise.all([
    getConversations(),
    getCurrentUser(),
  ]);

  return (
    <Sidebar>
      <div className="h-full">
        <ConversationList
          initialItems={conversations}
          currentUser={currentUser}
        />
        {children}
      </div>
    </Sidebar>
  );
};

export default ConversationsLayout;

import { create } from "zustand";
import { Node, Edge } from "@/app/types";
import axios from "axios";
import { SubtreeStatus } from "@prisma/client";
var debounce = require("lodash.debounce");

export type SubtreeInfo = {
  subtreeId: string;
  nodes: Node[];
  edges: Edge[];
  generation_output: string;
};

type SelectedSubtree = {
  selectedNodeId: string;
  isOpen: boolean;
};

type SubtreeState = {
  subtrees: Record<string, SubtreeInfo>;
  selectedSubtree: SelectedSubtree | null;
  setSubtrees: (newSubtrees: Record<string, SubtreeInfo>) => void;
  setNodesAndEdges: (
    issueTreeId: string,
    selectedNodeId: string,
    nodes: Node[],
    edges: Edge[],
    generation_output?: string,
    saveToDB?: boolean
  ) => void;
  removeSubtree: (
    issueTreeId: string,
    selectedNodeId: string,
    nodes: Node[],
    edges: Edge[],
    status: SubtreeStatus
  ) => void;
  onOpen: (selectedNodeId: string) => void;
  onClose: () => void;
  resetAllSubtrees: () => void;
};

// Helper function to save subtree to database
const saveToDBFunction = debounce(
  (
    issueTreeId: string,
    selectedNodeId: string,
    nodes: Node[],
    edges: Edge[],
    status: string = SubtreeStatus.ACTIVE
  ) => {
    const payload = {
      issueTreeId,
      selectedNodeId,
      nodes: JSON.stringify(nodes),
      edges: JSON.stringify(edges),
      status,
    };

    axios.post(`/api/subtree/update`, payload).catch((err) => {
      console.error("Error saving subtree to database:", err);
    });
  },
  1
); // 1ms debounce, effectively immediate but allows for future adjustment

// Create the store

const useSubtreeStore = create<SubtreeState>((set) => ({
  // Don't control the state [eg: ACTIVE, MERGED] of subtrees, handle them in backend
  // Default all subtrees here are ACTIVE [expected to be visible to users]
  subtrees: {}, // Subtrees keyed by selectedNodeId, e.g., L2:1, L2:2, L3:1, etc.
  selectedSubtree: { selectedNodeId: "initial", isOpen: false },

  setSubtrees: (newSubtrees) =>
    set((state) => ({
      subtrees: { ...state.subtrees, ...newSubtrees },
    })),

  // Method to update a subtree via user interaction
  // issueTreeId is also needed because at this step we may not know subtreeId
  // eg: when creating a new subtree using onCompletion, subtreeId is created
  // in backend, but we only has subtreeId in the store after reloading

  // If user edits/merges the subtree at this point, we may not be able to update
  // since we don't have subtreeId yet. hence in api/subtree/update/route.ts, we use
  // issueTreeId and selectedNodeId to identify the subtree to update instead of feeding
  // in subtreeId. IssueTree component is too complicated :( Don't want to mess with it
  setNodesAndEdges: (
    issueTreeId,
    selectedNodeId,
    nodes,
    edges,
    generation_output = "",
    saveToDB = false
  ) =>
    set((state) => {
      const updatedSubtrees = {
        ...state.subtrees,
        [selectedNodeId]: {
          ...state.subtrees[selectedNodeId],
          nodes,
          edges,
          generation_output,
        },
      };

      if (saveToDB) {
        saveToDBFunction(issueTreeId, selectedNodeId, nodes, edges);
      }

      return { subtrees: updatedSubtrees };
    }),

  removeSubtree: (issueTreeId, selectedNodeId, nodes, edges, status) =>
    set((state) => {
      const updatedSubtrees = Object.fromEntries(
        Object.entries(state.subtrees).filter(([key]) => key !== selectedNodeId)
      );

      saveToDBFunction(issueTreeId, selectedNodeId, nodes, edges, status);

      return { subtrees: updatedSubtrees };
    }),

  resetAllSubtrees: () => set(() => ({ subtrees: {} })),

  onOpen: (selectedNodeId: string) =>
    set(() => ({
      selectedSubtree: { selectedNodeId, isOpen: true },
    })),

  onClose: () =>
    set(() => ({
      selectedSubtree: { selectedNodeId: "initial", isOpen: false },
    })),
}));

export default useSubtreeStore;

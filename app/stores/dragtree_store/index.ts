// Export the main store
export { useDragTreeStore } from "./store";

// Export utility types for consumers
export type { DatabaseDragTree } from "./utils/database-utils";

// Export utility functions for potential external use
export {
  reconstructTreeFromDatabase,
  convertTreeToDbStructure,
} from "./utils/database-utils";

export {
  calculateNodeLevel,
  findNodeById,
  buildNodeMap,
  collectInterestedNodes,
  validateTreeConstraints,
} from "./utils/tree-utils";

export { createNodeMetadata, createNewTreeNode } from "./utils/node-utils";

export { debouncedDbSync } from "./utils/sync-utils";

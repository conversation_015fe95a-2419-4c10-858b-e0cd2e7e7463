// Utility to transform Prisma DB nodes' content_items into nodeContent Map
import { DragTreeNodeContentStatus } from '@prisma/client'

// Local copy of NodeContentItem to avoid circular dependency
type NodeContentItem = {
  contentId: string
  contentType: 'QUICK_RESEARCH' | 'ANALYSIS' | 'SUMMARY'
  contentVersion: string
  status: DragTreeNodeContentStatus
  contentText: string
  metadata: Record<string, any>
  messages?: any[]
}

export function buildNodeContentMapFromDbNodes(
  dbNodes: any[]
): Map<string, Map<string, NodeContentItem>> {
  const newNodeContent = new Map<string, Map<string, NodeContentItem>>()

  dbNodes.forEach(node => {
    if (node.content_items && node.content_items.length > 0) {
      const nodeContentMap = new Map<string, NodeContentItem>()

      node.content_items.forEach((contentItem: any) => {
        if (contentItem.status !== DragTreeNodeContentStatus.INACTIVE) {
          nodeContentMap.set(contentItem.id, {
            contentId: contentItem.id,
            contentType: contentItem.content_type || 'QUICK_RESEARCH',
            contentVersion: contentItem.content_version || 'v1',
            status: contentItem.status,
            contentText: contentItem.content_text || '',
            metadata: contentItem.content_metadata || {},
            messages: contentItem.messages || [],
          })
        }
      })

      if (nodeContentMap.size > 0) {
        newNodeContent.set(node.id, nodeContentMap)
      }
    }
  })

  return newNodeContent
}

import { create } from 'zustand'

type NavigationStore = {
  // React Flow navigation
  targetNodeId: string | null
  setTargetNodeId: (nodeId: string | null) => void
  navigateToNode: (nodeId: string) => void

  // Tree View navigation (reverse direction)
  treeTargetNodeId: string | null
  setTreeTargetNodeId: (nodeId: string | null) => void
  navigateToTreeNode: (nodeId: string) => void

  // Flag to prevent React Flow auto-focus when navigation comes from React Flow
  preventReactFlowFocus: boolean
  navigateToTreeNodeFromReactFlow: (nodeId: string) => void
}

export const useNavigationStore = create<NavigationStore>(set => ({
  targetNodeId: null,
  treeTargetNodeId: null,
  preventReactFlowFocus: false,

  setTargetNodeId: nodeId => set({ targetNodeId: nodeId }),
  setTreeTargetNodeId: nodeId => set({ treeTargetNodeId: nodeId }),

  navigateToNode: nodeId => {
    set({ targetNodeId: nodeId })
    // Clear the target after a longer delay to allow users to see the highlight
    setTimeout(() => set({ targetNodeId: null }), 2000)
  },

  navigateToTreeNode: nodeId => {
    set({ treeTargetNodeId: nodeId })
    // Clear the target after a longer delay to allow users to see the highlight
    setTimeout(() => set({ treeTargetNodeId: null }), 3000)
  },

  navigateToTreeNodeFromReactFlow: nodeId => {
    set({
      treeTargetNodeId: nodeId,
      preventReactFlowFocus: true,
    })
    // Clear the target and reset the flag after highlighting
    setTimeout(
      () =>
        set({
          treeTargetNodeId: null,
          preventReactFlowFocus: false,
        }),
      3000
    )
  },
}))

import { create } from 'zustand'
import {
  chatgptSiteUrl,
  claudeSiteUrl,
  copilotSiteUrl,
  geminiSiteUrl,
} from '@/app/configs'

type ServiceProvider = {
  name: string
  url: string
  isDisplayed: boolean
}

export type PanelStoreType = {
  isOpen: boolean
  onOpen: () => void
  onClose: () => void
  isOpenNewWindow: boolean
  switchIsOpenNewWindow: () => void
  serviceProviders: ServiceProvider[]
  toggleServiceProviderDisplay: (providerName: string) => void
  workingContext: string
  setWorkingContext: (context: string) => void
  showFeedback: boolean
  setShowFeedback: (showFeedback: boolean) => void
}

const initialServiceProviders: ServiceProvider[] = [
  { name: 'ChatGPT', url: chatgptSiteUrl, isDisplayed: true },
  { name: 'Claude', url: claudeSiteUrl, isDisplayed: true },
  { name: '<PERSON> Copilot', url: copilotSiteUrl, isDisplayed: false },
  { name: 'Google Gemini', url: geminiSiteUrl, isDisplayed: true },
]

export const usePanelStore = create<PanelStoreType>(set => ({
  isOpen: false,
  onOpen: () => set({ isOpen: true }),
  onClose: () => set({ isOpen: false }),
  isOpenNewWindow: true,
  switchIsOpenNewWindow: () =>
    set(state => ({ isOpenNewWindow: !state.isOpenNewWindow })),
  serviceProviders: initialServiceProviders,
  toggleServiceProviderDisplay: (providerName: string) => {
    set(state => ({
      serviceProviders: state.serviceProviders.map(provider =>
        provider.name === providerName
          ? { ...provider, isDisplayed: !provider.isDisplayed }
          : provider
      ),
    }))
  },
  workingContext: '',
  setWorkingContext: (context: string) => set({ workingContext: context }),
  showFeedback: false,
  setShowFeedback: (showFeedback: boolean) => set({ showFeedback }),
}))

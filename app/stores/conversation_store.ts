import { create } from "zustand";
import { ConversationStatus } from "@prisma/client";
import { ConversationLayoutType } from "@/app/types";

type ConversationStateType = {
  getState: () => ConversationStateType;
  conversationList: ConversationLayoutType[];
  getConversationStatus: (conversationId: string) => ConversationStatus;
  setConversationList: (conversationList: ConversationLayoutType[]) => void;
  addConversation: (conversation: ConversationLayoutType) => void;
  updateConversation: (conversation: ConversationLayoutType) => void;
  setConversationStatus: (
    conversationId: string,
    status: ConversationStatus
  ) => void;
};

export const useConversationStore = create<ConversationStateType>()(
  (set, get) => ({
    getState: () => get(),
    conversationList: [],

    getConversationStatus: (conversationId: string): ConversationStatus => {
      const conversation = get().conversationList.find(
        (c) => c.id === conversationId
      );
      return conversation?.conversation_status ?? ConversationStatus.INACTIVE;
    },

    setConversationList: (conversationList: ConversationLayoutType[]): void =>
      set((state) => ({ ...state, conversationList })),

    addConversation: (conversation: ConversationLayoutType): void =>
      set((state) => ({
        ...state,
        conversationList: [...state.conversationList, conversation],
      })),

    updateConversation: (updatedConversation: ConversationLayoutType): void =>
      set((state) => ({
        ...state,
        conversationList: state.conversationList.map((conversation) =>
          conversation.id === updatedConversation.id
            ? updatedConversation
            : conversation
        ),
      })),

    setConversationStatus: (
      conversationId: string,
      status: ConversationStatus
    ): void =>
      set((state) => ({
        ...state,
        conversationList: state.conversationList.map((conversation) =>
          conversation.id === conversationId
            ? {
                ...conversation,
                conversation_status: status,
                updated_at: new Date(),
              }
            : conversation
        ),
      })),
  })
);

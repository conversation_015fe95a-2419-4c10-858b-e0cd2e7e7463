import { create } from "zustand";
import { Node, Edge } from "@/app/types";
import { fetchedSearchesType, fetchedRagsType } from "@/app/server-actions";
import axios from "axios";
import debounce from "lodash/debounce";
import DOMPurify from "dompurify";

// Enums
export enum issueTreeStoreStatus {
  Loading = "LOADING", // when things are not ready
  Ready = "READY", // Ready to edit, the state should sync with DB
  Saving = "SAVING", // when there are changes but not saved to DB
  Completed = "COMPLETED", // when the issue tree is completed, usually user can only see this after draft creation
}

export enum issueTreeMode {
  Tree = "TREE",
  List = "LIST",
}

// Types
type SelectedNode = {
  selectedNodeId: string;
  isOpen: boolean;
};

type LeafNodeData = Partial<{
  resolved: boolean;
  example: string;
  skipped: boolean;
}>;

export type IssueTreeStoreType = {
  status: issueTreeStoreStatus;
  mode: issueTreeMode;
  summaryText: string;
  issueTreeId: string;
  nodes: Node[];
  edges: Edge[];
  unresolvedNodesCount: number;
  skippedNodesCount: number;
  leafNodeCount: number;
  treeMarkdownText: string;
  originalAskText: string;
  selectedNode: SelectedNode | null;
  showAlertDialog: boolean;
  searches: fetchedSearchesType[];
  rags: fetchedRagsType[];

  // Methods
  setId: (issueTreeId: string) => void;
  setStatus: (status: issueTreeStoreStatus) => void;
  setTreeMode: () => void;
  setListMode: () => void;
  setSummaryText: (summaryText: string) => void;
  setOriginalAskText: (originalAskText: string) => void;
  setNodes: (nodes: Node[], status: issueTreeStoreStatus) => void;
  setEdges: (edges: Edge[]) => void;
  setNodesAndEdges: (nodes: Node[], edges: Edge[]) => void;
  setLeafNodeData: (nodeId: string, newLeafData: LeafNodeData) => void;
  setTreeMarkdownText: () => void;
  setSelectedNode: (selectedNodeId: string) => void;
  closeSelectedNode: () => void;
  setShowAlertDialog: (showAlertDialog: boolean) => void;
  setSearchesAndRags: (
    searches: fetchedSearchesType[],
    rags: fetchedRagsType[]
  ) => void;
  reset: () => void;
};

// Helper functions
const calculateCounts = (nodes: Node[]) => {
  const leafNodeCount = nodes.filter(
    (node) => node.type === "customLeafNode"
  ).length;
  const unresolvedNodesCount = nodes.filter(
    (node) => node.type === "customLeafNode" && node.data && !node.data.resolved
  ).length;
  const skippedNodesCount = nodes.filter(
    (node) => node.type === "customLeafNode" && node.data && node.data.skipped
  ).length;

  console.log(
    "IssueTreeStore calculateCounts: leafNodeCount:",
    leafNodeCount,
    "|| unresolvedNodesCount:",
    unresolvedNodesCount,
    "|| skippedNodesCount:",
    skippedNodesCount
  );
  return { leafNodeCount, unresolvedNodesCount, skippedNodesCount };
};

const constructMarkdown = (nodes: Node[], edges: Edge[]): string => {
  const nodeMap: { [id: string]: Node } = Object.fromEntries(
    nodes.map((node) => [node.id, node])
  );
  const edgeMap: { [source: string]: string[] } = edges.reduce((acc, edge) => {
    acc[edge.source] = [...(acc[edge.source] || []), edge.target];
    return acc;
  }, {} as { [source: string]: string[] });

  const dfs = (nodeId: string, depth: number): string => {
    const node = nodeMap[nodeId];
    if (!node || node.data.skipped) return "";

    let text = `${"#".repeat(depth + 1)} ${node.data.label}\n`;
    if (node.data.example) {
      text += `- ${node.data.example}\n`;
    }

    return (
      text +
      (edgeMap[nodeId] || []).map((childId) => dfs(childId, depth + 1)).join("")
    );
  };

  return dfs("L1:1", 0);
};

const saveToDB = (
  id: string,
  nodes: Node[],
  edges: Edge[],
  summaryText: string | null,
  callback: () => void,
  delay: number = 3000
) => {
  console.log("Saving to DB:", id, nodes, edges, "with delay", delay);

  const payload: any = {
    issueTreeId: id,
    nodes: DOMPurify.sanitize(JSON.stringify(nodes)),
    edges: DOMPurify.sanitize(JSON.stringify(edges)),
  };

  if (summaryText !== null) {
    payload.summary_context = summaryText;
  }

  axios
    .post("/api/issuetree/update", payload)
    .then(callback)
    .catch(console.error);
};

const DEBOUNCE_DELAY_SAVE_DB = 3000; // 3 seconds
const debouncedSaveToDB = debounce(
  (
    id: string,
    nodes: Node[],
    edges: Edge[],
    summaryText: string | null,
    callback: () => void
  ) => {
    console.log(
      "Saving to DB:",
      id,
      nodes.length,
      edges.length,
      "with delay",
      DEBOUNCE_DELAY_SAVE_DB
    );
    const payload: any = {
      issueTreeId: id,
      nodes: DOMPurify.sanitize(JSON.stringify(nodes)),
      edges: DOMPurify.sanitize(JSON.stringify(edges)),
    };

    if (summaryText !== null) {
      payload.summary_context = summaryText;
    }

    axios
      .post(`/api/issuetree/update`, payload)
      .then(callback)
      .catch(console.error);
  },
  DEBOUNCE_DELAY_SAVE_DB
);

// Main store
const useIssueTreeStore = create<IssueTreeStoreType>((set, get) => ({
  // Initial state
  status: issueTreeStoreStatus.Loading,
  mode: issueTreeMode.Tree,
  issueTreeId: "initial",
  summaryText: "",
  nodes: [],
  edges: [],
  unresolvedNodesCount: -1,
  skippedNodesCount: -1,
  leafNodeCount: -1,
  treeMarkdownText: "",
  originalAskText: "",
  selectedNode: { selectedNodeId: "initial", isOpen: false },
  showAlertDialog: true,
  searches: [],
  rags: [],

  // Methods
  setId: (issueTreeId) => set({ issueTreeId }),
  setStatus: (status) => set({ status }),
  setTreeMode: () => set({ mode: issueTreeMode.Tree }),
  setListMode: () => set({ mode: issueTreeMode.List }),
  setSummaryText: (summaryText) => set({ summaryText }),
  setOriginalAskText: (originalAskText) => set({ originalAskText }),
  setNodes: (nodes, status) => {
    const counts = calculateCounts(nodes);
    set({ nodes, ...counts, status });
  },
  setEdges: (edges) => set({ edges }),
  setNodesAndEdges: (nodes, edges) => {
    const counts = calculateCounts(nodes);
    const markdownText =
      nodes.length > 0 && edges.length > 0
        ? constructMarkdown(nodes, edges)
        : get().treeMarkdownText;

    get().setStatus(issueTreeStoreStatus.Saving);

    set({ nodes, edges, ...counts, treeMarkdownText: markdownText });

    const currentIssueTreeId = get().issueTreeId;
    if (currentIssueTreeId !== "initial") {
      const instantSaveToDB = debounce(saveToDB, 1);
      instantSaveToDB(currentIssueTreeId, nodes, edges, null, () => {
        get().setStatus(issueTreeStoreStatus.Ready);
      });
    }
  },
  setLeafNodeData: (nodeId, newLeafData) => {
    const currentIssueTreeId = get().issueTreeId;
    if (currentIssueTreeId === "initial") return;

    set((state) => {
      const updatedNodes = state.nodes.map((node) =>
        node.id === nodeId && node.type === "customLeafNode"
          ? { ...node, data: { ...node.data, ...newLeafData } }
          : node
      );

      const counts = calculateCounts(updatedNodes);

      return {
        ...state,
        nodes: updatedNodes,
        status: issueTreeStoreStatus.Saving,
        ...counts,
      };
    });

    debouncedSaveToDB(currentIssueTreeId, get().nodes, get().edges, null, () =>
      set({ status: issueTreeStoreStatus.Ready })
    );
  },
  setTreeMarkdownText: () => {
    const markdownText = constructMarkdown(get().nodes, get().edges);
    set({ treeMarkdownText: markdownText });
  },
  setSelectedNode: (selectedNodeId) =>
    set({ selectedNode: { selectedNodeId, isOpen: true } }),
  closeSelectedNode: () =>
    set({ selectedNode: { selectedNodeId: "initial", isOpen: false } }),
  setShowAlertDialog: (showAlertDialog) =>
    set({ showAlertDialog: showAlertDialog }),
  setSearchesAndRags: (searches, rags) => set({ searches, rags }),
  reset: () =>
    set({
      status: issueTreeStoreStatus.Loading,
      mode: issueTreeMode.Tree,
      issueTreeId: "initial",
      nodes: [],
      edges: [],
      unresolvedNodesCount: -1,
      skippedNodesCount: -1,
      leafNodeCount: 0,
      summaryText: "",
      treeMarkdownText: "",
      originalAskText: "",
      searches: [],
      rags: [],
    }),
}));

export default useIssueTreeStore;

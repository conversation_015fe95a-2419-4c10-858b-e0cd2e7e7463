import React from "react";
import SettingsPage from "@/app/components/settings/SettingsPage";
import getCurrentUser from "@/app/actions/getCurrentUser";
import Sidebar from "@/app/components/sidebar/Sidebar";
import { User } from "@prisma/client";

const Settings: React.FC = async () => {
  const currentUser: User | null = await getCurrentUser();

  if (!currentUser) {
    return <div>Cannot find Current User</div>;
  }

  return (
    <Sidebar>
      <div className="h-full">
        <SettingsPage currentUser={currentUser} />
      </div>
    </Sidebar>
  );
};

export default Settings;

'use client'

import React, { useState, useRef } from 'react'
import TiptapQuickResearchEditor from '@/app/components/editor/TiptapQuickResearchEditor'
import { Editor } from '@tiptap/core'
import {
  createDemoSuggestions,
  acceptAllSuggestions,
  rejectAllSuggestions,
  getAllSuggestions,
  createSuggestionsFromText,
  type SuggestionBatch,
} from '@/app/components/editor/utils/suggestion-utils'

export default function TableDemo(): React.JSX.Element {
  const editorRef = useRef<Editor | null>(null)
  const [suggestionCount, setSuggestionCount] = useState<number>(0)

  // Better initial content that demonstrates both tables/mermaid and suggestions
  const [content, setContent] = useState<string>(
    `# Enhanced TipTap Editor Demo - Now with Git Diff-like Suggestions!

Welcome to the enhanced TipTap editor with table, mermaid diagram, and **suggestion** support! This editor can handle both tables and diagrams from LLM output, plus show suggested changes like a git diff.

## 🆕 New Suggestion Feature:
- **Git diff-like suggestions** - see old text (red, strikethrough) and new text (green)
- **Accept/reject buttons** - click ✓ to accept, ✗ to reject individual suggestions
- **Markdown export** - copies the original text when exporting (not the suggestions)
- **Bulk operations** - accept or reject all suggestions at once

## How to use suggestions:
1. Click **"Create Demo Suggestions"** to see the feature in action
2. Individual suggestions show: ~~old text~~ → new text with ✓/✗ buttons
3. Use **"Accept All"** or **"Reject All"** for bulk operations
4. Copy the content to see markdown export behavior

## Example text for suggestions:
This is a simple example of basic functionality. Hello World! The current implementation provides fundamental features.

## Testing multiple occurrences:
The word "test" appears multiple times in this test document. When you use the "Test Multiple Occurrences" button, it will create suggestions for every occurrence of "test" and "editor". This test shows how the editor handles multiple instances of the same text.

## How to use tables:
- Type **\/table** to insert a new 3×3 table
- Click inside any table cell to see table controls
- Blue arrows add rows/columns, red buttons delete
- Paste markdown tables and they'll be converted automatically

## How to use mermaid diagrams:
- Type **\/mermaid** to insert a new mermaid diagram
- Click the edit button to modify the diagram code
- Use Cmd+Enter to save, Esc to cancel
- Copy button exports as markdown \`\`\`mermaid code\`\`\`

## Example Table

| Feature | Status | Description |
|---------|--------|-------------|
| Basic Tables | ✅ | Create and edit tables |
| Markdown Support | ✅ | Parse markdown tables |
| Table Controls | ✅ | Add/remove rows and columns |
| Mermaid Diagrams | ✅ | Interactive diagram editor |
| **Suggestions** | ✅ | Git diff-like suggestions |
| **API Integration** | ✅ | Fetch suggestions from API |

## Example Mermaid Diagrams

Here are some example Mermaid diagrams to demonstrate the editor capabilities:

### 1. Simple Flowchart
\`\`\`mermaid
graph TD
    A[📋 Start Project] --> B{Budget Available?}
    B -->|Yes| C[🎯 Plan Development]
    B -->|No| D[💰 Secure Funding]
    D --> C
    C --> E[🚀 Execute]
    E --> F[✅ Complete]
\`\`\`

### 2. Sequence Diagram

\`\`\`mermaid
sequenceDiagram
    participant U as User
    participant F as Frontend
    participant A as API
    participant D as Database
    U->>F: Login Request
    F->>A: Authenticate User
    A->>D: Query User Data
    D-->>A: Return User Info
    A-->>F: JWT Token
    F-->>U: Login Success

    Note over U,D: User is now authenticated
\`\`\`

## API Integration Example:

The editor supports API integration for suggestions. You can call an API endpoint and apply the returned suggestions. Here's how the API flow works:

1. **Send content** to your API endpoint
2. **API processes** the content and returns suggested changes
3. **Apply suggestions** using the utility functions
4. **User reviews** and accepts/rejects suggestions

## Try it yourself:

Type **/table** or **/mermaid** below to test the features, or use the suggestion buttons above! Test multiple occurrences by clicking the appropriate button to see how the editor handles repeated text.`
  )

  const handleEditorReady = (editor: Editor) => {
    editorRef.current = editor
    updateSuggestionCount()
  }

  const updateSuggestionCount = () => {
    if (editorRef.current) {
      const suggestions = getAllSuggestions(editorRef.current)
      setSuggestionCount(suggestions.length)
    }
  }

  const handleCreateDemoSuggestions = () => {
    if (editorRef.current) {
      createDemoSuggestions(editorRef.current)
      setTimeout(updateSuggestionCount, 100) // Small delay to ensure DOM update
    }
  }

  const handleCreateCustomSuggestions = () => {
    if (editorRef.current) {
      const customSuggestions: SuggestionBatch = [
        {
          oldText: 'simple example',
          newText: 'comprehensive example with advanced features',
        },
        {
          oldText: 'basic functionality',
          newText: 'advanced functionality with AI-powered features',
        },
        {
          oldText: 'Hello World',
          newText: 'Hello Universe',
        },
        {
          oldText: 'fundamental features',
          newText: 'cutting-edge features',
        },
      ]

      createSuggestionsFromText(editorRef.current, customSuggestions)
      setTimeout(updateSuggestionCount, 100)
    }
  }

  const handleTestMultipleOccurrences = () => {
    if (editorRef.current) {
      // This will test multiple occurrences of the same text
      const multipleOccurrencesSuggestions: SuggestionBatch = [
        {
          oldText: 'test',
          newText: 'TEST',
        },
        {
          oldText: 'editor',
          newText: 'EDITOR',
        },
      ]

      createSuggestionsFromText(
        editorRef.current,
        multipleOccurrencesSuggestions
      )
      setTimeout(updateSuggestionCount, 100)
    }
  }

  const handleSimulateAPICall = async () => {
    if (editorRef.current) {
      // Simulate API call with loading state
      const loadingToast = document.createElement('div')
      loadingToast.textContent = '🔄 Fetching suggestions from API...'
      loadingToast.className =
        'fixed top-4 right-4 bg-blue-500 text-white px-4 py-2 rounded-lg shadow-lg z-50'
      document.body.appendChild(loadingToast)

      try {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 1500))

        // Simulate API response
        const apiSuggestions: SuggestionBatch = [
          {
            oldText: 'TipTap',
            newText: 'TipTap Rich Text Editor',
          },
          {
            oldText: 'features',
            newText: 'powerful features',
          },
          {
            oldText: 'editor',
            newText: 'advanced editor',
          },
        ]

        createSuggestionsFromText(editorRef.current!, apiSuggestions)
        setTimeout(updateSuggestionCount, 100)

        // Success message
        loadingToast.textContent = '✅ API suggestions applied!'
        loadingToast.className =
          'fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg z-50'
        setTimeout(() => document.body.removeChild(loadingToast), 2000)
      } catch (error) {
        // Error message
        loadingToast.textContent = '❌ Failed to fetch suggestions'
        loadingToast.className =
          'fixed top-4 right-4 bg-red-500 text-white px-4 py-2 rounded-lg shadow-lg z-50'
        setTimeout(() => document.body.removeChild(loadingToast), 2000)
      }
    }
  }

  const handleAcceptAll = () => {
    if (editorRef.current) {
      acceptAllSuggestions(editorRef.current)
      setTimeout(updateSuggestionCount, 100)
    }
  }

  const handleRejectAll = () => {
    if (editorRef.current) {
      rejectAllSuggestions(editorRef.current)
      setTimeout(updateSuggestionCount, 100)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="mx-auto max-w-4xl px-4">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">
          Enhanced TipTap Editor - Tables, Mermaid & Git Diff Suggestions
        </h1>

        {/* Suggestion Controls */}
        <div className="bg-white rounded-lg shadow-sm border p-6 mb-6">
          <h2 className="text-xl font-semibold text-gray-800 mb-4">
            🆕 Suggestion Controls
          </h2>

          {/* Create Suggestions Group */}
          <div className="mb-6">
            <h3 className="text-lg font-medium text-gray-700 mb-3">
              Create Suggestions
            </h3>
            <div className="flex flex-wrap gap-3">
              <button
                onClick={handleCreateDemoSuggestions}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Create Demo Suggestions
              </button>
              <button
                onClick={handleCreateCustomSuggestions}
                className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
              >
                Create Custom Suggestions
              </button>
              <button
                onClick={handleTestMultipleOccurrences}
                className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
              >
                Test Multiple Occurrences
              </button>
              <button
                onClick={handleSimulateAPICall}
                className="px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700 transition-colors"
              >
                Simulate API Call
              </button>
            </div>
          </div>

          {/* Bulk Actions Group */}
          <div className="mb-4">
            <h3 className="text-lg font-medium text-gray-700 mb-3">
              Bulk Actions
            </h3>
            <div className="flex flex-wrap gap-3">
              <button
                onClick={handleAcceptAll}
                className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed"
                disabled={suggestionCount === 0}
              >
                Accept All ({suggestionCount})
              </button>
              <button
                onClick={handleRejectAll}
                className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed"
                disabled={suggestionCount === 0}
              >
                Reject All ({suggestionCount})
              </button>
            </div>
          </div>

          <p className="text-sm text-gray-600">
            Current suggestions:{' '}
            <span className="font-semibold">{suggestionCount}</span>
            {suggestionCount > 0 &&
              ' (check browser console for operation logs)'}
          </p>
        </div>

        <div className="bg-white rounded-lg shadow-sm border p-6 mb-8">
          <h2 className="text-xl font-semibold text-gray-800 mb-4">
            TipTap Editor with Table, Mermaid & Suggestion Support
          </h2>
          <p className="text-gray-600 mb-4">Enhanced features:</p>
          <ul className="list-disc list-inside text-gray-600 mb-6 space-y-2">
            <li>
              <strong>🆕 Suggestions:</strong> Git diff-like suggestions with
              accept/reject buttons
            </li>
            <li>
              Type{' '}
              <code className="bg-gray-100 px-2 py-1 rounded text-sm">
                /table
              </code>{' '}
              to insert a 3×3 table with headers
            </li>
            <li>
              Type{' '}
              <code className="bg-gray-100 px-2 py-1 rounded text-sm">
                /mermaid
              </code>{' '}
              to insert a mermaid diagram
            </li>
            <li>Click inside any table cell to see contextual controls</li>
            <li>
              <span className="text-blue-600 font-medium">Blue arrows</span> add
              rows/columns,{' '}
              <span className="text-red-600 font-medium">red buttons</span>{' '}
              delete
            </li>
            <li>Notion-style mermaid editor with clean UI</li>
            <li>
              Proper markdown export for diagrams (Ctrl+A copies correctly)
            </li>
            <li>Handles both tables and diagrams from LLM output seamlessly</li>
          </ul>

          <div className="border rounded-lg">
            <TiptapQuickResearchEditor
              content={content}
              onContentChange={setContent}
              onEditorReady={handleEditorReady}
              placeholder="Start typing here or use / commands..."
              className="min-h-[400px]"
              autoFocus={false}
            />
          </div>
        </div>

        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
          <h3 className="font-semibold text-blue-900 mb-2">
            💡 Suggestion Pro Tips:
          </h3>
          <ul className="text-blue-800 text-sm space-y-1">
            <li>
              • **Red text with strikethrough**: Original text that will be
              removed
            </li>
            <li>• **Green text**: New suggested text that will be added</li>
            <li>
              • **✓ Green button**: Accept the suggestion (replace old with new)
            </li>
            <li>
              • **✗ Red button**: Reject the suggestion (keep original text)
            </li>
            <li>
              • **Markdown export**: When you copy, it exports the original text
              (not suggestions)
            </li>
            <li>
              • **Bulk operations**: Use "Accept All" or "Reject All" for
              efficiency
            </li>
            <li>
              • **🆕 Multiple occurrences**: Now handles multiple instances of
              the same text
            </li>
            <li>
              • **🆕 API integration**: Supports fetching suggestions from
              external APIs
            </li>
          </ul>
        </div>

        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
          <h3 className="font-semibold text-yellow-900 mb-2">
            🔧 Recent Fixes & Improvements:
          </h3>
          <ul className="text-yellow-800 text-sm space-y-1">
            <li>
              • **Fixed "Accept All" bug**: Now properly processes all
              suggestions
            </li>
            <li>
              • **Multiple occurrences**: Handles multiple instances of the same
              text correctly
            </li>
            <li>
              • **Better positioning**: Fixed position conflicts when processing
              suggestions
            </li>
            <li>
              • **Console logging**: Added logging to track suggestion
              operations
            </li>
            <li>
              • **API integration**: Added example API integration function
            </li>
            <li>
              • **Improved error handling**: Better error messages and debugging
            </li>
          </ul>
        </div>

        <div className="bg-emerald-50 border border-emerald-200 rounded-lg p-4">
          <h3 className="font-semibold text-emerald-900 mb-2">
            🎯 Table & Mermaid Pro Tips:
          </h3>
          <ul className="text-emerald-800 text-sm space-y-1">
            <li>
              • Table controls only appear when your cursor is inside a table
            </li>
            <li>• Hover over buttons to see helpful tooltips</li>
            <li>• Blue arrows add content, red buttons delete content</li>
            <li>• Navigate between cells using Tab and Shift+Tab</li>
            <li>
              • Paste markdown tables directly - they'll be converted
              automatically
            </li>
            <li>• Mermaid controls appear on hover or when selected</li>
            <li>• Use Cmd+Enter to save diagram edits, Esc to cancel</li>
            <li>
              • Copy button exports as proper markdown with ```mermaid wrapper
            </li>
          </ul>
        </div>
      </div>
    </div>
  )
}

import { streamObject } from 'ai'
import { MockLanguageModelV1 } from 'ai/test'
import { NextResponse } from 'next/server'
import { screenSchema } from '@/app/api/rephrase/screen/utils'
import { getMockScreenObject } from '@/app/(conv)/screening/constants/mockData'
import { type SupportedLanguageCode } from '@/app/(conv)/screening/constants/languages'

export const maxDuration = 60

type ScreeningDiagnoseSimulatorRequestType = {
  description: string
  preferredLanguage?: SupportedLanguageCode
}

export async function POST(req: Request) {
  try {
    const requestData: ScreeningDiagnoseSimulatorRequestType = await req.json()
    const { description, preferredLanguage = 'en' } = requestData

    // Get mock data based on language
    const mockScreenObject = getMockScreenObject(preferredLanguage)

    // Create mock model for progressive object streaming
    const mockModel = new MockLanguageModelV1({
      defaultObjectGenerationMode: 'json',
      doStream: async () => {
        // Create progressive JSON text deltas that build the object step by step
        const chunks = [
          // Start with opening brace
          { type: 'text-delta' as const, textDelta: '{\n' },

          // Add is_problem_clear field
          { type: 'text-delta' as const, textDelta: '  "is_problem_clear": ' },
          {
            type: 'text-delta' as const,
            textDelta: `${mockScreenObject.is_problem_clear}`,
          },
          { type: 'text-delta' as const, textDelta: ',\n' },

          // Add problem_ambiguity field
          {
            type: 'text-delta' as const,
            textDelta: '  "problem_ambiguity": "',
          },
          {
            type: 'text-delta' as const,
            textDelta: mockScreenObject.problem_ambiguity?.slice(0, 20) || '',
          },
          {
            type: 'text-delta' as const,
            textDelta: mockScreenObject.problem_ambiguity?.slice(20, 40) || '',
          },
          {
            type: 'text-delta' as const,
            textDelta: mockScreenObject.problem_ambiguity?.slice(40) || '',
          },
          { type: 'text-delta' as const, textDelta: '",\n' },

          // Add intention array start
          { type: 'text-delta' as const, textDelta: '  "intention": [\n' },
          {
            type: 'text-delta' as const,
            textDelta: `    "${mockScreenObject.intention?.[0] || ''}"`,
          },
          { type: 'text-delta' as const, textDelta: ',\n' },
          {
            type: 'text-delta' as const,
            textDelta: `    "${mockScreenObject.intention?.[1] || ''}"`,
          },
          { type: 'text-delta' as const, textDelta: ',\n' },
          {
            type: 'text-delta' as const,
            textDelta: `    "${mockScreenObject.intention?.[2] || ''}"`,
          },
          { type: 'text-delta' as const, textDelta: '\n  ],\n' },

          // Add entity array
          { type: 'text-delta' as const, textDelta: '  "entity": [\n' },
          {
            type: 'text-delta' as const,
            textDelta: `    "${mockScreenObject.entity?.[0] || ''}"`,
          },
          { type: 'text-delta' as const, textDelta: ',\n' },
          {
            type: 'text-delta' as const,
            textDelta: `    "${mockScreenObject.entity?.[1] || ''}"`,
          },
          { type: 'text-delta' as const, textDelta: ',\n' },
          {
            type: 'text-delta' as const,
            textDelta: `    "${mockScreenObject.entity?.[2] || ''}"`,
          },
          { type: 'text-delta' as const, textDelta: '\n  ],\n' },

          // Add pass field and close
          { type: 'text-delta' as const, textDelta: '  "pass": ' },
          {
            type: 'text-delta' as const,
            textDelta: `${mockScreenObject.pass}`,
          },
          { type: 'text-delta' as const, textDelta: '\n}' },

          // Finish chunk
          {
            type: 'finish' as const,
            finishReason: 'stop' as const,
            usage: { promptTokens: 150, completionTokens: 300 },
          },
        ]

        // Create the stream with delays
        const stream = new ReadableStream({
          start(controller) {
            let index = 0

            const processNext = () => {
              if (index < chunks.length) {
                const delay = index === 0 ? 800 : 180 // Initial delay then faster chunks
                setTimeout(() => {
                  controller.enqueue(chunks[index])
                  index++
                  processNext()
                }, delay)
              } else {
                controller.close()
              }
            }

            processNext()
          },
        })

        return {
          stream,
          rawCall: {
            rawPrompt: `Analyze: ${description}`,
            rawSettings: {},
          },
        }
      },
    })

    // Use streamObject with the mock model
    const result = await streamObject({
      model: mockModel,
      schema: screenSchema,
      system: `You are analyzing the clarity of a problem statement. Return a structured analysis with the following fields:
- is_problem_clear: boolean indicating if the problem is clearly defined
- problem_ambiguity: string describing any ambiguity issues
- intention: array of strings listing the user's intentions
- entity: array of strings listing key entities mentioned
- pass: boolean indicating if the problem passes clarity check`,
      prompt: `Analyze this problem statement for clarity: "${description}"`,
    })

    return result.toTextStreamResponse()
  } catch (error) {
    console.error('Screening diagnose simulator API error:', error)
    return NextResponse.json(
      { message: 'Error in diagnosis simulator' },
      { status: 500 }
    )
  }
}

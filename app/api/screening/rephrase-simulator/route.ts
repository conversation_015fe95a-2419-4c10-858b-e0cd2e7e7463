import { streamText } from 'ai'
import { MockLanguageModelV1 } from 'ai/test'
import { NextResponse } from 'next/server'
import { getMockRephraseDescriptions } from '@/app/(conv)/screening/constants/mockData'
import { type SupportedLanguageCode } from '@/app/(conv)/screening/constants/languages'

export const maxDuration = 60

type ScreeningRephraseSimulatorRequestType = {
  userId: string
  description: string
  preferredLanguage?: SupportedLanguageCode
}

export async function POST(req: Request) {
  try {
    const requestData: ScreeningRephraseSimulatorRequestType = await req.json()
    const {
      userId: _userId,
      description,
      preferredLanguage = 'en',
    } = requestData // userId currently unused

    // Get mock rephrased descriptions based on language
    const mockRephrased = getMockRephraseDescriptions(preferredLanguage)

    // Create the complete response text (4 numbered suggestions)
    const completeResponse = mockRephrased
      .map((suggestion, index) => `${index + 1}. ${suggestion}`)
      .join('\n\n')

    // Split into words for progressive streaming
    const words = completeResponse.split(' ')

    // Create mock model that streams word by word
    const mockModel = new MockLanguageModelV1({
      doStream: async () => {
        // Create text deltas - each chunk contains just the next word/space
        const streamChunks: Array<{
          type: 'text-delta' | 'finish'
          textDelta?: string
          finishReason?: 'stop'
          usage?: { promptTokens: number; completionTokens: number }
        }> = []

        // First chunk is the first word
        streamChunks.push({
          type: 'text-delta' as const,
          textDelta: words[0],
        })

        // Subsequent chunks are space + word
        for (let i = 1; i < words.length; i++) {
          streamChunks.push({
            type: 'text-delta' as const,
            textDelta: ' ' + words[i],
          })
        }

        // Final finish chunk
        streamChunks.push({
          type: 'finish' as const,
          finishReason: 'stop' as const,
          usage: {
            promptTokens: 50,
            completionTokens: words.length,
          },
        })

        // Create readable stream from chunks
        const stream = new ReadableStream({
          start(controller) {
            let index = 0
            const pushChunk = () => {
              if (index < streamChunks.length) {
                // Add delay between chunks to simulate real-time generation
                setTimeout(
                  () => {
                    controller.enqueue(streamChunks[index])
                    index++
                    pushChunk()
                  },
                  index === 0 ? 500 : 120
                ) // Initial delay, then faster chunks
              } else {
                controller.close()
              }
            }
            pushChunk()
          },
        })

        return {
          stream,
          rawCall: { rawPrompt: description, rawSettings: {} },
        }
      },
    })

    // Use streamText with mock model - this will format the response correctly
    const result = await streamText({
      model: mockModel,
      messages: [
        {
          role: 'system',
          content: `Rephrase this description into 4 numbered problem statements: ${description}`,
        },
      ],
    })

    return result.toDataStreamResponse()
  } catch (error) {
    console.error('Screening rephrase simulator API error:', error)
    return NextResponse.json({ message: 'Error' }, { status: 500 })
  }
}

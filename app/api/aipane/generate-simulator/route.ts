import { streamText } from 'ai'
import { MockLanguageModelV1 } from 'ai/test'
import { NextResponse } from 'next/server'
import { AIPaneGenerateRequest } from '@/app/api/aipane/generate/types'

export const maxDuration = 60

// -----------------------------------------------------------------------------
// Streaming speed configuration (in milliseconds)
// Modify these to tune how fast the simulated generator streams.
// -----------------------------------------------------------------------------
const STREAM_INITIAL_DELAY_MS = 50 // delay before the first chunk (was 100)
const STREAM_CHUNK_DELAY_MS = 10 // delay between subsequent chunks (was 30)

export async function POST(req: Request) {
  try {
    // Defensive parsing like working simulators
    let requestData: any = null
    try {
      requestData = await req.json()
    } catch (error) {
      console.error('🧪 [AI Pane Simulator] Failed to parse JSON:', error)
      return NextResponse.json(
        { message: 'Invalid JSON in request body.' },
        { status: 400 }
      )
    }

    if (!requestData) {
      return NextResponse.json(
        { message: 'Request body is required.' },
        { status: 400 }
      )
    }

    const {
      prompt,
      model = 'gpt-4.1',
      settings = {},
    } = requestData as AIPaneGenerateRequest

    if (!prompt || typeof prompt !== 'string' || !prompt.trim()) {
      console.error('🧪 [AI Pane Simulator] Invalid prompt:', {
        prompt,
        type: typeof prompt,
      })
      return NextResponse.json(
        { message: 'Invalid request body: prompt is required.' },
        { status: 400 }
      )
    }

    // Use simulated markdown with table and mermaid for UI testing
    const completeResponse = getSimulatedMarkdownResponse(prompt, model)
    const words = completeResponse.split(' ')

    // Create mock model that streams word by word (same pattern as other simulators)
    const mockModel = new MockLanguageModelV1({
      doStream: async () => {
        // Create text deltas - each chunk contains just the next word/space
        const streamChunks: Array<{
          type: 'text-delta' | 'finish'
          textDelta?: string
          finishReason?: 'stop'
          usage?: { promptTokens: number; completionTokens: number }
        }> = []

        // First chunk is the first word
        streamChunks.push({
          type: 'text-delta' as const,
          textDelta: words[0],
        })

        // Subsequent chunks are space + word
        for (let i = 1; i < words.length; i++) {
          streamChunks.push({
            type: 'text-delta' as const,
            textDelta: ' ' + words[i],
          })
        }

        // Final finish chunk
        streamChunks.push({
          type: 'finish' as const,
          finishReason: 'stop' as const,
          usage: {
            promptTokens: 100,
            completionTokens: words.length,
          },
        })

        // Create readable stream from chunks
        const stream = new ReadableStream({
          start(controller) {
            let index = 0
            const pushChunk = () => {
              if (index < streamChunks.length) {
                setTimeout(
                  () => {
                    console.log(
                      '🧪 [AI Pane Simulator] Enqueuing chunk',
                      index,
                      ':',
                      streamChunks[index]
                    )
                    controller.enqueue(streamChunks[index])
                    index++
                    pushChunk()
                  },
                  index === 0 ? STREAM_INITIAL_DELAY_MS : STREAM_CHUNK_DELAY_MS
                )
              } else {
                controller.close()
              }
            }
            pushChunk()
          },
        })

        return {
          stream,
          rawCall: { rawPrompt: prompt, rawSettings: settings },
        }
      },
    })

    const result = await streamText({
      model: mockModel,
      messages: [
        {
          role: 'system',
          content: 'Generate comprehensive content based on the user prompt.',
        },
        {
          role: 'user',
          content: prompt,
        },
      ],
    })

    return result.toDataStreamResponse()
  } catch (error) {
    console.error('AI Pane generate simulator API error:', error)
    return NextResponse.json({ message: 'Error' }, { status: 500 })
  }
}

// Helper: Simulated markdown with table and mermaid for UI testing
function getSimulatedMarkdownResponse(prompt: string, model: string): string {
  return `
# Simulated AI Output

Here is a summary table:

| Feature      | Status   | Notes         |
|--------------|----------|---------------|
| Streaming    | ✅       | Works great   |
| Mermaid      | ✅       | See below     |
| Tables       | ✅       | Rendered above|

## Mermaid Diagram

\`\`\`mermaid
graph TD
    A[Start] --> B{Is it working?}
    B -- Yes --> C[Celebrate!]
    B -- No --> D[Debug]
\`\`\`

${prompt}

# ${model}
`
}

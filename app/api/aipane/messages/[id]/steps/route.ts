import { NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/app/api/auth/authOptions'
import prisma from '@/app/libs/prismadb'
import { isRateLimited, getRetryAfterSeconds } from '@/app/libs/rateLimiter'

export const maxDuration = 30

/**
 * GET /api/aipane/messages/[id]/steps
 *
 * Retrieves execution steps for a specific message.
 * Used for lazy-loading steps when user clicks "Show Reasoning" toggle.
 *
 * Query parameters:
 * - includeMetadata: Whether to include full metadata (default: true)
 */
export async function GET(
  req: Request,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: { message: 'Unauthorized', code: 'AUTH_REQUIRED' } },
        { status: 401 }
      )
    }

    // Rate limiting - 50 requests per minute per user for message steps
    const rateLimitKey = `${session.user.id}:aipane:steps`
    if (isRateLimited(rateLimitKey, 60000)) {
      // 1 minute window
      const retryAfter = getRetryAfterSeconds(rateLimitKey, 60000)
      return NextResponse.json(
        { error: { message: 'Too many requests', code: 'RATE_LIMITED' } },
        {
          status: 429,
          headers: {
            'Retry-After': retryAfter.toString(),
            'X-RateLimit-Limit': '50',
            'X-RateLimit-Remaining': '0',
          },
        }
      )
    }

    const params = await context.params
    const messageId = params.id
    const url = new URL(req.url)
    const includeMetadata = url.searchParams.get('includeMetadata') !== 'false'

    console.log(
      `📋 [Message Steps API] Fetching steps for message: ${messageId}`
    )

    if (!messageId || !messageId.startsWith('msg_')) {
      return NextResponse.json(
        { message: 'Invalid message ID format' },
        { status: 400 }
      )
    }

    // First, verify the message exists and user has access
    const message = await prisma.aiMessage.findUnique({
      where: { id: messageId },
      include: {
        conversation: {
          select: { userId: true },
        },
        steps: {
          orderBy: { stepOrder: 'asc' },
        },
      },
    })

    if (!message) {
      return NextResponse.json(
        { message: 'Message not found' },
        { status: 404 }
      )
    }

    // Check if user has access to this message's conversation
    if (message.conversation.userId !== session.user.id) {
      return NextResponse.json({ message: 'Access denied' }, { status: 403 })
    }

    // Transform steps for response
    const transformedSteps = message.steps.map(step => ({
      id: step.id,
      stepOrder: step.stepOrder,
      type: step.type,
      ...(includeMetadata ? { metadata: step.metadata } : {}),
      parallelKey: step.parallelKey,
      parentStepId: step.parentStepId,
      createdAt: step.createdAt,
      updatedAt: step.updatedAt,
    }))

    const response = {
      messageId: message.id,
      steps: transformedSteps,
      summary: {
        totalSteps: transformedSteps.length,
        stepsByType: transformedSteps.reduce(
          (acc, step) => {
            acc[step.type] = (acc[step.type] || 0) + 1
            return acc
          },
          {} as Record<string, number>
        ),
        hasParallelExecution: transformedSteps.some(step => step.parallelKey),
        hasNestedSteps: transformedSteps.some(step => step.parentStepId),
      },
    }

    console.log(
      `✅ [Message Steps API] Retrieved ${transformedSteps.length} steps for message: ${messageId}`
    )

    return NextResponse.json(response)
  } catch (error) {
    console.error('Message Steps API error:', error)
    return NextResponse.json(
      { message: 'Error retrieving message steps' },
      { status: 500 }
    )
  }
}

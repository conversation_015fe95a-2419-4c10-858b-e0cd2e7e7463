// Types for AI Pane Chat API

/**
 * Standard chat message format compatible with Vercel AI SDK
 */

// The modern Vercel AI SDK sends the message `content` as an **array** of
// parts to enable multimodal messages. Each part has a `type` (currently
// always `text`) and the actual `text` string. To remain backward compatible
// we represent `content` as a union so existing string-based code keeps
// compiling while allowing the array format from the client.

export type MessageContent =
  | string
  | {
      type: 'text'
      text: string
    }[]

export type ChatMessage = {
  role: 'user' | 'assistant' | 'system'
  content: MessageContent
  // Optional extensions for future reasoning/tool support
  thinking?: string
  toolCalls?: ToolCall[]
}

/**
 * Tool call representation for future tool integration
 */
export type ToolCall = {
  id: string
  type: 'function' | 'search' | 'calculation'
  name: string
  parameters: Record<string, any>
  result?: any
}

/**
 * Request type for AI Pane Chat API
 * Compatible with both useChat hook and direct API calls
 */
export type AIPaneChatRequest = {
  messages: {
    role: 'user' | 'assistant' | 'system'
    content: MessageContent
  }[]
  model?: string
  context?: string
  settings?: Record<string, any>
  conversationId?: string
  // additional polymorphic context fields
  contextEntityType?: string
  contextEntityId?: string
}

/**
 * Enhanced message type for UI display
 * Extends basic ChatMessage with UI-specific properties
 */
export type UIChatMessage = ChatMessage & {
  id: string
  timestamp: Date
  isStreaming?: boolean
  thinkingTime?: number // For "Thought for X seconds" display
  isCollapsed?: boolean // For thinking section collapse state
}

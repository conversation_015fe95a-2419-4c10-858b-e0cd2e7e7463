'use server'

import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/app/api/auth/authOptions'
import {
  getConversationWithMessagesPaginated,
  updateConversationTitle,
} from '@/app/server-actions/ai-chat/persistence-service'

export async function PATCH(req: NextRequest, { params }: any) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 })
    }

    const conversationId = params.id
    if (!conversationId || !conversationId.startsWith('thread_')) {
      return NextResponse.json(
        { message: 'Invalid conversation ID' },
        { status: 400 }
      )
    }

    const { title } = await req.json()
    if (!title || typeof title !== 'string') {
      return NextResponse.json(
        { message: 'Title is required' },
        { status: 400 }
      )
    }

    // Verify ownership
    const convRes = await getConversationWithMessagesPaginated(conversationId, {
      limit: 1,
      includeSteps: false,
      includeAttachments: false,
    })
    if (!convRes.success) {
      return NextResponse.json(
        { message: convRes.error || 'Conversation not found' },
        { status: 404 }
      )
    }
    if (convRes.data!.conversation.userId !== session.user.id) {
      return NextResponse.json({ message: 'Forbidden' }, { status: 403 })
    }

    const updateRes = await updateConversationTitle(conversationId, title)
    if (!updateRes.success) {
      return NextResponse.json(
        { message: updateRes.error || 'Failed to update title' },
        { status: 500 }
      )
    }

    return NextResponse.json({ success: true })
  } catch (e) {
    console.error('Update conversation title error:', e)
    return NextResponse.json({ message: 'Server error' }, { status: 500 })
  }
}

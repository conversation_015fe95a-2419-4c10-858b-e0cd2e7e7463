import { AuthOptions, DefaultSession } from 'next-auth'
import Gith<PERSON><PERSON>rovider from 'next-auth/providers/github'
import GoogleProvider from 'next-auth/providers/google'
import CredentialsProvider from 'next-auth/providers/credentials'
import { PrismaAdapter } from '@auth/prisma-adapter'
import type { Adapter } from 'next-auth/adapters'

import prisma from '@/app/libs/prismadb'
import { UserStatus } from '@prisma/client'

// Extend the built-in session types
declare module 'next-auth' {
  interface Session extends DefaultSession {
    user: {
      id: string
      status: UserStatus
    } & DefaultSession['user']
  }
}

export const authOptions: AuthOptions = {
  adapter: PrismaAdapter(prisma) as Adapter,
  providers: [
    GithubProvider({
      clientId: process.env.GITHUB_ID as string,
      clientSecret: process.env.GITHUB_SECRET as string,
    }),
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID as string,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET as string,
    }),
  ],
  debug: process.env.NODE_ENV === 'development',
  session: {
    strategy: 'jwt',
    maxAge: 7 * 24 * 60 * 60, // 7 days in seconds
  },
  callbacks: {
    signIn: async ({ user }) => {
      // Check if user exists and is active
      const dbUser = await prisma.user.findUnique({
        where: { email: user.email as string },
        select: { status: true },
      })

      // Allow sign-in for new users or active existing users
      return !dbUser || dbUser.status === UserStatus.ACTIVE
    },

    jwt: async ({ token, user }) => {
      if (user) {
        // Fetch user data from database
        const dbUser = await prisma.user.findUnique({
          where: { email: user.email as string },
          select: { id: true, status: true },
        })

        // Add custom claims to the token
        if (dbUser) {
          token.id = dbUser.id
          token.status = dbUser.status
        }
      }
      return token
    },

    session: ({ session, token }) => {
      // Add custom session data
      if (session.user) {
        session.user.id = token.id as string
        session.user.status = token.status as UserStatus
      }
      return session
    },
  },
  secret: process.env.NEXTAUTH_SECRET,
}

// Add credentials provider only for non-production environments
if (process.env.NODE_ENV !== 'production') {
  authOptions.providers.push(
    CredentialsProvider({
      name: 'E2E Test Credentials',
      credentials: {
        email: {
          label: 'Email',
          type: 'text',
          placeholder: '<EMAIL>',
        },
      },
      async authorize(credentials) {
        if (!credentials?.email) {
          throw new Error('Email is required for E2E login.')
        }

        const user = await prisma.user.findUnique({
          where: { email: credentials.email },
        })

        if (user) {
          // Return user object to create a session
          return user
        } else {
          // If you want to auto-create a user, you can do it here
          return null
        }
      },
    })
  )
}

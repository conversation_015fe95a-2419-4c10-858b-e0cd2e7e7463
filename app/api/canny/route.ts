import { NextResponse } from "next/server";
import getCurrentUser from "@/app/actions/getCurrentUser";
import { User, UserStatus } from "@prisma/client";

var PrivateKey = process.env.CANNY_PRIVATE_KEY;

var jwt = require("jsonwebtoken");

// Create a Canny token such that the user can leave a comment on Canny
function createCannyToken(user: User) {
  var userData = {
    avatarURL: user.image,
    email: user.email,
    id: user.id,
    name: user.name,
  };
  return jwt.sign(userData, PrivateKey, { algorithm: "HS256" });
}

export async function GET() {
  try {
    const currentUser = await getCurrentUser();

    // Check if the user is authorized
    if (!currentUser?.id || !currentUser?.email) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    // Block them from accessing API if they are not active
    if (currentUser?.status !== UserStatus.ACTIVE) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const token = createCannyToken(currentUser);
    // console.log(token, "token");

    // Return the newly created message as a JSON response
    return NextResponse.json({ ssoToken: token });
  } catch (error) {
    console.error(error);

    // If an error occurs, return a 500 status code with an "Error" message
    return new NextResponse("Error", { status: 500 });
  }
}

import { z } from 'zod'

// define a schema for the screen object
export const screenSchema = z.object({
  is_problem_clear: z
    .boolean()
    .describe(
      "Indicate whether the problem statement is clear or if there are significant ambiguities that prevent understanding, especially if the user's main goal or intention is not explicitly stated. Respond with true if the problem statement is clear, or false otherwise"
    ),
  problem_ambiguity: z
    .string()
    .describe(
      "Indicate whether the problem statement is clear or if there are significant ambiguities that prevent understanding, especially if the user's main goal or intention is not explicitly stated. Respond with a brief explanation of the ambiguity if it exists, or 'No obvious ambiguities' if the problem statement is clear. Don't use second person [the user], use first person [you]"
    ),
  intention: z
    .array(z.string())
    .describe(
      "Examine carefully whether the user's main goal or intention is explicitly stated. If not, you MUST point it out. Identify and state the user's main goals or objectives"
    ),
  entity: z
    .array(z.string())
    .describe(
      "List the key persons, events, locations, subjects, objects, numbers, conditions, or entities involved in the problem statement. For any unusual terms or names, include a brief interpretation (e.g., <PERSON><PERSON>'s platform [a hotel revenue management software], 2009 [year])"
    ),
  score: z
    .number()
    .describe(
      'Assign a score from **1 to 5** (1 being poor, 5 being excellent) indicating the overall clarity and sufficiency of the problem statement'
    ),
  pass: z
    .boolean()
    .describe(
      'Based on the overall quality score, determine whether the input is sufficient to proceed. **Pass** for scores **4 or 5**; **No Pass** for scores below 4'
    ),
})

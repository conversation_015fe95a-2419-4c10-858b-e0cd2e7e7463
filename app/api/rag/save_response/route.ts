import { NextResponse } from "next/server";
import prisma from "@/app/libs/prismadb";
import { ragSaveRequestType } from "@/app/types/api";

// This is for user to update the draft content
export async function POST(req: Request): Promise<NextResponse> {
  try {
    const requestData: ragSaveRequestType = await req.json();

    // Extract the issueTreeId and leave the rest of the fields in updateData
    const {
      issueTreeId,
      currentUser,
      conversationId,
      selectedNodeId,
      generationInput,
      generationOutput,
    } = requestData;

    // Create a new RAG response in the database
    const newRAGResponse = await prisma.rAGResponse.create({
      data: {
        creator_id: currentUser.id,
        conversation_id: conversationId,
        issue_tree_id: issueTreeId,
        selected_node_id: selectedNodeId,
        generation_input: generationInput,
        generation_output: generationOutput,
      },
    });

    return NextResponse.json(newRAGResponse);
  } catch (error) {
    console.error("Error saving RAG response:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}

import { NextResponse } from "next/server";
import OpenAI from "openai";
import {
  CustomBraveSearch,
  // exampleSearchResults,
  processSearchResults,
  // exampleResultMap,
} from "../utils";
import prisma from "@/app/libs/prismadb";
import { OpenAIUsageType } from "@prisma/client";
import { logAPIUsage } from "@/app/api/utils";
import { ragSearchRequestType } from "@/app/types/api";

const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });

export async function POST(request: Request) {
  try {
    // Parse the request body as JSON
    const requestData: ragSearchRequestType = await request.json();

    const { query, conversationId, currentUser, issueTreeId, selectedNodeId } =
      requestData;
    console.log("api/rag/search: ", query);

    // Rephrase the input prompt for search engine API
    // More detailed note:
    // This part adds some latency to the response, the rationale is if we just throw the prompt to the search engine, it usually returns irrelevant links
    // Prob. because it is not good at understanding the long prompts request

    // Example:
    // Google search this: [Risk Assessment] As a product manager, I want to assess potential challenges such as market competition, integration complexities, and regulatory requirements in the airline industry when developing and selling the revenue management system to mitigate risks and ensure successful adoption by clients. Please help to research this with data and facts to backup: How can the revenue management system align with customer preferences and expectations to drive revenue growth?
    // The first few return links under Incognito mode are [as of 2024 Jun]:
    // How to manage product risks? / A Guide to Performing Risk Assessments for Product / What are the best practices for managing product risk?
    // These are not relevant to the real intention of prompt: Airline Revenue Management System, Customer Preferences, Revenue Growth

    // So, we rephrase the prompt to make it more succint and to the point.
    // But prob. not a good time to spend ton of energy on this because it is basically reinventing what perplexity, MS copilot, other search engines are doing.
    // Ideally in the future, there are some streaming search API, then we can just call them

    const model_name = "gpt-3.5-turbo";
    const searchRephrasePrompt = `You are a rephraser and always respond with a rephrased version of the input that is given to a search engine API. Always be succint and use the same words as the input. ONLY RETURN THE REPHRASED VERSION OF THE INPUT ${query}`;
    const completion = await openai.chat.completions.create({
      model: model_name,
      messages: [
        {
          role: "system",
          content: searchRephrasePrompt,
        },
      ],
    });

    const rephrasedSearchQuery =
      completion.choices[0].message.content ||
      "Default: Investigating the challenges of integrating a new revenue management system with current airline IT infrastructure.";

    // Conduct Brave search
    const search_results = await CustomBraveSearch(rephrasedSearchQuery);

    const resultArray = processSearchResults(search_results);
    console.log("api/rag/search: ", resultArray);

    await Promise.all([
      // onCompletion: open ai usage & log the completion
      logAPIUsage({
        open_ai_usage_type: OpenAIUsageType.RAG_QUERY_REPHRASER,
        model_name: model_name,
        input_text: searchRephrasePrompt,
        output_text: rephrasedSearchQuery,
        currentUser,
        conversationId,
      }),
      // Save the completion to the database
      prisma.search.create({
        data: {
          creator_id: currentUser.id,
          conversation_id: conversationId,
          issue_tree_id: issueTreeId,
          selected_node_id: selectedNodeId,
          search_query: rephrasedSearchQuery,
          search_result: JSON.stringify(search_results),
        },
      }),
    ]);

    // Return the newly created message as a JSON response
    return NextResponse.json({
      query: query,
      rephrased_query: rephrasedSearchQuery,
      search_results: resultArray,
    });
  } catch (error) {
    console.error(error);

    // If an error occurs, return a 500 status code with an "Error" message
    return new NextResponse("Error", { status: 500 });
  }
}

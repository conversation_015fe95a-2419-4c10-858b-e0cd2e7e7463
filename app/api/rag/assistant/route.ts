import { NextResponse } from 'next/server'
import { generateText, streamText, tool, StreamData } from 'ai'
import { z } from 'zod'
import { azure } from '@ai-sdk/azure'
import { CustomBraveSearch, processSearchResults } from '../utils'

import { llmContextFromSearchResults } from '@/app/users/components/utils'
import { numberOfLinksToUse } from '@/app/configs'
import { ragAssistantRequestType } from '@/app/types/api'
import { RAGDBOperations } from '../utils'

const baseUrl = process.env.NEXT_PUBLIC_SERVER_URL

export const maxDuration = 60

export async function POST(req: Request) {
  console.log('hit rag/assistant')

  const requestData: ragAssistantRequestType = await req.json()
  console.log('Received request data:', requestData)

  if (!requestData.userId) {
    return NextResponse.json(
      { message: 'Unauthorized. User ID is required.' },
      { status: 401 }
    )
  }

  const systemPrompt = createSystemMessage(
    requestData.originalAsk,
    requestData.nodeQuestion
  )

  const model_name = 'gpt-4o-mini'

  try {
    // Search the web for information using Brave Search API
    const searchResult = await generateText({
      model: azure(model_name),
      prompt: systemPrompt,
      tools: {
        searchBrave: tool({
          description: 'Search the web for information using Brave Search API',
          parameters: z.object({
            search_query: z
              .string()
              .describe(
                'You are a rephraser and always respond with a rephrased version of the input that is given to a search engine API. Always be succinct and use the same words as the input. ONLY RETURN THE REPHRASED VERSION OF THE INPUT'
              ),
          }),
          execute: async ({ search_query }) => {
            console.log('Search Query:', search_query)
            const searchResults = await CustomBraveSearch(search_query)

            // Call the server-side API to handle database operations
            await fetch(`${baseUrl}/api/rag/db-operations`, {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                operation: RAGDBOperations.LOG_SEARCH,
                data: {
                  searchQuery: search_query,
                  searchResults,
                  requestData,
                  model_name,
                  systemPrompt,
                },
              }),
            })

            return processSearchResults(searchResults)
          },
        }),
      },
      // Always use the search tool
      toolChoice: 'required',
    })

    console.log('Search results:', searchResult)

    // From search results array, create the LLM context in string format
    const llmContext = llmContextFromSearchResults(
      searchResult.toolResults[0].result,
      numberOfLinksToUse
    )

    const fullSystemPrompt = systemPrompt + llmContext
    console.log('fullSystemPrompt', fullSystemPrompt)

    const metadata = new StreamData()

    // Append the search query and search results to the metadata, so client can access it
    metadata.append({
      searchQuery: searchResult.toolResults[0].args.search_query,
      searchResults: searchResult.toolResults[0].result,
    })

    // Generate the response from the LLM
    const result = await streamText({
      model: azure(model_name),
      prompt: fullSystemPrompt,
      async onFinish(result) {
        metadata.close()

        // Call the server-side API to handle database operations
        await fetch(`${baseUrl}/api/rag/db-operations`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            operation: RAGDBOperations.LOG_AND_SAVE_RESPONSE,
            data: {
              fullSystemPrompt,
              // Combine the response with the search context
              result: result.text,
              model_name,
              requestData,
            },
          }),
        })
      },
    })

    return result.toDataStreamResponse({ data: metadata })
  } catch (error) {
    console.error('Error in streamText:', error)
    return NextResponse.json(
      { message: 'Internal Server Error.' },
      { status: 500 }
    )
  }
}

function createSystemMessage(
  originalAsk: string,
  nodeQuestion: string
): string {
  return `
  As a team of elite consultants, we are solving this problem: ${originalAsk}

  You will be given a set of related contexts to the question in the next message, each starting with a reference number like ¹²³⁴⁵⁶⁷⁸⁹ in superscript form. Please use the context and cite the context at the end of each sentence if applicable.

  Give your answer in accurate, analytical, and concrete ways based on the contexts provided. Your clients are very busy and biased to fact and data, go straight to the point, with no intro or outro.

  Please cite the contexts with the reference numbers, in the format ¹. If a sentence comes from multiple contexts, please list all applicable citations and separate by ·, like ¹·²·³.

  Remember, don't blindly repeat the contexts verbatim, you need to digest it and give an insightful answer. Here is the client question:
  ${nodeQuestion}

  `
}

import { streamText } from 'ai'
import { NextResponse } from 'next/server'
import { OpenAIUsageType } from '@prisma/client'
import { logAPIUsage } from '@/app/api/utils'
import { ragSaveRequestType, ragGenerateRequestType } from '@/app/types/api'
import { azure } from '@ai-sdk/azure'

const baseUrl =
  process.env.NODE_ENV === 'development'
    ? 'http://localhost:3000'
    : process.env.NEXT_PUBLIC_SERVER_URL

// IMPORTANT! Set the runtime to edge
export const runtime = 'edge'

// Define the POST function
export async function POST(req: Request) {
  try {
    const requestData: ragGenerateRequestType = await req.json()
    const {
      currentUser,
      context,
      conversationId,
      issueTreeId,
      selectedNodeId,
      originalAsk,
      nodeQuestion,
    } = requestData

    const systemMessage = createSystemMessage(
      originalAsk,
      context,
      nodeQuestion
    )
    console.log('formattedSystemMessage', systemMessage)

    const modelName = 'gpt-4o-mini'
    const result = await streamText({
      model: azure(modelName),
      messages: [{ role: 'system', content: systemMessage }],
      temperature: 0.6,
      onFinish: async ({ text }) => {
        console.log('api/rag/generate_response | completion:', text)
        await Promise.all([
          // record the API usage to the database as audit log
          logAPIUsage({
            open_ai_usage_type: OpenAIUsageType.RAG_GENERATE_RESPONSE,
            model_name: modelName,
            input_text: systemMessage,
            output_text: text,
            currentUser: currentUser,
            conversationId: conversationId,
          }),
          saveResponse({
            issueTreeId: issueTreeId,
            currentUser: currentUser,
            conversationId: conversationId,
            selectedNodeId: selectedNodeId,
            generationInput: systemMessage,
            generationOutput: text,
          }),
        ])
      },
    })

    // Respond with the stream
    return result.toDataStreamResponse()
  } catch (error) {
    // Log any errors to the console and return a 500 error response
    console.log('api/rag/generate_response | error:', error)
    return new NextResponse('Error', { status: 500 })
  }
}

function createSystemMessage(
  originalAsk: string,
  context: string,
  nodeQuestion: string
): string {
  return `
As a team of elite consultants, we are solving this problem: ${originalAsk}

You will be given a set of related contexts to the question, each starting with a reference number like ¹²³⁴⁵⁶⁷⁸⁹ in superscript form Please use the context and cite the context at the end of each sentence if applicable.

Give your answer in accurate, analytical, and concrete ways based on the contexts provided. Your clients are very busy and biased to fact and data, go straight to the point, with no intro or outro

Please cite the contexts with the reference numbers, in the format ¹. If a sentence comes from multiple contexts, please list all applicable citations and separate by ·, like ¹·²·³

Here is the set of contexts:
{
${context}
}

Remember, don't blindly repeat the contexts verbatim, you need to digest it and give an insightful answer, here is the client question
${nodeQuestion}
`
}

async function saveResponse(data: ragSaveRequestType): Promise<void> {
  try {
    const response = await fetch(`${baseUrl}/api/rag/save_response`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    })

    if (!response.ok) {
      throw new Error(`Server response: ${response.status}`)
    }
    await response.json()
  } catch (error) {
    console.error('Error in saveResponse:', error)
  }
}

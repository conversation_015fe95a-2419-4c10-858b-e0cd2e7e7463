// import { NextResponse } from "next/server";

// import prisma from "@/app/libs/prismadb";
// import { IssueTree, IssueTreeStatus, UserStatus } from "@prisma/client";
// import getPromptById from "@/app/actions/getPromptById";
// import getIssueTreeById from "@/app/actions/getIssueTreeById";

// export async function POST(request: Request) {
//   try {
//     const {
//       summaryText,
//       questionDirectionString,
//       skippedQuestionsString,
//       answeredQuestionsString,
//       currentUser,
//       conversationId,
//       issueTreeId,
//     } = await request.json();

//     if (!currentUser?.id || !currentUser?.email) {
//       return new NextResponse("Unauthorized", { status: 400 });
//     }

//     // Block them from accessing API if they are not active
//     if (currentUser?.status !== UserStatus.ACTIVE) {
//       return new NextResponse("Unauthorized", { status: 401 });
//     }

//     if (!summaryText) {
//       return new NextResponse("Summary text is empty", {
//         status: 400,
//       });
//     }

//     const promptId = "followup_questions_generation";
//     const qustionGenerationPrompt = await getPromptById(promptId);

//     interface IssueTreeConfig {
//       directive: string;
//       existing_summary: string;
//       original_ask: string;
//     }
//     const currentIssueTree = (await getIssueTreeById(
//       issueTreeId,
//       currentUser
//     )) as IssueTree;
//     // console.log(currentIssueTree, "currentIssueTree");
//     const issueTreeConfig: IssueTreeConfig =
//       currentIssueTree?.config as unknown as IssueTreeConfig;

//     let formattedSystemMessage = `${qustionGenerationPrompt?.content}\n\n`;

//     if (issueTreeConfig?.original_ask) {
//       formattedSystemMessage += `The original ask from the user is {${issueTreeConfig?.original_ask}}\n\n`;
//     }

//     formattedSystemMessage += `Below is the response summary based on previous questions, **ASK FOR TOPICS THAT WE DID NOT COVER, DO NOT USE CONTEXT BELOW AS EXAMPLE ANSWERS**: {${summaryText}}\n\n`;

//     if (skippedQuestionsString?.length > 0) {
//       formattedSystemMessage += `**IMPORTANT: DO NOT INCLUDE THE FOLLOWING QUESTIONS** {${skippedQuestionsString}}\n\n`;
//     }

//     if (answeredQuestionsString?.length > 0) {
//       formattedSystemMessage += `**IMPORTANT: DO NOT INCLUDE THE FOLLOWING QUESTIONS UNLESS UNCLEAR OR REQUIRING DEEP DIVE** {${answeredQuestionsString}}\n\n`;
//     }

//     if (questionDirectionString) {
//       formattedSystemMessage += `**User explicitly wants to cover these areas in followup questions:** {${questionDirectionString}}\n\n`;
//     }

//     formattedSystemMessage +=
//       "**Reminder: Generate questions that explore new dimensions or aspects of the project that were not covered in the initial summary.** Please take a breathe and think step by step to structure the problem before output";

//     console.log("formattedSystemMessage", formattedSystemMessage);

//     // Mark existing issue tree as completed
//     await prisma.issueTree.update({
//       where: {
//         id: issueTreeId,
//       },
//       data: {
//         status: IssueTreeStatus.COMPLETED,
//       },
//     });

//     // Create the issue tree under the before route to the same page
//     console.log("Going to create new issue tree");
//     const newIssueTree = await prisma.issueTree.create({
//       data: {
//         creator_id: currentUser.id,
//         conversation_id: conversationId,
//         prompt: formattedSystemMessage,
//         status: IssueTreeStatus.INITIALIZED,
//         config: {
//           directive: questionDirectionString,
//           existing_summary: summaryText,
//           original_ask: issueTreeConfig?.original_ask,
//         },
//       },
//     });
//     console.log("newIssueTree", newIssueTree);

//     return NextResponse.json(conversationId);
//   } catch (error) {
//     return new NextResponse("Internal Error", { status: 500 });
//   }
// }

// // Import necessary modules
// import { Configuration, OpenAIApi } from "openai-edge";
// import { OpenAIStream, StreamingTextResponse } from "ai";
// import { NextResponse } from "next/server";
// import { IssueTree, IssueTreeStatus, OpenAIUsageType } from "@prisma/client";
// import { logAPIUsage } from "@/app/api/utils";
// // import getPromptById from "@/app/actions/getPromptById";
// // import getIssueTreeById from "@/app/actions/getIssueTreeById";

// // Create an OpenAI API client (that's edge friendly!)
// const config = new Configuration({
//   apiKey: process.env.OPENAI_API_KEY,
// });
// const openai = new OpenAIApi(config);

// // IMPORTANT! Set the runtime to edge
// export const runtime = "edge";

// const baseUrl =
//   process.env.NODE_ENV === "development"
//     ? "http://localhost:3000"
//     : process.env.NEXT_PUBLIC_SERVER_URL;

// // Define the POST function
// export async function POST(req: Request) {
//   interface IssueTreeConfig {
//     directive: string;
//     existing_summary: string;
//     original_ask: string;
//   }
//   try {
//     // Extract the `messages`, `conversationId`, and `currentUser` from the body of the request
//     const {
//       messages,
//       conversationId,
//       currentUser,
//       issueTreeId,
//       answeredQuestionsString,
//     } = await req.json();

//     console.log(
//       "api/issuetree/generate_questions:",
//       messages,
//       conversationId,
//       currentUser
//       //   answeredQuestionsString
//     );

//     // Construct system message
//     const systemPromptMessageFetch = fetch(`${baseUrl}/api/issuetree/load`, {
//       method: "POST",
//       headers: {
//         "Content-Type": "application/json",
//       },
//       body: JSON.stringify({
//         conversationId: conversationId,
//         currentUser: currentUser,
//         isReturnAll: true,
//       }),
//     })
//       .then((res) => {
//         // console.log(res, "res");
//         if (!res.ok) {
//           throw new Error(`Server responded with status ${res.status}`);
//         }
//         return res.json();
//       })
//       .then((data) => {
//         // console.log(data, "data");
//         // handle the data returned from the server
//         const currentIssueTree = data as IssueTree;
//         // console.log(currentIssueTree, "currentIssueTree");
//         const issueTreeConfig: IssueTreeConfig =
//           currentIssueTree?.config as unknown as IssueTreeConfig;
//         const originalAsk = issueTreeConfig?.original_ask;
//         const existingSummary = issueTreeConfig?.existing_summary;
//         // console.log(originalAsk, existingSummary, "issueTreeConfig");
//         const formattedSystemMessage = `
// As a team of elite consultants, we are tasked with diverse client projects and help them to structure the project\n
// Currently, we have finished the interview with their core teams and clarified some questions. Below are their responses to our questions\n
// {\n${answeredQuestionsString}\n}\n
// In the previous session, we summarized the requirement as this already
// {\n${existingSummary}\n}\n
// The original ask from the client is
// {\n${originalAsk}\n}\n
// Now, your task is to create a **comprehensive yet easily digestible summary** that combines our initial summary with the insights from our most recent client interview. This summary should be in paragraph form for easy reading. **It is crucial to include all details, especially numerical data and factual elements, from both the initial and most recent client interactions for future decision-making. OMIT NO DETAILS AND AVOID OVERSIMPLIFICATION.**\n\n
// Structure the summary into around five paragraphs, each starting with a topic sentence that clearly previews the content. Write as if you are a part of the client internal team, making sure to cover the who, why, what, where, and when aspects in a third-party descriptive manner.\n\n
// Remember, the integrity and completeness of the information are non-negotiable. Every detail, no matter how small, is vital for future decision-making and must be included in the summary. OMIT NO DETAILS AND AVOID OVERSIMPLIFICATION.\n\n
// Next line starts with "We aim to"
// `;
//         console.log(
//           "formattedSystemMessage",
//           formattedSystemMessage.slice(0, 100)
//         );
//         return formattedSystemMessage;
//       })
//       .catch((error) => {
//         // handle any errors
//         console.error("Error api/chat/systemPromptMessageFetch:", error);
//       });

//     const formattedSystemMessage = await systemPromptMessageFetch;

//     if (!formattedSystemMessage) {
//       return new NextResponse("Error", { status: 500 });
//     }

//     // Use more wider model if the input tokens is more than 3000 tokens/ ~12000 characters
//     const model_name = "gpt-4o-mini";
//     console.log("model_name", model_name);
//     console.log("formattedSystemMessage", formattedSystemMessage.slice(0, 100));

//     // Ask OpenAI for a streaming chat completion given the prompt
//     const response = await openai.createChatCompletion({
//       model: model_name,
//       stream: true,
//       messages: [{ role: "system", content: formattedSystemMessage }],
//     });

//     // Convert the response into a friendly text-stream
//     const stream = OpenAIStream(response, {
//       // Save the response
//       async onCompletion(completion) {
//         console.log("api/issuetree/generate_summary | completion:", completion);
//         console.log(OpenAIUsageType.ISSUE_TREE_GENERATE_SUMMARY);
//         await Promise.all([
//           // record the API usage to the database as audit log
//           logAPIUsage({
//             open_ai_usage_type: OpenAIUsageType.ISSUE_TREE_GENERATE_SUMMARY,
//             model_name: model_name,
//             input_text: JSON.stringify([
//               { role: "system", content: formattedSystemMessage },
//             ]),
//             output_text: completion,
//             currentUser: currentUser,
//             conversationId: conversationId,
//           }),

//           // Save the completion to the database
//           updateTreeContent({
//             currentUser: currentUser,
//             issueTreeId: issueTreeId,
//             summary_context: completion,
//           }),
//         ]);
//       },
//     });

//     // // Respond with the stream
//     return new StreamingTextResponse(stream);
//     // return new NextResponse(formattedSystemMessage, { status: 200 });
//   } catch (error) {
//     // Log any errors to the console and return a 500 error response
//     console.log("api/issuetree/generate_summary | error:", error);
//     return new NextResponse("Error", { status: 500 });
//   }
// }

// async function updateTreeContent({
//   currentUser,
//   issueTreeId,
//   summary_context,
// }: {
//   currentUser: any;
//   issueTreeId: string;
//   summary_context: string;
// }): Promise<void> {
//   try {
//     const response = await fetch(`${baseUrl}/api/issuetree/update`, {
//       method: "POST",
//       headers: {
//         "Content-Type": "application/json",
//       },
//       body: JSON.stringify({
//         currentUser: currentUser,
//         issueTreeId: issueTreeId,
//         summary_context: summary_context,
//       }),
//     });

//     if (!response.ok) {
//       throw new Error("Server response: " + response.status);
//     }

//     const result = await response.json();
//     console.log("updateTreeContent response:", result);
//   } catch (error) {
//     console.error("Error in updateTreeContent:", error);
//   }
// }

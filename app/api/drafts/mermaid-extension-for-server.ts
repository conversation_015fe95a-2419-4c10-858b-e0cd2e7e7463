import { mergeAttributes, Node } from "@tiptap/core";

// Basically the same implementation as client but removing the client side only code
export default Node.create({
  name: "mermaidComponent",

  group: "block",

  content: "text*",

  code: true,

  addAttributes() {
    return {};
  },

  parseHTML() {
    return [
      {
        tag: "mermaid-component",
      },
    ];
  },

  renderHTML({ HTMLAttributes }) {
    return ["mermaid-component", mergeAttributes(HTMLAttributes)];
  },
});

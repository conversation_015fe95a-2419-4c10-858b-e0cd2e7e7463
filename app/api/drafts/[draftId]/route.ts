import { NextRequest, NextResponse } from "next/server";

import getCurrentUser from "@/app/actions/getCurrentUser";
import prisma from "@/app/libs/prismadb";
import { UserStatus } from "@prisma/client";

type Params = {
  draftId: string;
};

// This is for user to update the draft content
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<Params> }
) {
  try {
    const { draftId } = await params;
    const currentUser = await getCurrentUser();
    const updatedContent = await request.json();

    if (!currentUser?.id || !currentUser?.email) {
      return new NextResponse("Unauthorized", { status: 400 });
    }

    // Block them from accessing API if they are not active
    if (currentUser?.status !== UserStatus.ACTIVE) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const updatedDraft = await prisma.draft.update({
      where: {
        id: draftId,
      },
      data: {
        content: JSON.stringify(updatedContent?.content), // assuming the updated content is in the "content" field
      },
    });

    return NextResponse.json(updatedDraft);
  } catch (error) {
    console.log(error, "ERROR_MESSAGES");
    return new NextResponse("Error", { status: 500 });
  }
}

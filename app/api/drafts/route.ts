import { NextResponse } from "next/server";
import prisma from "@/app/libs/prismadb";
import { UserStatus } from "@prisma/client";
// Below are mainly for generating JSON for editor display
import { generateJSON } from "@tiptap/html";
import Bold from "@tiptap/extension-bold";
import Document from "@tiptap/extension-document";
import Paragraph from "@tiptap/extension-paragraph";
import Text from "@tiptap/extension-text";
import Underline from "@tiptap/extension-underline";
import Color from "@tiptap/extension-color";
import ListItem from "@tiptap/extension-list-item";
import OrderedList from "@tiptap/extension-ordered-list";
import TextStyle from "@tiptap/extension-text-style";
import Heading from "@tiptap/extension-heading";
import CodeBlock from "@tiptap/extension-code-block";
import mermaidExtension from "./mermaid-extension-for-server";

// import mermaid
const marked = require("marked");

export async function POST(request: Request) {
  try {
    // Parse the request body as JSON
    const body = await request.json();

    // Extract the necessary fields from the request body
    const {
      status,
      type,
      content,
      original_content,
      currentUser,
      conversation_id,
      prompt_id,
      exact_prompt,
      //   leave some flexibility for the future
      //   config,
    } = body;

    // Check if the user is authorized
    if (
      !currentUser?.id ||
      !currentUser?.email ||
      currentUser?.status !== UserStatus.ACTIVE
    ) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const HTMLContent = marked.parse(content);
    // Special handling for mermaid
    const replaceMermaidTags = (html: string) => {
      return html.replace(
        /<pre><code class="language-mermaid">([\s\S]*?)<\/code><\/pre>/g,
        "<mermaid-component>$1</mermaid-component>"
      );
    };

    const modifiedHTMLContent = replaceMermaidTags(HTMLContent);
    const JSONContent = generateJSON(modifiedHTMLContent, [
      Document,
      Paragraph,
      Text,
      Bold,
      Underline,
      Color,
      ListItem,
      TextStyle,
      Heading,
      OrderedList,
      CodeBlock,
      mermaidExtension,
      // other extensions …
    ]);

    // Create a new message in the database using Prisma
    const newDraft = await prisma.draft.create({
      data: {
        status: status,
        type: type,
        // content and original_content are the same initially
        // if user updates the content, we will update the content
        // and keep the original_content as the original
        // The goal is to capture what are changed by user if needed
        content: JSONContent ? JSON.stringify(JSONContent) : content,
        // Instead of JSONContent, we store the original markdown, easier to read
        // This field will not change after creation
        original_content: original_content,
        user_id: currentUser.id,
        conversation_id: conversation_id,
        prompt_id: prompt_id,
        exact_prompt: exact_prompt,
      },
    });

    // Return the newly created message as a JSON response
    return NextResponse.json(newDraft);
  } catch (error) {
    console.error(error);

    // If an error occurs, return a 500 status code with an "Error" message
    return new NextResponse("Error", { status: 500 });
  }
}

import Stripe from "stripe";
import {
  createProductRecord,
  updateProductRecord,
  createPriceRecord,
  updatePriceRecord,
  manageSubscriptionRecord,
  handleCheckoutSessionCompleted,
  SubscriptionEventType,
} from "./utils";

// Main function to handle different webhook events
export async function handleWebhookEvent(event: Stripe.Event) {
  switch (event.type) {
    case "product.created":
      await createProductRecord(event.data.object as Stripe.Product);
      break;
    case "product.updated":
      await updateProductRecord(event.data.object as Stripe.Product);
      break;
    case "price.created":
      await createPriceRecord(event.data.object as Stripe.Price);
      break;
    case "price.updated":
      await updatePriceRecord(event.data.object as Stripe.Price);
      break;
    case "customer.subscription.created":
      await manageSubscriptionRecord(
        event.data.object as Stripe.Subscription,
        SubscriptionEventType.Created
      );
      break;
    case "customer.subscription.updated":
      await manageSubscriptionRecord(
        event.data.object as Stripe.Subscription,
        SubscriptionEventType.Updated
      );
      break;
    case "checkout.session.completed":
      const checkoutSession = event.data.object as Stripe.Checkout.Session;
      if (checkoutSession.mode === "subscription") {
        await handleCheckoutSessionCompleted(checkoutSession);
      }
      break;
    default:
      console.warn(`Unhandled relevant event type: ${event.type}`);
  }
}

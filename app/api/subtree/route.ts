// This is to create the new subtree

import { NextResponse } from 'next/server'

import prisma from '@/app/libs/prismadb'
import { SubtreeStatus } from '@prisma/client'
import { SubtreeRequestType } from '@/app/types/api'

export async function POST(request: Request) {
  try {
    const requestData: SubtreeRequestType = await request.json()
    const {
      conversationId,
      userId,
      issueTreeId,
      selectedNodeId,
      prompt,
      generation_output,
    } = requestData

    if (!userId || !conversationId) {
      return new NextResponse('Unauthorized', { status: 400 })
    }

    const newSubtree = await prisma.subtree.create({
      data: {
        creator_id: userId,
        conversation_id: conversationId,
        issue_tree_id: issueTreeId,
        prompt: prompt,
        generation_output: generation_output,
        status: SubtreeStatus.ACTIVE,
        selected_node_id: selectedNodeId,
      },
    })

    return NextResponse.json(newSubtree)
  } catch {
    return new NextResponse('Internal Error', { status: 500 })
  }
}

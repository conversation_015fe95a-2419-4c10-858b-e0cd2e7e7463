// For subtree tree content update
import { NextResponse } from "next/server";
import prisma from "@/app/libs/prismadb";
import { SubtreeStatus } from "@prisma/client";

// This is for user to update the draft content
export async function POST(req: Request) {
  try {
    const requestData = await req.json();

    // Extract the issueTreeId and leave the rest of the fields in updateData
    const { issueTreeId, selectedNodeId, ...updateData } = requestData;
    console.log("updateData", issueTreeId, selectedNodeId, updateData);

    // Get the most latest created ACTIVE subtree
    // There should be only one active subtree for a selected node, but just in case there are bugs [multiple]
    const subtree = await prisma.subtree.findFirst({
      where: {
        issue_tree_id: issueTreeId,
        selected_node_id: selectedNodeId,
        status: SubtreeStatus.ACTIVE,
      },
      orderBy: {
        updated_at: "desc",
      },
    });

    if (subtree === null) {
      return NextResponse.json({ error: "subtrees not found" });
    }

    const updatedSubtree = await prisma.subtree.update({
      where: {
        id: subtree.id,
      },
      data: {
        ...updateData,
      },
    });

    return NextResponse.json(updatedSubtree);
  } catch (error) {
    console.log(error, "ERROR_MESSAGES");
    return new NextResponse("Error", { status: 500 });
  }
}

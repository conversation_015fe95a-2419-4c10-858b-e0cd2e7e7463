// Function to construct prompt
export const constructPrompt = (
  markdown: string,
  originalAskText: string,
  nodeMetadata: any,
  currentId: string,
  childs: string[],
  customDirective: string
) => {
  const childsLabel = childs
    .map((childId) => nodeMetadata[childId].label)
    .join("\n");
  const currentNodeLabel = nodeMetadata[currentId].label;

  // Trim and check if customDirective is not empty
  const customDirectiveLine =
    customDirective.trim().length > 0
      ? `The client has provided specific instructions for this task: {${customDirective.trim()}}\n\n`
      : "";

  const prompt = `As a team of elite consultants, we are tasked with diverse client projects and help them to structure the project. Our current focus is refining our project structuring process by creating detailed subtrees within our broader issue trees. This approach is designed to allow our clients to engage more deeply with specific aspects of their projects, providing a granular view that complements the overarching structure.

Please generate a list of questions that **emphasize the underlying rationale ('WHY') and objectives ('WHAT')** of the project, focusing on a particular segment of the issue tree that has not been previously covered. We are looking for a multi-layered issue tree that explores new categories thoroughly. Each subcategory should contain 2-3 related questions to provide a comprehensive yet concise exploration.

Use a single level-one heading at the beginning for the project title, followed by sub-headings for new categories, sub-categories, and further sub-divisions as necessary. Ensure that each question aligns with its corresponding new category and sub-category to maintain the hierarchical structure and depth. Subtree questions should fit within the current project structure and expand on the marked category without overlapping with existing categories. Avoid implementation details at this stage.

**Examples should be included for each question and should be hypothetical but realistic data points or scenarios that are ready-to-use and can be directly cited or used in a presentation or report. Whenever possible, use real companies or data to provide examples. These should NOT be action items or steps for further analysis. They should require NO further elaboration or analysis.**

Examples should be formatted as a continuous sentence following a single "- Examples:" tag.

For example: {
# Business Goals
## Revenue Streams
### What are the potential revenue streams for the platform?
- Examples: The platform has successfully diversified its revenue streams, enhancing its financial robustness. Last quarter, in-app purchases including premium filters and effects generated $20 million. A subscription model offering ad-free experiences consistently contributes an additional $5 million monthly. Exclusive partnerships with content creators have also proven lucrative, bringing in $2 million over the past six months. Additional revenue sources include in-app advertising, which leverages high user engagement rates to generate approximately $15 million last quarter. Merchandising linked to popular viral trends has yielded $3 million in the past year. These diverse streams underscore the platform's capacity to monetize its user base effectively across multiple channels.
### {More related questions}

## Market Expansion
### How can the platform expand into new geographical markets?
- Examples: The platform can expand into new geographical markets by developing localized content strategies, such as collaborating with local influencers to cater to regional preferences, which increased the user base by 30% in Southeast Asia last year. Adapting features to support local languages and payment methods has boosted engagement, as seen with a 25% increase in India. Strategic partnerships with local businesses and compliance with local regulations are crucial, as demonstrated by successful market entries in Europe and Russia, with targeted marketing campaigns during local festivals driving up new sign-ups by 40% in Latin America.
### {More related questions}
}

Original ask from client: {${originalAskText}}

Given the existing tree structure below, please generate a subtree that explores the specified area: ${currentNodeLabel}, please DO NOT overlap with the existing structure:
{${markdown}}

DO NOT include these existing subcategories/questions under **${currentNodeLabel}**:
{${childsLabel}}

Please ensure that the questions under the new subtree are solely under **${currentNodeLabel}** and do not repeat any categories or questions from the current tree structure. Your root heading should be indicated heading without outputting other categories/questions, those are just for you to understand the context to avoid overlapping.
${customDirectiveLine}

Let's take a moment to think step by step and structure the problem before outputting, remember, the example should be realistic, your next line MUST start with # ${currentNodeLabel}
`;
  return prompt;
};

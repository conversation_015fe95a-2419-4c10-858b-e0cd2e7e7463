import { NextResponse } from "next/server";
import prisma from "@/app/libs/prismadb";
import {
  ConversationType,
  IssueTreeStatus,
  UserStatus,
  ConversationStatus,
  Conversation,
  IssueTree,
  User,
} from "@prisma/client";
import getCurrentUser from "@/app/actions/getCurrentUser";
import getConversations from "@/app/actions/getConversations";
import {
  calculateMaxActiveConversations,
  countActiveConversationsAfterDate,
} from "@/lib/utils";
import { activeConversationCutoffDate } from "@/app/configs";
import { ConversationLayoutType } from "@/app/types";

export async function POST(request: Request) {
  try {
    const currentUser = await getCurrentUser();
    if (
      !currentUser ||
      !currentUser?.id ||
      !currentUser?.email ||
      currentUser?.status !== UserStatus.ACTIVE
    ) {
      console.log("User not authorized");
      return NextResponse.json("Unauthorized", { status: 401 });
    }

    const { description } = await request.json();
    if (!description) {
      console.log("No description provided");
      return NextResponse.json("No description provided", { status: 400 });
    }

    if (await hasTooManyActiveConversations(currentUser)) {
      console.log("Too many active conversations");
      return NextResponse.json("Too many active conversations", {
        status: 400,
      });
    }

    // Technically, if we redesign the whole app, conversation concept maybe not needed
    // Our app starts from conversation, then pivot to having the issue tree inside conversation
    const newConversation = await createConversation(currentUser, description);
    console.log("New conversation created:", newConversation.id);

    const newIssueTree = await createIssueTree(
      currentUser,
      newConversation,
      description
    );
    console.log("Issue tree created", newIssueTree.id);

    return NextResponse.json(sanitizeConversation(newConversation));
  } catch (error) {
    console.error("Error in POST /api/conversation:", error);
    return NextResponse.json("Internal Error", { status: 500 });
  }
}

async function hasTooManyActiveConversations(
  user: User | null
): Promise<boolean> {
  const conversations = await getConversations();
  const currentActiveConversations = countActiveConversationsAfterDate(
    conversations,
    ConversationStatus.ACTIVE,
    activeConversationCutoffDate
  );
  const maxActiveConversations = calculateMaxActiveConversations(user);
  return currentActiveConversations >= maxActiveConversations;
}

async function createConversation(
  user: User,
  description: string
): Promise<Conversation> {
  const promptId = "initial_questions_generation";
  const formattedSystemMessage = createSystemMessage(description);

  return prisma.conversation.create({
    data: {
      creator_id: user.id,
      conversation_type: ConversationType.ISSUE_TREE,
      prompt_id: promptId,
      config: {
        prompt: {
          system_message: formattedSystemMessage,
          description: description,
        },
      },
      title: description,
    },
  });
}

async function createIssueTree(
  user: User,
  conversation: Conversation,
  description: string
): Promise<IssueTree> {
  const formattedSystemMessage = createSystemMessage(description);
  return prisma.issueTree.create({
    data: {
      creator_id: user.id,
      conversation_id: conversation.id,
      prompt: formattedSystemMessage,
      status: IssueTreeStatus.INITIALIZED,
      config: {
        directive: "initial",
        existing_summary: description,
        original_ask: description,
      },
    },
  });
}

function sanitizeConversation(
  conversation: Conversation
): ConversationLayoutType {
  const {
    id,
    creator_id,
    title,
    conversation_type,
    conversation_status,
    is_hidden,
    created_at,
    updated_at,
  } = conversation;

  return {
    id,
    creator_id,
    title,
    conversation_type,
    conversation_status,
    is_hidden,
    created_at,
    updated_at,
    issuetree_updated_at: updated_at,
  } as ConversationLayoutType;
}

function createSystemMessage(description: string): string {
  const promptTemplate = `
As a team of elite consultants, we are tasked with diverse client projects and help them to structure the project.

Please generate a list of questions that **emphasize the underlying rationale ('WHY') and objectives ('WHAT')** and delve deeper into each aspect of the project. We are looking for a multi-layered issue tree that explores each category thoroughly. Use a single level-one heading at the beginning for the project title, followed by sub-headings for categories, sub-categories, and further sub-divisions as necessary. Ensure that each question aligns with its corresponding category and sub-category to maintain the hierarchical structure and depth. Depending on the problem nature, the questions should cover a wide range of topics, for example, the business-related projects should include but not be limited to business context, product specifics, user scale and base, purpose, platforms, product market, pain points, competitive advantage, market/society cycle, and business model. Try to apply this thinking to various kinds of projects.

**Examples should be included for each question and they should be realistic data points or scenarios that are ready to use and can be directly cited or used in a presentation or report. These should NOT be action items or steps for further analysis. They should require NO further elaboration or analysis.**

Examples should be formatted as a continuous sentence following a single "- Examples:" tag.

For example: {
# TikTok-like Platform Strategy
## Business Goals
### Revenue Streams
#### What are the potential revenue streams for the platform?
- Examples: The platform has successfully diversified its revenue streams, enhancing its financial robustness. Last quarter, in-app purchases including premium filters and effects generated $20 million. A subscription model offering ad-free experiences consistently contributes an additional $5 million monthly. Exclusive partnerships with content creators have also proven lucrative, bringing in $2 million over the past six months. Additional revenue sources include in-app advertising, which leverages high user engagement rates to generate approximately $15 million last quarter. Merchandising linked to popular viral trends has yielded $3 million in the past year. These diverse streams underscore the platform's capacity to monetize its user base effectively across multiple channels.
{Don't output this: Give more questions}
## User Engagement
### Retention Strategies
#### What specific strategies will be used to retain users on the platform, and how will they be implemented?
- Examples: To bolster user retention, the platform has implemented several strategic initiatives. The introduction of a personalized recommendation algorithm has notably increased the average session duration by 15%. Additionally, gamification features like daily challenges have boosted the number of daily active users by 20%. A loyalty program that offers exclusive content has proven effective, retaining 80% of long-term users over the past year. More granular strategies include personalization tactics, where content feeds tailored to user behavior have led to a 30% increase in daily user interactions. Community-building efforts, such as promoting user-generated challenges, have enhanced community engagement by 25% annually. Moreover, a focus on content diversity, aiming to represent various global cultures, has attracted a 40% increase in international users, broadening the platform's global appeal and user base.
{Don't output this: Give more questions}
## Market Analysis
### User Base and Engagement
#### What is the current user base and level of engagement for a TikTok-like platform?
- Examples: The platform currently boasts 50 million active users in the United States, each spending an average of 30 minutes daily. Engagement metrics have shown a 10% month-over-month increase, with video shares and comments up by 25% and 30%, respectively, in the last quarter. Demographically, the majority of the user base, around 60%, comprises young adults aged 18-24 who are highly active on social media. Over the past year, the platform's global reach expanded by 50%, now including significant user bases in Europe and Asia, with engagement peaking during holiday seasons, marked by a 40% increase in content creation and interaction.
{Don't output this: Give more questions}
## Algorithm
### ...
{Use the same format to elaborate to ask questions from various perspectives, focusing on business context and user, group #### questions using ## category and ### subcategory}
}

Ensure that there's only ONE level-one heading (#) in the entire output and that it's at the beginning. Also, ensure that each set of examples is formatted as a continuous sentence after a single "- Examples:" tag.
`;

  return `
${promptTemplate}\n\n
The user asks: {${description}}\n\n
User may give requests not related to system design, do your best to adopt the format exactly to design that, **you have to output the issue tree regardless of the situation**

Now take a breathe and think step by step to structure the problem before output, consider as diverse and deep as possible to cover every important details for this project
`;
}

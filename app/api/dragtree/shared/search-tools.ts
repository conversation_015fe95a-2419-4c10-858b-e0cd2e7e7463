/**
 * Search tools for AI model integration
 * Defines tools that the AI can use during research generation
 */

import { tool } from 'ai'
import { z } from 'zod'
import {
  braveWebSearch,
  formatSearchResultsForAI,
  createSearchMetadata,
  SearchMetadata,
} from './brave-search'

// Global rate limiter for Brave Search API
// Limit concurrent requests to stay well under 50 QPS limit
// Using 20 concurrent max to account for request duration and provide burst protection
let braveLimit: any = null

// Lazy initialization to avoid issues with module loading
async function getBraveLimit() {
  if (!braveLimit) {
    try {
      // Dynamic import for p-limit (ES modules)
      const { default: pLimit } = await import('p-limit')
      braveLimit = pLimit(20) // Max 20 concurrent Brave API calls
      console.log(
        '🚦 [Search Tools] Brave Search rate limiter initialized (max 20 concurrent)'
      )
    } catch (error) {
      console.warn(
        '⚠️ [Search Tools] p-limit not available, using unbounded requests'
      )
      // Fallback: no limiting
      braveLimit = (fn: any) => fn()
    }
  }
  return braveLimit
}

// Progress tracking for search operations
export type SearchProgressCallback = (status: {
  type: 'searching' | 'completed' | 'error'
  query?: string
  resultCount?: number
  error?: string
}) => void

// NEW: Factory function to build a web search tool with a scoped metadata collector and progress tracking
export function buildSearchTools(
  metadataCollector: SearchMetadata[],
  progressCallback?: SearchProgressCallback
) {
  const webSearch = tool({
    description: `Search the web for current, relevant information to enhance research quality. Use this tool when you need:
- Recent developments or current events
- Specific facts, statistics, or data
- Technical information or specifications
- Expert opinions or authoritative sources
- Market research or industry insights

Guidelines for effective searches:
- Use specific, focused queries
- Include relevant keywords and terms
- Search for authoritative sources
- Verify information across multiple results`,
    parameters: z.object({
      query: z
        .string()
        .describe(
          'The search query to find relevant information. Be specific and include key terms.'
        ),
      count: z
        .number()
        .min(1)
        .max(20)
        .default(10)
        .describe('Number of search results to retrieve (1-20, default: 10)'),
    }),
    execute: async ({ query, count = 10 }, { toolCallId }) => {
      const limit = await getBraveLimit()

      try {
        console.log(
          `🔧 [Tool: Web Search] Executing search: "${query}" (Tool Call ID: ${toolCallId})`
        )

        // Notify frontend about search start
        progressCallback?.({
          type: 'searching',
          query,
        })

        // Use rate limiter to control concurrent Brave API calls
        const searchResults = await limit(() => braveWebSearch(query, count))

        if (searchResults.length > 0) {
          const searchMetadata = createSearchMetadata(query, searchResults)
          metadataCollector.push(...searchMetadata)
          console.log(
            `📊 [Tool: Web Search] Collected ${searchMetadata.length} search result(s) for metadata`
          )
        }

        // Notify frontend about search completion
        progressCallback?.({
          type: 'completed',
          query,
          resultCount: searchResults.length,
        })

        // Return formatted results for AI, and store metadata in collector
        return formatSearchResultsForAI(searchResults)
      } catch (error) {
        console.error('💥 [Tool: Web Search] Error:', error)

        // Notify frontend about search error
        progressCallback?.({
          type: 'error',
          query,
          error: error instanceof Error ? error.message : 'Unknown error',
        })

        return `Error performing web search: ${error instanceof Error ? error.message : 'Unknown error'}`
      }
    },
    experimental_toToolResultContent: result => {
      // Result is now always a string, so just return it as text content
      return [{ type: 'text', text: result as string }]
    },
  })

  return {
    webSearch,
  } as const
}

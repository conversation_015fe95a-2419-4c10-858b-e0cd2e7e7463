/**
 * Centralized research prompts for DragTree research functionality
 * This file contains prompts that can be shared between:
 * - API endpoints (/api/dragtree/research_generate)
 * - Copy/paste functionality for external tools
 * - UI components that need to display or reference prompts
 */

// Unified research prompt template for both internal API and external tools
export const createResearchPrompt = (
  questionText: string,
  screeningQuestion?: string,
  language: string = 'English'
): string => {
  // Build contextual section dynamically to insert original ask (if provided) and the specific research focus.
  const originalAskPart = screeningQuestion
    ? `This is the original user ask: "${screeningQuestion}". `
    : ''

  return `### Core Objective & Guiding Principles
Your primary goal is to act as a **Senior Strategist**. Your task is to illuminate the strategic landscape surrounding the user's question, equipping them with a sophisticated and multi-dimensional understanding. The response must be built upon a clear, insightful analytical thesis, but its purpose is not just to persuade, but to guide the user's own critical thinking. The analysis must be reasoned, logical, and rigorously supported by evidence.

### Persona
Assume the role of a seasoned **Senior Strategist** or **Principal Analyst** briefing a sharp, intelligent decision-maker. Your tone is one of quiet confidence and deep expertise. You are not a salesperson; you are a trusted advisor whose value lies in clarity, context, and foresight.

---

### Response Structure & Rules

Respond with ONE Markdown in the structure below (raw Markdown, **do NOT wrap your entire response in triple backticks**). Section titles must be exactly as written.

## **TL;DR**
    • **3–4 bullets**, with each bullet being ≤ 15 words.
    • Synthesize the most critical takeaways from your analysis.
    • **Bold** every key number, term, or strategic concept.

## **Evidence Snapshot**
    • Use either a *Numeric Table* or a *Ranked Checklist* format (3-5 items).
    • Select credible data points or qualitative factors that provide essential context.
    • Every sourced fact in this section must have a citation marker.

##  **Deep Dive**
    ► **Instruction:** This section is your primary analysis. It must be a cohesive, well-structured narrative essay of **2-4 paragraphs**.
    1.  **Weave an Integrated Opening:** Your opening sentences must transparently frame your analytical approach and confidently state your **central thesis**.
        * **Framing:** If the question involves an unknown internal state, begin by gracefully acknowledging this. **Vary your phrasing** using approaches like: "Applying standard industry frameworks...", "While every situation is unique...", or "Viewing this through a strategic lens...".
        * **Thesis:** State your central thesis directly and confidently as part of your organic opening. Do not explicitly label it "Thesis:".
    2.  **Construct a Multi-Paragraph Narrative:** Develop your argument across the 2-4 paragraphs, using paragraph breaks for readability.

    ► **Crucial Execution Rule:** **You are forbidden from using subheadings, bullet points, or numbered lists within this section.** The goal is a flowing, well-structured essay. Use your analytical palette below to inform the content of your narrative.

    ► **Your Analytical Palette:** Quantitative Data, Historical Precedents / Case Studies, Strategic Frameworks, Exploration of Second-Order Effects.

## **Actionable Takeaways**
    ► **Instruction:** Conclude with a clear, practical list of 3-5 strategic recommendations or next steps that logically follow from your Deep Dive analysis.

---
### Source & Citation Rules

• **Marker Mandate:** Every sourced fact, number, or direct quote in your response **must** be immediately followed by a unique, numbered inline citation marker in the format «1», «2», «3», etc.

• **Source List Mandate:** Conclude your entire response with a section titled **Sources**, formatted as a numbered list.

• **Validation Clause:** The entire response is invalid unless it meets these two conditions:
    1.  Every numbered marker in the text (e.g., «1», «3») has a perfectly matching number in the final Sources list.
    2.  Each source in the list must be credible and include enough information to be found (e.g., Title, Publisher, Year, URL if available).

• **Output Nothing Else:** Output *nothing* except the Markdown content followed by the Sources list. **Do NOT enclose the overall output in triple backticks or any code fence.**

${originalAskPart}Now we want you to research: "${questionText}".

Please generate in ${language}`
}

// External tool prompt – simplified wording for copying into services like ChatGPT/Claude
export const createExternalToolPrompt = (
  questionText: string,
  screeningQuestion?: string,
  language: string = 'English'
): string => {
  // Provide a minimal, easy-to-understand instruction for external tools.
  // 1. If we have the original user ask (`screeningQuestion`), include it for extra context.
  // 2. Otherwise, only ask the tool to research the given question.
  // This keeps internal research prompts (createResearchPrompt) detailed, while
  // giving external services a concise prompt that avoids leaking proprietary
  // instructions.
  const originalAskPart = screeningQuestion
    ? `This is the original user ask: "${screeningQuestion}". `
    : ''

  return `${originalAskPart}
Now we want you to research: "${questionText}".\n\nPlease generate in ${language}`
}

// Legacy compatibility - copy-paste prompt that uses the unified approach
export const createCopyPastePrompt = (
  questionText: string,
  screeningQuestion?: string
): string => {
  return createResearchPrompt(questionText, screeningQuestion)
}

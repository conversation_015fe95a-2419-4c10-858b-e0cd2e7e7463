import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/app/api/auth/authOptions'
import { getDragTree } from '@/app/server-actions/drag-tree'
import { isRateLimited, getRetryAfterSeconds } from '@/app/libs/rateLimiter'

export async function GET(request: NextRequest) {
  try {
    console.log('🔍 [Drag Tree Load] Starting tree load...')

    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      console.log('❌ [Drag Tree Load] Unauthorized - no session')
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const dragTreeId = searchParams.get('dragTreeId')

    if (!dragTreeId) {
      return NextResponse.json(
        { error: 'Drag Tree ID is required' },
        { status: 400 }
      )
    }

    console.log(
      `📋 [Drag Tree Load] Loading tree: ${dragTreeId} for user: ${session.user.id}`
    )

    // Rate limit to avoid repeated rapid loads
    const rateLimitKey = `${session.user.id}:dragtree:load:${dragTreeId}`
    if (isRateLimited(rateLimitKey, 5000)) {
      const retryAfter = getRetryAfterSeconds(rateLimitKey, 5000)
      return NextResponse.json(
        { error: 'Too many requests – please wait a moment.' },
        {
          status: 429,
          headers: { 'Retry-After': retryAfter.toString() },
        }
      )
    }

    const result = await getDragTree(dragTreeId)

    if (!result.success || !result.data) {
      console.log('❌ [Drag Tree Load] Tree not found or unauthorized')
      return NextResponse.json(
        { error: result.error || 'Drag tree not found' },
        { status: 404 }
      )
    }

    // Verify the tree belongs to the user
    if (result.data.user_id !== session.user.id) {
      console.log("❌ [Drag Tree Load] Tree doesn't belong to user")
      return NextResponse.json(
        { error: 'Unauthorized access to this tree' },
        { status: 403 }
      )
    }

    console.log(
      `✅ [Drag Tree Load] Successfully loaded tree: ${dragTreeId} (status: ${result.data.status})`
    )

    return NextResponse.json({
      success: true,
      data: result.data,
    })
  } catch (error) {
    console.error('💥 [Drag Tree Load] Unexpected error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

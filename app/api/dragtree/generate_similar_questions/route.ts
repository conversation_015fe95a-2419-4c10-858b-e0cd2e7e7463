import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/app/api/auth/authOptions'
import {
  DragTreeStatus,
  DragTreeNodeStatus,
  // OpenAIUsageType, // Currently unused
} from '@prisma/client'
import prisma from '@/app/libs/prismadb'
import { updateDragTree } from '@/app/server-actions/drag-tree'
import { generateDragTreeNodeId } from '@/app/(conv)/dragTree/[dragTreeId]/utils/id-generation'
import { DragtreeGenerateSimilarQuestionsRequestType } from '@/app/types/api'
import { streamText } from 'ai'
import { azure } from '@ai-sdk/azure'
import { logOpenAIUsage_serverAction } from '@/app/server-actions/log_openai_usage'

export const maxDuration = 60

export async function POST(request: NextRequest) {
  try {
    console.log('🤖 [Similar Questions] Starting generation...')

    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      console.log('❌ [Similar Questions] Unauthorized - no session')
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const requestData: DragtreeGenerateSimilarQuestionsRequestType =
      await request.json()
    const {
      conversationId: _conversationId, // Currently unused
      userId: _userId, // Currently unused
      nodeId,
      categoryLabel,
      existingQuestions: _existingQuestions, // Currently unused
      originalContext: _originalContext, // Currently unused
    } = requestData

    if (!nodeId || !categoryLabel) {
      return NextResponse.json(
        { error: 'Node ID and category label are required' },
        { status: 400 }
      )
    }

    console.log(
      `🔍 [Similar Questions] Processing for node: ${nodeId} (${categoryLabel})`
    )

    // Find the active drag tree for this user
    const activeDragTree = await prisma.dragTree.findFirst({
      where: {
        user_id: session.user.id,
        status: DragTreeStatus.ACTIVE,
      },
      orderBy: {
        updated_at: 'desc',
      },
    })

    if (!activeDragTree) {
      console.log('❌ [Similar Questions] No active drag tree found')
      return NextResponse.json(
        { error: 'No active drag tree found' },
        { status: 404 }
      )
    }

    console.log(`📋 [Similar Questions] Using tree: ${activeDragTree.id}`)

    /* 🚧 COMMENTED OUT FOR TESTING - RESTORE WHEN READY FOR PRODUCTION
    const systemMessage = constructSimilarQuestionsPrompt(
      categoryLabel,
      existingQuestions,
      originalContext
    );

    const model_name = "gpt-4o-mini";
    try {
      const result = await streamText({
        model: azure(model_name),
        messages: [{ role: "system", content: systemMessage }],
        temperature: 0.7,
        onFinish: async ({ text }) => {
          console.log(
            "api/dragtree/generate_similar_questions: completion:",
            text
          );
          await logOpenAIUsage_serverAction({
            open_ai_usage_type: OpenAIUsageType.DRAGTREE_GENERATE_QUESTIONS,
            model_name: model_name,
            input_text: systemMessage,
            output_text: text,
            userId: userId,
            conversationId: conversationId,
          });
        },
      });

      return result.toDataStreamResponse();
    } catch (error) {
      console.log("api/dragtree/generate_similar_questions: error:", error);
      return new NextResponse("Error", { status: 500 });
    }
    */

    // Generate content (static for now)
    console.log('🧪 [Similar Questions] Generating static content')
    await new Promise(resolve => setTimeout(resolve, 1500)) // Simulate delay

    const generatedQuestions = [
      `(testing) question 1 ${categoryLabel}`,
      `(testing) question 2 ${categoryLabel}`,
      `(testing) question 3 ${categoryLabel}`,
    ]

    console.log(
      '📝 [Similar Questions] Generated questions:',
      generatedQuestions
    )

    // Create nodes with deterministic IDs and prepare data structures
    const newNodes: Array<{
      id: string
      drag_tree_id: string
      node_type: 'QUESTION'
      label: string
      metadata: any
      status: DragTreeNodeStatus
    }> = []

    const newQuestionIds: string[] = []

    // Use the provided nodeId directly instead of generating from label
    const parentNodeId = nodeId

    // Get the parent node to determine the level
    const parentNode = await prisma.dragTreeNode.findUnique({
      where: { id: parentNodeId },
    })

    if (!parentNode) {
      console.log('❌ [Similar Questions] Parent category not found')
      return NextResponse.json(
        { error: 'Parent category not found' },
        { status: 404 }
      )
    }

    const parentLevel = (parentNode.metadata as any)?.level || 0
    const newQuestionLevel = parentLevel + 1

    generatedQuestions.forEach(question => {
      // Use deterministic ID generation with timestamp to ensure uniqueness on regeneration
      const timestamp = Date.now()
      const nodeId = generateDragTreeNodeId(
        activeDragTree.id,
        `${question}_gen_${timestamp}`,
        'QUESTION'
      )

      newNodes.push({
        id: nodeId,
        drag_tree_id: activeDragTree.id,
        node_type: 'QUESTION',
        label: question,
        metadata: {
          generatedType: 'similar_questions',
          level: newQuestionLevel,
          parentId: parentNodeId,
        },
        status: DragTreeNodeStatus.ACTIVE,
      })

      newQuestionIds.push(nodeId)
    })

    // Get current tree structure and update it
    const currentTreeStructure = activeDragTree.tree_structure as {
      root_id: string
      hierarchy: Record<string, string[]>
    }

    // Update hierarchy to include new questions under the parent category
    const updatedHierarchy = { ...currentTreeStructure.hierarchy }
    if (!updatedHierarchy[parentNodeId]) {
      updatedHierarchy[parentNodeId] = []
    }
    updatedHierarchy[parentNodeId] = [
      ...updatedHierarchy[parentNodeId],
      ...newQuestionIds,
    ]

    const updatedTreeStructure = {
      root_id: currentTreeStructure.root_id,
      hierarchy: updatedHierarchy,
    }

    console.log(
      `💾 [Similar Questions] Updating database with ${newNodes.length} new nodes`
    )

    // Atomic database update using Promise.all
    await Promise.all([
      // Create new nodes
      prisma.dragTreeNode.createMany({
        data: newNodes,
        skipDuplicates: true,
      }),
      // Update tree structure
      updateDragTree({
        treeId: activeDragTree.id,
        treeStructure: updatedTreeStructure,
      }),
    ])

    console.log('🎉 [Similar Questions] Database update completed successfully')

    // Return simple success response - FE will refresh from database
    return NextResponse.json({
      success: true,
      message: 'Similar questions generated successfully',
      shouldRefresh: true,
      nodeCount: newNodes.length,
    })
  } catch (error) {
    console.error('💥 [Similar Questions] Error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

/* Currently unused function - uncomment when needed
const constructSimilarQuestionsPrompt = (
  categoryLabel: string,
  existingQuestions: string[],
  originalContext: string
): string => {
  const existingQuestionsText =
    existingQuestions.length > 0
      ? existingQuestions.map(q => `- ${q}`).join('\n')
      : 'No existing questions yet.'

  return `As an elite consultant, you are tasked with generating additional insightful questions for a specific category in our project structure.

**Category Focus**: ${categoryLabel}

**Context**: ${originalContext}

**Existing Questions Under This Category**:
${existingQuestionsText}

Please generate 3-5 additional questions that:
1. Are similar in depth and scope to the existing questions
2. Explore different angles or aspects of the same category
3. Maintain the same level of detail and insight
4. Do NOT duplicate existing questions
5. Focus on WHY and WHAT aspects primarily, with some HOW elements

**Format Requirements**:
- Output ONLY the questions, one per line
- Each question should start with "- "
- Do not include any other formatting
- Do not include examples or explanations
- Keep questions concise but insightful

**Example Output Format**:
- What are the key success metrics for measuring this aspect?
- How does this element impact the overall project timeline?
- Why is this particular approach preferred over alternatives?

Generate questions that complement the existing ones while maintaining consistency in style and depth.`
}
*/

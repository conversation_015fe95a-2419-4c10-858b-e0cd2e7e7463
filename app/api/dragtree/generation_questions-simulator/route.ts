import { streamText } from 'ai'
import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/app/api/auth/authOptions'
import { exampleMarkdown } from '@/app/(conv)/dragTree/[dragTreeId]/utils/example'

export const maxDuration = 60

export async function POST(request: NextRequest) {
  try {
    console.log(
      '🚀 [Generation Questions Simulator] Starting streaming generation...'
    )

    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      console.log(
        '❌ [Generation Questions Simulator] Unauthorized - no session'
      )
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { dragTreeId } = body

    if (!dragTreeId) {
      return NextResponse.json(
        { error: 'Drag Tree ID is required' },
        { status: 400 }
      )
    }

    console.log(
      `🔍 [Generation Questions Simulator] Processing tree: ${dragTreeId}`
    )

    // Process and stream the dummy markdown content
    const processedContent = processMarkdown(exampleMarkdown)
    const simulatedText = processedContent.join('\n\n')

    // Use streamText with a mock model for simulation
    const result = await streamText({
      model: {
        provider: 'mock',
        modelId: 'simulation',
        async doStream() {
          return {
            stream: createMockStream(simulatedText),
            rawCall: { rawPrompt: 'mock', rawSettings: {} },
          }
        },
      } as any,
      messages: [{ role: 'user', content: 'Generate content' }],
    })

    return result.toDataStreamResponse()
  } catch (error) {
    console.error(
      '💥 [Generation Questions Simulator] Unexpected error:',
      error
    )
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

function createMockStream(text: string) {
  const words = text.split(' ')
  let index = 0

  return new ReadableStream({
    start(controller) {
      const sendNext = () => {
        if (index >= words.length) {
          controller.close()
          return
        }

        // Send 1-3 words at a time to simulate natural streaming
        const chunkSize = Math.floor(Math.random() * 3) + 1
        const chunk = words.slice(index, index + chunkSize).join(' ') + ' '

        controller.enqueue({
          type: 'text-delta',
          textDelta: chunk,
        })

        index += chunkSize

        // Variable delay to simulate realistic streaming
        setTimeout(sendNext, 30 + Math.random() * 100)
      }

      sendNext()
    },
  })
}

/**
 * Process markdown content by:
 * 1. Filtering out lines starting with "what", "why", "how", "when" (case insensitive)
 * 2. Removing "#" symbols
 * 3. Cleaning up empty lines
 */
function processMarkdown(markdown: string): string[] {
  const lines = markdown.split('\n')
  const filtered: string[] = []

  for (const line of lines) {
    const trimmed = line.trim()

    // Skip empty lines
    if (!trimmed) continue

    // Check if line starts with question words (case insensitive)
    const startsWithQuestionWord = /^(what|why|how|when)\b/i.test(trimmed)
    if (startsWithQuestionWord) continue

    // Remove # symbols and clean up
    const cleaned = trimmed.replace(/^#+\s*/, '').trim()

    // Only add non-empty cleaned lines
    if (cleaned) {
      filtered.push(cleaned)
    }
  }

  return filtered
}

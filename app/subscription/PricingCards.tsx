"use client";

import React from "react";
import { Product, Price } from "@prisma/client";
import { PricingCardsContainer } from "./PricingCardsContainer";
import { useSubscription } from "./hooks/useSubscription";
import { EnvironmentWarning } from "./components/EnvironmentWarning";
import { SubscriptionHeader } from "./components/SubscriptionHeader";
import { ContactInfo } from "./components/ContactInfo";

type PricingCardsProps = {
  products: Product[];
  prices: Price[];
};

export const PricingCards: React.FC<PricingCardsProps> = ({
  products,
  prices,
}) => {
  const { isLocalOrDev, isSubscribed, cardData, handleStripeCheckout } =
    useSubscription(products, prices);

  return (
    <div className="flex flex-col h-screen">
      <div className="flex-grow flex flex-col">
        <EnvironmentWarning isLocalOrDev={isLocalOrDev} />
        <SubscriptionHeader isSubscribed={isSubscribed} />
        <PricingCardsContainer
          cardData={cardData}
          isSubscribed={isSubscribed}
          onSubscribe={handleStripeCheckout}
        />
      </div>
      <ContactInfo />
    </div>
  );
};

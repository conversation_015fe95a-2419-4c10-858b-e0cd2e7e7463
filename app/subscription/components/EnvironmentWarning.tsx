import React from "react";

type EnvironmentWarningProps = {
  isLocalOrDev: boolean;
};

export const EnvironmentWarning: React.FC<EnvironmentWarningProps> = ({
  isLocalOrDev,
}) => {
  if (!isLocalOrDev) return null;

  return (
    <h2 className="text-2xl font-bold text-center text-red-500">
      You are in local/dev environment, DO NOT subscribe w/ real card!
      <a
        href=" https://docs.stripe.com/testing#cards"
        className="text-blue-500 hover:text-blue-600"
      >
        Use Stripe testing cards
      </a>
    </h2>
  );
};

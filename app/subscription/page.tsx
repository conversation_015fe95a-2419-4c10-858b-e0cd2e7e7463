import { fetchProduct, fetchPrice } from '@/app/server-actions'
import getCurrentUser from '@/app/actions/getCurrentUser'
import SubscriptionPageWrapper from './SubscriptionPageWrapper'

// Force dynamic rendering to avoid database calls during static generation
export const dynamic = 'force-dynamic'

const SubscriptionPage = async () => {
  const [currentUser, products, prices] = await Promise.all([
    getCurrentUser(),
    fetchProduct(),
    fetchPrice(),
  ])

  return (
    <SubscriptionPageWrapper
      currentUser={currentUser}
      products={products}
      prices={prices}
    />
  )
}

export default SubscriptionPage

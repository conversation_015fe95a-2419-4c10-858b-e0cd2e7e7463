"use client";

import { User, Product, Price } from "@prisma/client";
import { UserProvider } from "@/app/context/UserContext";
import { PricingCards } from "./PricingCards";

type SubscriptionPageWrapperProps = {
  currentUser: User | null;
  products: Product[];
  prices: Price[];
};

// This component serves as a bridge between server-side data fetching and client-side rendering
const SubscriptionPageWrapper: React.FC<SubscriptionPageWrapperProps> = ({
  currentUser,
  products,
  prices,
}) => {
  return (
    // Provide the user context to child components
    <UserProvider value={currentUser}>
      {/* Render the PricingCards component with the fetched data */}
      <PricingCards products={products} prices={prices} />
    </UserProvider>
  );
};

export default SubscriptionPageWrapper;

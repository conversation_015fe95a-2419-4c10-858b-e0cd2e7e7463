import prisma from "@/app/libs/prismadb";
import getCurrentUser from "./getCurrentUser";
import getIssueTreeById from "./getIssueTreeById";

const getDraftById = async (conversationId: string) => {
  try {
    const currentUser = await getCurrentUser();
    const currentIssueTree = await getIssueTreeById(
      conversationId,
      currentUser
    );

    if (!currentUser?.email) {
      return null;
    }

    const draft = await prisma.draft.findFirst({
      where: {
        conversation_id: conversationId,
      },
      orderBy: {
        created_at: "desc",
      },
    });
    console.log("currentIssueTree", currentIssueTree);
    if (draft?.exact_prompt && currentIssueTree?.summary_context) {
      draft.exact_prompt = currentIssueTree?.summary_context; // Replace with summary context
      return draft;
    }
    if (draft?.exact_prompt) {
      draft.exact_prompt = ""; // Remove the exact_prompt property
      return draft;
    }
    return null;
  } catch (error: any) {
    console.log(error, "SERVER_ERROR");
    return null;
  }
};

export default getDraftById;

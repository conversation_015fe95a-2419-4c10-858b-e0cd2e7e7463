import prisma from "@/app/libs/prismadb";

const getIssueTreeById = async (conversationId: string, currentUser: any) => {
  try {
    if (!currentUser?.email) {
      return null;
    }
    // Get the earliest issue tree for the conversation
    // Previously for one converation we may have multiple issue trees, but now we only have one
    // For backward compatibility, we will use the earliest one since follow-up issue tree may not be compatible with the latest code
    // But our code works for the first issue tree
    const issueTree = await prisma.issueTree.findFirst({
      where: {
        conversation_id: conversationId,
      },
      orderBy: {
        created_at: "asc",
      },
    });

    return issueTree;
  } catch (error: any) {
    console.log(error, "SERVER_ERROR");
    return null;
  }
};

export default getIssueTreeById;

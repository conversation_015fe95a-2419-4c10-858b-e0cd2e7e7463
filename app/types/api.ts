import { NodeMapping } from '@/app/(conv)/conversations/[conversationId]/issue_tree/SubtreeDialog/utils'
import { SupportedLanguageCode } from '@/app/(conv)/screening/constants/languages'

// request to api/rag/search
export type ragSearchRequestType = {
  query: string
  conversationId: string
  currentUser: {
    id: string
  }
  issueTreeId: string
  selectedNodeId: string
}

// request to api/rag/save_response
export type ragSaveRequestType = {
  issueTreeId: string
  currentUser: {
    id: string
  }
  conversationId: string
  selectedNodeId: string
  generationInput: string
  generationOutput: string
}

// request to api/rag/generate_response
export type ragGenerateRequestType = {
  currentUser: {
    id: string
  }
  context: string
  conversationId: string
  issueTreeId: string
  selectedNodeId: string
  originalAsk: string
  nodeQuestion: string
}

// request to api/rag/assistant
export type ragAssistantRequestType = {
  userId: string
  conversationId: string
  issueTreeId: string
  selectedNodeId: string
  originalAsk: string
  nodeQuestion: string
}

// request to api/subtree
export type SubtreeRequestType = {
  conversationId: string
  userId: string
  issueTreeId: string
  selectedNodeId: string
  prompt: string
  generation_output: string
}

// request to api/subtree/generate_questions
export type SubtreeGenerateQuestionsRequestType = {
  issueTreeId: string
  conversationId: string
  userId: string
  markdown: string
  originalAskText: string
  nodeMapping: NodeMapping
  id: string
  childs: string[]
  customDirective: string
}

// request to api/rephrase
export type RephraseRequestType = {
  userId: string
  description: string
}

// request to api/notebook/generate_notebook
export type generateNotebookRequestType = {
  userId: string
  conversationId: string
  notebookId: string
}

export type DragtreeGenerateQuestionsRequestType = {
  conversationId: string
  userId: string
}

export type DragtreeGenerateSimilarQuestionsRequestType = {
  conversationId: string
  userId: string
  nodeId: string
  categoryLabel: string
  existingQuestions: string[]
  originalContext: string
}

export type DragtreeGenerateSimilarCategoriesRequestType = {
  conversationId: string
  userId: string
  nodeId: string
  parentCategoryLabel: string
  existingSubcategories: string[]
  originalContext: string
  treeContext: string
}

// request to api/screening/diagnose
export type ScreeningDiagnoseRequestType = {
  userId: string
  description: string
  preferredLanguage?: SupportedLanguageCode
}

// request to api/screening/rephrase
export type ScreeningRephraseRequestType = {
  userId: string
  description: string
  preferredLanguage?: SupportedLanguageCode
}

// request to api/dragtree/research_generate
export type ResearchGenerateRequestType = {
  contentId: string
  questionText: string
  researchType?: string
}

import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/app/api/auth/authOptions'
import { NextRequest, NextResponse } from 'next/server'
import prisma from '@/app/libs/prismadb'
import { UserStatus, DragTreeStatus } from '@prisma/client'
import { z } from 'zod'

// Common validation schemas
export const userIdSchema = z.string().min(1, 'userId is required')
export const dragTreeIdSchema = z.string().min(1, 'dragTreeId is required')
export const nodeIdSchema = z.string().min(1, 'nodeId is required')

// Authentication validation result type
export type AuthValidationResult =
  | {
      success: true
      userId: string
      session: any
    }
  | {
      success: false
      error: NextResponse
    }

// Resource ownership validation result type
export type OwnershipValidationResult<T = any> =
  | {
      success: true
      resource: T
    }
  | {
      success: false
      error: NextResponse
    }

/**
 * Validates user authentication and returns user session
 * Common pattern across all API endpoints
 */
export async function validateAuthentication(): Promise<AuthValidationResult> {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return {
        success: false,
        error: NextResponse.json({ error: 'Unauthorized' }, { status: 401 }),
      }
    }

    // Check if user is active (enhanced validation pattern)
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { status: true },
    })

    if (!user || user.status !== UserStatus.ACTIVE) {
      return {
        success: false,
        error: NextResponse.json(
          { error: 'User account is not active' },
          { status: 403 }
        ),
      }
    }

    return {
      success: true,
      userId: session.user.id,
      session,
    }
  } catch (error) {
    console.error('🔐 Authentication validation error:', error)
    return {
      success: false,
      error: NextResponse.json(
        { error: 'Authentication failed' },
        { status: 500 }
      ),
    }
  }
}

/**
 * Validates request body against Zod schema
 * Returns parsed data or error response
 */
export async function validateRequestBody<T>(
  request: NextRequest,
  schema: z.ZodSchema<T>
): Promise<
  { success: true; data: T } | { success: false; error: NextResponse }
> {
  try {
    const body = await request.json()
    const parseResult = schema.safeParse(body)

    if (!parseResult.success) {
      return {
        success: false,
        error: NextResponse.json(
          { error: parseResult.error.flatten() },
          { status: 400 }
        ),
      }
    }

    return {
      success: true,
      data: parseResult.data,
    }
  } catch (error) {
    console.error('📝 Request body validation error:', error)
    return {
      success: false,
      error: NextResponse.json(
        { error: 'Invalid JSON in request body' },
        { status: 400 }
      ),
    }
  }
}

/**
 * Validates URL search parameters against Zod schema
 */
export function validateSearchParams<T>(
  searchParams: URLSearchParams,
  schema: z.ZodSchema<T>
): { success: true; data: T } | { success: false; error: NextResponse } {
  try {
    const params = Object.fromEntries(searchParams.entries())
    const parseResult = schema.safeParse(params)

    if (!parseResult.success) {
      return {
        success: false,
        error: NextResponse.json(
          { error: parseResult.error.flatten() },
          { status: 400 }
        ),
      }
    }

    return {
      success: true,
      data: parseResult.data,
    }
  } catch (error) {
    console.error('🔍 Search params validation error:', error)
    return {
      success: false,
      error: NextResponse.json(
        { error: 'Invalid search parameters' },
        { status: 400 }
      ),
    }
  }
}

/**
 * Validates drag tree ownership by user
 * Ensures user can only access their own drag trees
 */
export async function validateDragTreeOwnership(
  userId: string,
  dragTreeId: string
): Promise<OwnershipValidationResult> {
  try {
    const dragTree = await prisma.dragTree.findUnique({
      where: { id: dragTreeId },
      include: {
        user: {
          select: { id: true, status: true },
        },
      },
    })

    if (!dragTree) {
      return {
        success: false,
        error: NextResponse.json(
          { error: 'Drag tree not found' },
          { status: 404 }
        ),
      }
    }

    if (dragTree.user_id !== userId) {
      return {
        success: false,
        error: NextResponse.json(
          { error: "Access denied: You don't own this drag tree" },
          { status: 403 }
        ),
      }
    }

    return {
      success: true,
      resource: dragTree,
    }
  } catch (error) {
    console.error('🌳 Drag tree ownership validation error:', error)
    return {
      success: false,
      error: NextResponse.json(
        { error: 'Failed to validate drag tree ownership' },
        { status: 500 }
      ),
    }
  }
}

/**
 * Validates drag tree node ownership by user (through drag tree)
 */
export async function validateDragTreeNodeOwnership(
  userId: string,
  nodeId: string
): Promise<OwnershipValidationResult> {
  try {
    const node = await prisma.dragTreeNode.findUnique({
      where: { id: nodeId },
      include: {
        drag_tree: {
          select: { id: true, user_id: true },
        },
      },
    })

    if (!node) {
      return {
        success: false,
        error: NextResponse.json(
          { error: 'Drag tree node not found' },
          { status: 404 }
        ),
      }
    }

    if (node.drag_tree.user_id !== userId) {
      return {
        success: false,
        error: NextResponse.json(
          { error: "Access denied: You don't own this drag tree node" },
          { status: 403 }
        ),
      }
    }

    return {
      success: true,
      resource: node,
    }
  } catch (error) {
    console.error('🍃 Drag tree node ownership validation error:', error)
    return {
      success: false,
      error: NextResponse.json(
        { error: 'Failed to validate drag tree node ownership' },
        { status: 500 }
      ),
    }
  }
}

/**
 * Validates drag tree status for operations
 * Prevents race conditions and invalid state transitions
 */
export async function validateDragTreeStatus(
  dragTreeId: string,
  requiredStatus: DragTreeStatus | DragTreeStatus[]
): Promise<OwnershipValidationResult> {
  try {
    const dragTree = await prisma.dragTree.findUnique({
      where: { id: dragTreeId },
      select: { id: true, status: true, title: true },
    })

    if (!dragTree) {
      return {
        success: false,
        error: NextResponse.json(
          { error: 'Drag tree not found' },
          { status: 404 }
        ),
      }
    }

    const allowedStatuses = Array.isArray(requiredStatus)
      ? requiredStatus
      : [requiredStatus]

    if (!allowedStatuses.includes(dragTree.status)) {
      return {
        success: false,
        error: NextResponse.json(
          {
            error: `Invalid drag tree status. Expected: ${allowedStatuses.join(' or ')}, got: ${dragTree.status}`,
          },
          { status: 409 }
        ),
      }
    }

    return {
      success: true,
      resource: dragTree,
    }
  } catch (error) {
    console.error('📊 Drag tree status validation error:', error)
    return {
      success: false,
      error: NextResponse.json(
        { error: 'Failed to validate drag tree status' },
        { status: 500 }
      ),
    }
  }
}

/**
 * Combined validation for drag tree operations
 * Validates authentication, ownership, and status in one call
 */
export async function validateDragTreeOperation(
  dragTreeId: string,
  requiredStatus?: DragTreeStatus | DragTreeStatus[]
): Promise<
  | {
      success: true
      userId: string
      session: any
      dragTree: any
    }
  | {
      success: false
      error: NextResponse
    }
> {
  // Validate authentication
  const authResult = await validateAuthentication()
  if (!authResult.success) {
    return authResult
  }

  // Validate ownership
  const ownershipResult = await validateDragTreeOwnership(
    authResult.userId,
    dragTreeId
  )
  if (!ownershipResult.success) {
    return ownershipResult
  }

  // Validate status if required
  if (requiredStatus) {
    const statusResult = await validateDragTreeStatus(
      dragTreeId,
      requiredStatus
    )
    if (!statusResult.success) {
      return statusResult
    }
  }

  return {
    success: true,
    userId: authResult.userId,
    session: authResult.session,
    dragTree: ownershipResult.resource,
  }
}

/**
 * Standard error response helper
 */
export function createErrorResponse(
  message: string,
  status: number
): NextResponse {
  return NextResponse.json({ error: message }, { status })
}

/**
 * Standard success response helper
 */
export function createSuccessResponse<T>(
  data: T,
  status: number = 200
): NextResponse {
  return NextResponse.json({ success: true, data }, { status })
}

/**
 * Extract all node IDs referenced in the database tree_structure JSON
 * The JSON has the form:
 * {
 *   root_id: string,
 *   hierarchy: Record<string, string[]>
 * }
 */
export function extractIdsFromTreeStructure(treeStructure: any): Set<string> {
  if (!treeStructure || typeof treeStructure !== 'object') return new Set()

  const { root_id, hierarchy } = treeStructure as {
    root_id: string
    hierarchy: Record<string, string[]>
  }

  if (!root_id || !hierarchy) return new Set()

  const ids = new Set<string>()
  const stack: string[] = [root_id]

  while (stack.length) {
    const current = stack.pop() as string
    if (!ids.has(current)) {
      ids.add(current)
      const children = hierarchy[current] || []
      children.forEach(child => stack.push(child))
    }
  }

  return ids
}

/**
 * Validate that the set of IDs referenced by tree_structure exactly matches
 * the set of ACTIVE node IDs.
 * Used both on the front-end (after mutations) and in Prisma middleware.
 */
export function validateTreeAlignmentRaw(
  treeStructure: any,
  activeIds: Set<string>
): boolean {
  const idsFromTree = extractIdsFromTreeStructure(treeStructure)

  if (idsFromTree.size !== activeIds.size) return false
  for (const id of Array.from(idsFromTree)) {
    if (!activeIds.has(id)) return false
  }
  return true
}

export type CachedMessage = {
  id: string
  role: 'user' | 'assistant' | 'system'
  content: string
  createdAt: Date | string
}

const CACHE_TTL_MS = 60 * 60 * 1000 // 1 hour

const cache = new Map<
  string,
  { messages: CachedMessage[]; expiresAt: number }
>()

const MAX_ENTRIES = 1000

export function getCachedMessages(
  conversationId: string
): CachedMessage[] | null {
  const entry = cache.get(conversationId)
  if (!entry) return null
  if (Date.now() > entry.expiresAt) {
    cache.delete(conversationId)
    return null
  }
  return entry.messages
}

export function setCachedMessages(
  conversationId: string,
  messages: CachedMessage[]
) {
  cache.set(conversationId, {
    messages,
    expiresAt: Date.now() + CACHE_TTL_MS,
  })

  // Simple prune strategy: if we exceed MAX_ENTRIES remove the oldest
  if (cache.size > MAX_ENTRIES) {
    const oldestKey = Array.from(cache.entries()).sort(
      (a, b) => a[1].expiresAt - b[1].expiresAt
    )[0][0]
    cache.delete(oldestKey)
  }
}

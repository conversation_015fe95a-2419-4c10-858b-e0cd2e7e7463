// Optional: configure or set up a testing framework before each test.
// If you delete this file, remove `setupFilesAfterEnv` from `jest.config.js`

import '@testing-library/jest-dom'

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(() => ({
    push: jest.fn(),
    replace: jest.fn(),
    prefetch: jest.fn(),
    back: jest.fn(),
    forward: jest.fn(),
    refresh: jest.fn(),
  })),
  useSearchParams: jest.fn(() => new URLSearchParams()),
  usePathname: jest.fn(() => '/'),
}))

// Mock IntersectionObserver
global.IntersectionObserver = class IntersectionObserver {
  constructor() {}
  observe() {
    return null
  }
  disconnect() {
    return null
  }
  unobserve() {
    return null
  }
}

// Mock ResizeObserver
global.ResizeObserver = class ResizeObserver {
  constructor() {}
  observe() {
    return null
  }
  disconnect() {
    return null
  }
  unobserve() {
    return null
  }
}

// Mock TextEncoder/TextDecoder for Node.js environment
if (typeof global.TextEncoder === 'undefined') {
  global.TextEncoder = require('util').TextEncoder
}
if (typeof global.TextDecoder === 'undefined') {
  global.TextDecoder = require('util').TextDecoder
}

if (typeof global.structuredClone === 'undefined') {
  global.structuredClone = val => JSON.parse(JSON.stringify(val))
}

// Mock Request/Response for Next.js server components
global.Request = global.Request || class Request {}
global.Response = global.Response || class Response {}
global.Headers = global.Headers || class Headers {}
global.ReadableStream =
  global.ReadableStream ||
  class ReadableStream {
    constructor() {}
  }

// Mock crypto-js with different hashes for different inputs
jest.mock('crypto-js', () => ({
  SHA256: jest.fn(input => {
    // Simple hash simulation - different inputs get different hashes
    // Create a simple hash based on input string
    let hash = 0
    for (let i = 0; i < input.length; i++) {
      const char = input.charCodeAt(i)
      hash = (hash << 5) - hash + char
      hash = hash & hash // Convert to 32bit integer
    }

    // Convert to hex-like string
    const hexHash = Math.abs(hash).toString(16).padStart(16, '0')
    const fullHash = hexHash + hexHash // Double for longer hash

    return {
      toString: jest.fn(() => fullHash),
    }
  }),
  enc: {
    Hex: 'hex',
  },
}))

// Mock nanoid with unique counter
let nanoidCounter = 0
jest.mock('nanoid', () => ({
  nanoid: jest.fn(() => `mockNanoId${nanoidCounter++}`),
  customAlphabet: jest.fn(() => {
    let counter = 0
    return jest.fn(() => `mockCustomId${counter++}`)
  }),
}))

// Mock unified and remark
jest.mock('unified', () => ({
  unified: jest.fn(() => ({
    use: jest.fn(() => ({
      parse: jest.fn(() => ({
        children: [],
      })),
    })),
  })),
}))

jest.mock('remark-parse', () => jest.fn())

// Mock @tiptap/html to avoid zeed-dom ES module issues
jest.mock('@tiptap/html', () => ({
  generateJSON: jest.fn((html, extensions) => ({
    type: 'doc',
    content: [
      {
        type: 'paragraph',
        content: [{ type: 'text', text: html }],
      },
    ],
  })),
}))

// Mock highlight.js and lowlight to avoid ES module issues
jest.mock('highlight.js', () => ({}))
jest.mock('lowlight', () => ({
  lowlight: {
    register: jest.fn(),
    highlight: jest.fn(() => ({ value: '' })),
  },
}))

// Mock tiptap extensions that use ES modules
jest.mock('@tiptap/extension-code-block-lowlight', () => jest.fn())
jest.mock('@tiptap/extension-highlight', () => jest.fn())

// Mock mermaid to avoid ES module issues
jest.mock('mermaid', () => ({
  default: {
    initialize: jest.fn(),
    render: jest.fn(),
  },
}))

// Mock the entire editor extensions to avoid complex ES module chains
jest.mock('@/app/components/editor/extensions', () => ({
  TiptapExtensions: [],
}))

// Mock editor utils to avoid import issues
jest.mock('@/app/components/editor/utils', () => ({
  getInitialContent: jest.fn(() => ({ type: 'doc', content: [] })),
  processMessageContent: jest.fn(() => ({ type: 'doc', content: [] })),
  replaceMermaidTags: jest.fn(html => html),
}))

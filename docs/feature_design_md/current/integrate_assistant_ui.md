# Design Doc (Unified): Re-implement Chat UI with `assistant-ui`

**Date:** 2025-07-18
**Author(s):** Gemini, o3

---

## 1 · Problem Statement

Our bespoke chat component (`ChatTabContent.tsx` + `useAiConversation.ts`) has repeatedly failed in production:

- Complex flex / tab layout collapses to zero-height, hiding messages.
- Rendering logic is brittle (SYSTEM messages, role casing, pagination).
- Maintenance cost is disproportionate to business value.

---

## 2 · Solution Overview

Adopt [`assistant-ui`](https://github.com/assistant-ui) – an open-source Tailwind-based React kit purpose-built for AI chat threads.

We **will not modify or delete** any existing chat code during migration.
Instead we will create a parallel implementation; once verified, we can safely remove legacy files.

---

## 3 · Why `assistant-ui` Fits

| Requirement                 | Current State                                        | `assistant-ui` Capability                                                                 |
| --------------------------- | ---------------------------------------------------- | ----------------------------------------------------------------------------------------- |
| Streamed text chunks        | Vercel AI SDK already streams `res.body`             | Accepts async iterator via `run()`                                                        |
| Historical messages         | `/api/aipane/conversations/[id]`                     | Pass as `initialMessages` prop                                                            |
| Tool calls (search, think)  | Stored as assistant “steps” and rendered in timeline | Library surfaces tool messages; we can inject custom `renderMessage` to keep our timeline |
| Markdown / TipTap rich text | Markdown stored, sometimes TipTap JSON               | Renders markdown by default; custom renderer can switch to TipTap viewer                  |
| Pagination (load older)     | Cursor-based API                                     | We can wrap `<Thread />` in our existing `InfiniteScrollContainer`                        |
| Tailwind design system      | Project already Tailwind                             | Native support                                                                            |
| Bundle size                 | -                                                    | +≈14 KB gzipped (acceptable)                                                              |

No backend change is required; the API contract matches our current `/api/aipane/chat` endpoint.

---

## 4 · High-Level Architecture

```
TabContainer
└─ ChatTabContent (header / footer remains)
   └─ AssistantUIWrapper          ← new component
      ├─ useAssistant() hook (assistant-ui)
      ├─ Thread   (message list + auto-scroll)
      └─ UserInput (composer, Enter handling)
```

- `AssistantUIWrapper` is fully self-contained.
- Legacy `useAiConversation` stays untouched until replacement is stable.
- Asset creation/update logic hooks into `onMessageFinish` exactly as today.

---

## 5 · Implementation Details

### 5.1 Installation

### 5.2 Create `AssistantUIWrapper.tsx`

```tsx
'use client'

import {
  useAssistant,
  Thread,
  UserInput,
  ToolCallMessage,
} from 'assistant-ui/react'

type Props = {
  conversationId: string
  initialMessages: { role: string; content: string }[]
}

export default function AssistantUIWrapper({
  conversationId,
  initialMessages,
}: Props) {
  const { status, messages, input, setInput, submitMessage, appendHistory } =
    useAssistant({
      conversationId,
      initialMessages,
      async run(messages) {
        // proxy to existing backend
        const res = await fetch('/api/aipane/chat', {
          method: 'POST',
          body: JSON.stringify({ messages, conversationId }),
        })
        return res.body! // assistant-ui consumes the stream
      },
    })

  /* optional:  load-older pagination using appendHistory() */

  return (
    <div className="flex flex-col h-full">
      <Thread
        className="flex-1 min-h-0"
        messages={messages}
        renderMessage={m =>
          m instanceof ToolCallMessage ? (
            /* custom timeline component */
            <ReasoningTimeline steps={m.toolCalls} />
          ) : undefined
        }
      />
      <UserInput
        value={input}
        onChange={e => setInput(e.target.value)}
        onSubmit={submitMessage}
        placeholder="Message AI Assistant…"
      />
    </div>
  )
}
```

### 5.3 Wire into `ChatTabContent.tsx`

1. Import the wrapper.
2. Replace legacy message list + composer with:

```tsx
<AssistantUIWrapper
  conversationId={tab.aiPaneData?.conversationId!}
  initialMessages={assetMessagesOrPrefetch}
/>
```

3. Keep the existing header (title, context dialog) and asset-sidebar hooks.

### 5.4 Custom Rendering for Tool Calls

- Use `renderMessage` prop to detect tool-call messages and show our current “ReasoningTimeline”.
- No change to backend step collector.

### 5.5 Pagination (Optional Phase 2)

Wrap `<Thread />` in our existing `InfiniteScrollContainer`; on `onTopReach` call `/api/aipane/conversations/[id]?cursor=` then `appendHistory()`.

---

## 6 · Phased Roll-Out Plan

| Phase                | Goal                                    | Key Tasks                                                                     | Success Criteria                               |
| -------------------- | --------------------------------------- | ----------------------------------------------------------------------------- | ---------------------------------------------- |
| **0 – Setup**        | Install lib, scaffold wrapper           | `npm i assistant-ui`; push `AssistantUIWrapper.tsx` with hard-coded demo data | Wrapper renders inside a blank page            |
| **1 – Embed**        | Replace legacy body behind feature flag | Import wrapper into ChatTabContent; pass real conversationId/messages         | Messages display, new prompt streams correctly |
| **2 – Tool call UX** | Parity with current timeline            | Implement custom `renderMessage` for tool calls                               | “Thinking / search” timeline visible           |
| **3 – Pagination**   | Scroll to load older history            | `onTopReach` → fetch older → `appendHistory()`                                | Older messages appear, no glitches             |
| **4 – QA & Toggle**  | Stabilise                               | Cross-browser + mobile tests; performance                                     | Feature flag enabled for all users             |
| **5 – Cleanup**      | Remove dead code                        | Delete `useAiConversation.ts`, old JSX, and unused CSS                        | Build passes with no unused exports            |

**Important:**
_Do not delete or modify existing chat code until Phase 4 passes QA._ The old implementation stays in the repository behind a flag, enabling instant rollback.

---

## 7 · Conclusion

`assistant-ui` aligns perfectly with our backend contract, eliminates our chronic layout/render bugs, and reduces maintenance. The phased plan lets us integrate safely without touching legacy code until the new path is proven.

> Proceed with Phase 0–1 implementation under a feature flag.

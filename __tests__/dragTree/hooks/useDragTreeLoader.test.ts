/**
 * useDragTreeLoader Race Condition Fix Tests
 *
 * Tests that the loader correctly handles rapid navigation between trees
 * and prevents mixed state by ignoring stale responses.
 */

import { useDragTreeStore } from '@/app/stores/dragtree_store'

// Mock the store to test interactions
jest.mock('@/app/stores/dragtree_store', () => ({
  useDragTreeStore: jest.fn(() => ({
    setFrontendTreeStructure: jest.fn(),
    setScreeningQuestion: jest.fn(),
    setDragTreeId: jest.fn(),
    setDragTreeTitle: jest.fn(),
    resetDragTreeData: jest.fn(),
  })),
}))

// Mock the server action
jest.mock('@/app/server-actions/drag-tree', () => ({
  getDragTree: jest.fn(),
}))

// Mock tutorial utils
jest.mock('@/app/(conv)/dragTree/[dragTreeId]/utils/tutorial-utils', () => ({
  checkTutorialStatusFromMetadata: jest.fn(() => ({
    success: true,
    data: { isCompleted: false, isSkipped: false, shouldShow: true },
  })),
}))

describe('useDragTreeLoader Race Condition Fix', () => {
  describe('Store Reset on dragTreeId Change', () => {
    it('should reset store immediately when dragTreeId changes', () => {
      const mockResetDragTreeData = jest.fn()
      const mockStore = {
        setFrontendTreeStructure: jest.fn(),
        setScreeningQuestion: jest.fn(),
        setDragTreeId: jest.fn(),
        setDragTreeTitle: jest.fn(),
        resetDragTreeData: mockResetDragTreeData,
      }

      ;(useDragTreeStore as unknown as jest.Mock).mockReturnValue(mockStore)

      // Simulate the effect logic that runs when dragTreeId changes
      const simulateRouteChange = (
        prevDragTreeId: string | null,
        newDragTreeId: string | null
      ) => {
        if (newDragTreeId && newDragTreeId !== prevDragTreeId) {
          mockResetDragTreeData()
        }
      }

      // Test navigation from null to tree A
      simulateRouteChange(null, 'tree-a')
      expect(mockResetDragTreeData).toHaveBeenCalledTimes(1)

      // Test navigation from tree A to tree B
      simulateRouteChange('tree-a', 'tree-b')
      expect(mockResetDragTreeData).toHaveBeenCalledTimes(2)

      // Test staying on same tree (should not reset)
      simulateRouteChange('tree-b', 'tree-b')
      expect(mockResetDragTreeData).toHaveBeenCalledTimes(2)
    })
  })

  describe('Request ID Generation and Validation', () => {
    it('should generate unique request IDs with correct format', () => {
      const generateRequestId = (dragTreeId: string) => {
        return `${dragTreeId}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
      }

      const id1 = generateRequestId('tree-a')
      const id2 = generateRequestId('tree-a')

      expect(id1).toMatch(/^tree-a-\d+-[a-z0-9]+$/)
      expect(id2).toMatch(/^tree-a-\d+-[a-z0-9]+$/)
      expect(id1).not.toBe(id2) // Should be unique
    })

    it('should correctly identify stale vs current requests', () => {
      let currentRequestId = ''

      const simulateAsyncRequest = (dragTreeId: string, delay: number = 0) => {
        const requestId = `${dragTreeId}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
        currentRequestId = requestId

        return new Promise<{ requestId: string; isStale: boolean }>(resolve => {
          setTimeout(() => {
            const isStale = currentRequestId !== requestId
            resolve({ requestId, isStale })
          }, delay)
        })
      }

      return Promise.all([
        simulateAsyncRequest('tree-a', 100), // This will be stale
        simulateAsyncRequest('tree-b', 50), // This will be current
      ]).then(([resultA, resultB]) => {
        expect(resultA.isStale).toBe(true) // First request should be stale
        expect(resultB.isStale).toBe(false) // Second request should be current
      })
    })
  })

  describe('Race Condition Prevention Logic', () => {
    it('should simulate the loader logic preventing stale updates', () => {
      const mockStore = {
        setFrontendTreeStructure: jest.fn(),
        setScreeningQuestion: jest.fn(),
        setDragTreeId: jest.fn(),
        setDragTreeTitle: jest.fn(),
        resetDragTreeData: jest.fn(),
      }

      let currentRequestId = ''
      const processedResponses: string[] = []

      // Simulate the actual loader logic
      const simulateLoadRequest = (
        dragTreeId: string,
        responseDelay: number
      ) => {
        const requestId = `${dragTreeId}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
        currentRequestId = requestId

        // Simulate async response
        return new Promise<void>(resolve => {
          setTimeout(() => {
            // Check if this is still the current request (stale check)
            if (currentRequestId === requestId) {
              // Process response - only if not stale
              processedResponses.push(dragTreeId)
              mockStore.setFrontendTreeStructure({ id: dragTreeId })
            }
            resolve()
          }, responseDelay)
        })
      }

      return Promise.all([
        simulateLoadRequest('tree-a', 100), // Slower response
        simulateLoadRequest('tree-b', 50), // Faster response
      ]).then(() => {
        // Only tree-b should have been processed (it was faster and became current)
        expect(processedResponses).toEqual(['tree-b'])
        expect(mockStore.setFrontendTreeStructure).toHaveBeenCalledTimes(1)
        expect(mockStore.setFrontendTreeStructure).toHaveBeenCalledWith({
          id: 'tree-b',
        })
      })
    })
  })

  describe('Store Reset Functionality', () => {
    it('should reset all state fields when resetDragTreeData is called', () => {
      // Test the resetDragTreeData logic
      const initialState = {
        frontendTreeStructure: { id: 'tree-a', nodes: [] },
        screeningQuestion: 'Previous question?',
        dragTreeTitle: 'Previous Title',
        preferredLanguage: 'en',
        nodeMap: new Map([['node1', {}]]),
        pendingDatabaseOperations: new Set(['op1']),
        nodeContent: new Map([['node1', new Map()]]),
      }

      const expectedResetState = {
        frontendTreeStructure: null,
        screeningQuestion: '',
        dragTreeTitle: null,
        preferredLanguage: null,
        nodeMap: new Map(),
        pendingDatabaseOperations: new Set(),
        nodeContent: new Map(),
      }

      // Simulate the reset logic
      const resetState = {
        frontendTreeStructure: null,
        screeningQuestion: '',
        dragTreeTitle: null,
        preferredLanguage: null,
        nodeMap: new Map(),
        pendingDatabaseOperations: new Set(),
        nodeContent: new Map(),
      }

      expect(resetState.frontendTreeStructure).toBe(null)
      expect(resetState.screeningQuestion).toBe('')
      expect(resetState.dragTreeTitle).toBe(null)
      expect(resetState.preferredLanguage).toBe(null)
      expect(resetState.nodeMap.size).toBe(0)
      expect(resetState.pendingDatabaseOperations.size).toBe(0)
      expect(resetState.nodeContent.size).toBe(0)
    })
  })

  describe('Integration Scenarios', () => {
    it('should handle rapid navigation A→B→A correctly', () => {
      const mockStore = {
        setFrontendTreeStructure: jest.fn(),
        setScreeningQuestion: jest.fn(),
        setDragTreeId: jest.fn(),
        setDragTreeTitle: jest.fn(),
        resetDragTreeData: jest.fn(),
      }

      let currentRequestId = ''
      const resetCalls: string[] = []
      const processedTrees: string[] = []

      const simulateRapidNavigation = () => {
        // Navigate to A
        resetCalls.push('reset-for-A')
        mockStore.resetDragTreeData()
        const requestA = 'tree-a-' + Date.now()
        currentRequestId = requestA

        // Navigate to B quickly
        setTimeout(() => {
          resetCalls.push('reset-for-B')
          mockStore.resetDragTreeData()
          const requestB = 'tree-b-' + Date.now()
          currentRequestId = requestB

          // Navigate back to A very quickly
          setTimeout(() => {
            resetCalls.push('reset-for-A-again')
            mockStore.resetDragTreeData()
            const requestA2 = 'tree-a-' + Date.now()
            currentRequestId = requestA2

            // Simulate responses arriving out of order
            setTimeout(() => {
              // Response B arrives first
              if (currentRequestId === requestB) {
                processedTrees.push('tree-b')
              }
            }, 10)

            setTimeout(() => {
              // Response A2 arrives second (should be processed)
              if (currentRequestId === requestA2) {
                processedTrees.push('tree-a-final')
              }
            }, 20)

            setTimeout(() => {
              // Original response A arrives last (should be ignored)
              if (currentRequestId === requestA) {
                processedTrees.push('tree-a-stale')
              }
            }, 30)
          }, 5)
        }, 5)

        return new Promise<void>(resolve => {
          setTimeout(() => {
            resolve()
          }, 50)
        })
      }

      return simulateRapidNavigation().then(() => {
        expect(resetCalls).toEqual([
          'reset-for-A',
          'reset-for-B',
          'reset-for-A-again',
        ])
        expect(processedTrees).toEqual(['tree-a-final']) // Only the final A should be processed
        expect(mockStore.resetDragTreeData).toHaveBeenCalledTimes(3)
      })
    })
  })
})

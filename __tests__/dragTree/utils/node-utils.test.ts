import {
  createNodeMetadata,
  createNewTreeNode,
  createNodesFromText,
} from '@/app/stores/dragtree_store/utils/node-utils'
import { TreeNodeType } from '@/app/(conv)/dragTree/[dragTreeId]/constants/node-types'
import { generateDragTreeNodeId } from '@/app/(conv)/dragTree/[dragTreeId]/utils/id-generation'

// Mock the ID generation function
jest.mock('@/app/(conv)/dragTree/[dragTreeId]/utils/id-generation', () => ({
  generateDragTreeNodeId: jest.fn(
    (dragTreeId, label, type) =>
      `${type.toLowerCase()}-${label.replace(/\s/g, '_')}`
  ),
}))

describe('Node Utils', () => {
  describe('createNodeMetadata', () => {
    it('should create metadata with parentId, level, and createdAt', () => {
      const metadata = createNodeMetadata('parent1', 2)
      expect(metadata.parentId).toBe('parent1')
      expect(metadata.level).toBe(2)
      expect(metadata.createdAt).toBeDefined()
    })
  })

  describe('createNewTreeNode', () => {
    it('should create a question node with the correct structure', () => {
      const node = createNewTreeNode(
        'tree1',
        TreeNodeType.QUESTION,
        'What is your name?'
      )
      expect(node.type).toBe(TreeNodeType.QUESTION)
      expect(node.label).toBe('What is your name?')
      expect(node.id).toBe('question-What_is_your_name?')
      expect(node.children.length).toBe(0)
    })

    it('should create a category node with a default child question', () => {
      const node = createNewTreeNode(
        'tree1',
        TreeNodeType.CATEGORY,
        'User Profile'
      )
      expect(node.type).toBe(TreeNodeType.CATEGORY)
      expect(node.label).toBe('User Profile')
      expect(node.id).toBe('category-User_Profile')
      expect(node.children.length).toBe(1)
      expect(node.children[0].type).toBe(TreeNodeType.QUESTION)
      expect(node.children[0].label).toBe('New Question')
    })

    it('should use default labels if none are provided', () => {
      const questionNode = createNewTreeNode('tree1', TreeNodeType.QUESTION)
      expect(questionNode.label).toBe('New Question')

      const categoryNode = createNewTreeNode('tree1', TreeNodeType.CATEGORY)
      expect(categoryNode.label).toBe('New Category')
    })
  })

  describe('createNodesFromText', () => {
    it('should create multiple nodes from an array of strings', () => {
      const texts = ['Question 1', 'Question 2']
      const nodes = createNodesFromText('tree1', texts, TreeNodeType.QUESTION)
      expect(nodes.length).toBe(2)
      expect(nodes[0].label).toBe('Question 1')
      expect(nodes[1].id).toBe('question-Question_2')
    })
  })
})

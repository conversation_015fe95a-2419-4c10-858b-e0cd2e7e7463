import { renderHook, act } from '@testing-library/react'
import { useScreeningRephrase } from '@/app/(conv)/screening/hooks/useScreeningRephrase'
import { useChat } from 'ai/react'
import { useSession } from 'next-auth/react'
import { toast } from 'react-hot-toast'

// Mock dependencies
jest.mock('ai/react', () => ({
  useChat: jest.fn(),
}))
jest.mock('next-auth/react')
jest.mock('react-hot-toast')

const useChatMock = useChat as jest.Mock
const useSessionMock = useSession as jest.Mock

describe('useScreeningRephrase', () => {
  let mockAppend: jest.Mock
  let onRephrasedUpdate: jest.Mock

  const defaultProps = {
    description: 'test description',
    selectedLanguage: 'en' as const,
    onRephrasedUpdate: jest.fn(),
  }

  beforeEach(() => {
    jest.clearAllMocks()
    mockAppend = jest.fn()
    onRephrasedUpdate = jest.fn()

    useChatMock.mockReturnValue({
      messages: [],
      append: mockAppend,
      isLoading: false,
    })
    useSessionMock.mockReturnValue({ data: { user: { id: 'test-user' } } })
  })

  it('should call append when startRephrase is called', () => {
    const { result } = renderHook(() =>
      useScreeningRephrase({ ...defaultProps, onRephrasedUpdate })
    )

    act(() => {
      result.current.startRephrase()
    })

    expect(mockAppend).toHaveBeenCalledWith(
      {
        role: 'system',
        content: 'start rephrasing questions',
      },
      {
        body: {
          userId: 'test-user',
          description: 'test description',
          preferredLanguage: 'en',
        },
      }
    )
  })

  it('should show an error if user is not authenticated', () => {
    useSessionMock.mockReturnValue({ data: null })
    const { result } = renderHook(() =>
      useScreeningRephrase({ ...defaultProps, onRephrasedUpdate })
    )

    act(() => {
      result.current.startRephrase()
    })

    expect(toast.error).toHaveBeenCalledWith(
      'Please log in to use the real API.'
    )
    expect(mockAppend).not.toHaveBeenCalled()
  })

  it('should call onRephrasedUpdate with parsed suggestions', () => {
    const { rerender } = renderHook(() =>
      useScreeningRephrase({ ...defaultProps, onRephrasedUpdate })
    )

    const newMessages = [
      {
        role: 'assistant',
        content: '1. First suggestion\n2. Second suggestion',
      },
    ]
    useChatMock.mockReturnValue({
      messages: newMessages,
      append: mockAppend,
      isLoading: false,
    })

    rerender({})

    expect(onRephrasedUpdate).toHaveBeenCalledWith([
      'First suggestion',
      'Second suggestion',
    ])
  })

  it('should map isLoading to isStreaming', () => {
    useChatMock.mockReturnValue({
      messages: [],
      append: mockAppend,
      isLoading: true,
    })
    const { result } = renderHook(() =>
      useScreeningRephrase({ ...defaultProps, onRephrasedUpdate })
    )
    expect(result.current.isStreaming).toBe(true)
  })
})

import { render, screen, fireEvent } from '@testing-library/react'
import But<PERSON> from '@/app/components/Button'

describe('Button Component', () => {
  // Test basic rendering
  it('renders button with children', () => {
    render(<Button>Test Button</Button>)

    const button = screen.getByRole('button', { name: /test button/i })
    expect(button).toBeInTheDocument()
  })

  // Test click functionality
  it('calls onClick when clicked', () => {
    const handleClick = jest.fn()
    render(<Button onClick={handleClick}>Clickable Button</Button>)

    const button = screen.getByRole('button', { name: /clickable button/i })
    fireEvent.click(button)

    expect(handleClick).toHaveBeenCalledTimes(1)
  })

  // Test disabled state
  it('is disabled when disabled prop is true', () => {
    render(<Button disabled>Disabled Button</Button>)

    const button = screen.getByRole('button', { name: /disabled button/i })
    expect(button).toBeDisabled()
  })

  // Test button types
  it('renders with correct type attribute', () => {
    render(<Button type="submit">Submit Button</Button>)

    const button = screen.getByRole('button', { name: /submit button/i })
    expect(button).toHaveAttribute('type', 'submit')
  })

  // Test styling classes
  it('applies full width class when fullWidth prop is true', () => {
    render(<Button fullWidth>Full Width Button</Button>)

    const button = screen.getByRole('button', { name: /full width button/i })
    expect(button).toHaveClass('w-full')
  })

  // Test secondary variant
  it('applies secondary styles when secondary prop is true', () => {
    render(<Button secondary>Secondary Button</Button>)

    const button = screen.getByRole('button', { name: /secondary button/i })
    expect(button).toHaveClass('text-gray-900')
  })

  // Test danger variant
  it('applies danger styles when danger prop is true', () => {
    render(<Button danger>Danger Button</Button>)

    const button = screen.getByRole('button', { name: /danger button/i })
    expect(button).toHaveClass('bg-rose-500')
  })

  // Test default button type
  it('defaults to button type when no type is specified', () => {
    render(<Button>Default Button</Button>)

    const button = screen.getByRole('button', { name: /default button/i })
    expect(button).toHaveAttribute('type', 'button')
  })

  // Test disabled button doesn't trigger onClick
  it('does not call onClick when disabled', () => {
    const handleClick = jest.fn()
    render(
      <Button disabled onClick={handleClick}>
        Disabled Button
      </Button>
    )

    const button = screen.getByRole('button', { name: /disabled button/i })
    fireEvent.click(button)

    expect(handleClick).not.toHaveBeenCalled()
  })
})

---
type: 'always_apply'
---

- For any potential data loss or modification, you MUST ask for permission

- Use gemini cli as your peer to offer second len if you want more idea

- I prefer type annotation, please add if possible, eg: useState<bool>(true) instead of useState(true)

- I prefer meaningful semantic html tags than always dev

- I prefer type than interface in typescript

- I prefer tailwind cn rather than many conditional style code

- I prefer using @/app/... instead of relative path if possible, unless they are in the same folder

- I prefer DRY principle, reuse/modify the existing components if possible

- I prefer code readability and prevent bulky components

- I prefer clean codebase and remove unused import

- I prefer more comments such that future AI models can get the context quickly

- I want to have a README at route level, document the key information like components and interactions such that future AI models don't need to read one by one. You will also update the README after some modification

- In this project, we use Vercel AI SDK to bridge usage of LLM in Frontend and Backend, don't reinvent the wheel, use that if possible

- If possible, use defined prisma status instead of hardcoding

# Using Gemini CLI for Large Codebase Analysis OR investigation

When analyzing large codebases or multiple files that might exceed context limits, use the Gemini CLI with its massive
context window. Use `gemini -p` to leverage Google Gemini's large context capacity.

## File and Directory Inclusion Syntax

Use the `@` syntax to include files and directories in your Gemini prompts. The paths should be relative to WHERE you run the
gemini command:

### Examples:

**Single file analysis:**

```bash
gemini -p "@src/main.py Explain this file's purpose and structure"

Multiple files:
gemini -p "@package.json @src/index.js Analyze the dependencies used in the code"

Entire directory:
gemini -p "@src/ Summarize the architecture of this codebase"

Multiple directories:
gemini -p "@src/ @tests/ Analyze test coverage for the source code"
```

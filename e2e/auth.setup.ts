import { test as setup, expect } from '@playwright/test'
import { PrismaClient } from '@prisma/client'

const authFile = 'playwright/.auth/user.json'
const prisma = new PrismaClient()
const testUserEmail = '<EMAIL>'

setup('authenticate', async ({ page, request }) => {
  // Check if we are in a CI environment
  if (!process.env.CI) {
    console.log('Not in CI, skipping authentication setup.')
    // In local dev, you might rely on an existing session or manual login.
    // For this test, we will proceed to ensure a consistent state.
  }

  // 1. Create the user in the database if they don't exist
  await prisma.user.upsert({
    where: { email: testUserEmail },
    update: {},
    create: {
      name: 'Test User',
      email: testUserEmail,
      image: '',
    },
  })

  // 2. Programmatically log in using the new credentials provider
  // This is much faster and more reliable than a UI-based login.
  const response = await request.post('/api/auth/callback/credentials', {
    form: {
      email: testUserEmail,
      // The CSRF token is required for credentials login. We can get it from the sign-in page.
      csrfToken: await getCsrfToken(request),
    },
  })

  // Check if the login was successful
  expect(response.ok()).toBeTruthy()

  // 3. Save the authenticated state (cookies) to a file.
  await page.context().storageState({ path: authFile })
  console.log(`Authenticated state for ${testUserEmail} saved successfully.`)
})

/**
 * Helper function to fetch the CSRF token from the sign-in page.
 * Next-Auth requires this for POST-based sign-ins.
 */
async function getCsrfToken(request: typeof setup.prototype.request) {
  const response = await request.get('/api/auth/csrf')
  const json = await response.json()
  if (!json?.csrfToken) {
    throw new Error('CSRF token not found. Is the server running?')
  }
  return json.csrfToken
}

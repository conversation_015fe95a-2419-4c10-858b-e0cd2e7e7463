import { ConversationStatus, User } from "@prisma/client";
import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";
import { maxActiveConversations } from "@/app/configs";
import { SubscriptionTier } from "@prisma/client";
import { linksFromSearchResults } from "@/app/users/components/utils";
import { SearchResult } from "@/app/api/rag/utils";
import { ConversationLayoutType } from "@/app/types";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

// This is used to add alert after number to remind users that LLM often BS
// eg: if suffix is (EXAMPLE NUMBER)
// 1. **Standard Numbers**
//    - **Input:** "The population of the city is 3,250."
//    - **Output:** "The population of the city is 3,250 (EXAMPLE NUMBER)."

// 2. **Numbers with Commas (thousands separators)**
//    - **Input:** "The company's revenue reached $2,000,000 last year."
//    - **Output:** "The company's revenue reached $2,000,000 (EXAMPLE NUMBER) last year."

// 3. **Numbers with Decimals**
//    - **Input:** "The area of the office is 250.75 square meters."
//    - **Output:** "The area of the office is 250.75 (EXAMPLE NUMBER) square meters."

// 4. **Percentages**
//    - **Input:** "About 15% of the software installed is outdated."
//    - **Output:** "About 15% (EXAMPLE NUMBER) of the software installed is outdated."

// 5. **Ranges with Hyphens**
//    - **Input:** "The expected attendees are 1,000-1,500 people."
//    - **Output:** "The expected attendees are 1,000-1,500 (EXAMPLE NUMBER) people."

// 6. **Numbers Followed by Punctuation**
//     - **Input:** "The project requires an investment of $1,000,000; xxx"
//     - **Output:** "The project requires an investment of $1,000,000 (EXAMPLE NUMBER); xxx"

export function addSuffixAfterNumber(
  inputText: string,
  suffix: string
): string {
  // Updated regex to handle numbers including commas, periods, percent signs, exclamation marks, and hyphens
  const regex = /(\d[\d,.%!-]*)(\s|\b)/g;
  // Replace by inserting the suffix right after the complete number and before the space or word boundary
  return inputText.replace(regex, `$1 ${suffix}$2`);
}

export function countActiveConversationsAfterDate(
  conversationList: ConversationLayoutType[],
  status: ConversationStatus,
  date: Date
): number {
  return (
    conversationList.filter((item) => {
      const isActive = item.conversation_status === status;
      const isAfterDate = new Date(item.created_at) > new Date(date);

      return isActive && isAfterDate;
    }).length || 0
  );
}

export const isLocalOrDevEnv = () => {
  // Localhost or dev environment
  if (typeof window !== "undefined") {
    return (
      window.location.hostname === "localhost" ||
      window.location.hostname.includes("dev")
    );
  }
  return false;
};

export const isUserSubscribing = (user: User | null): boolean => {
  if (user === null) {
    return false;
  }

  const isGuest = user.subscription_tier === SubscriptionTier.GUEST;
  const hasValidSubscription = Boolean(
    user.subscription_end_date &&
      new Date(user.subscription_end_date) > new Date()
  );

  return isGuest || hasValidSubscription;
};

export function calculateMaxActiveConversations(user: User | null): number {
  if (isUserSubscribing(user)) {
    return 42;
  } else if (isLocalOrDevEnv()) {
    return 30;
  } else {
    return maxActiveConversations;
  }
}

export const getResponseWithSearchContext = (
  response: string,
  searchResults: SearchResult[] | null,
  numberOfLinksToUse: number
): string => {
  // If there's no response, return an empty string
  if (!response) {
    return "";
  }
  // If there's no search results, return the response
  if (searchResults === null || searchResults.length === 0) {
    return response;
  }

  const contextLinks = linksFromSearchResults(
    searchResults,
    numberOfLinksToUse
  );

  return `${response}\n\n=== Context sources: ===\n${contextLinks}`;
};

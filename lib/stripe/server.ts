"use server";

import <PERSON><PERSON> from "stripe";
import { stripe } from "@/lib/stripe/config";
import { User } from "@prisma/client";
import { NextResponse } from "next/server";

const baseUrl =
  process.env.NODE_ENV === "development"
    ? "http://localhost:3000"
    : process.env.NEXT_PUBLIC_SERVER_URL;

export async function checkoutWithStripe(
  user: User,
  price_id: string
): Promise<{ sessionId: string } | NextResponse> {
  try {
    if (!user.id || !price_id) {
      throw new Error("Invalid user or price.");
    }

    let params: Stripe.Checkout.SessionCreateParams = {
      mode: "subscription",
      line_items: [
        {
          price: price_id,
          quantity: 1,
        },
      ],
      metadata: { user_id: user.id, email: user.email },
      cancel_url: `${baseUrl}/subscription`,
      success_url: `${baseUrl}/subscription/success?session_id={CHECKOUT_SESSION_ID}`,
      allow_promotion_codes: true,
    };

    // metadata will not be used to linked the customer
    // If we have customer id, we can link it to the session to avoid creating a new Stripe customer
    if (user.subscription_customer_id) {
      params.customer = user.subscription_customer_id;
    }

    console.log("Creating checkout session with params:", params);

    // Create a checkout session in Stripe
    let session;
    try {
      session = await stripe.checkout.sessions.create(params);
    } catch (err) {
      console.error(err);
      throw new Error("Unable to create checkout session.");
    }

    // Instead of returning a Response, just return the data or error.
    if (session) {
      return { sessionId: session.id };
    } else {
      throw new Error("Unable to create checkout session.");
    }
  } catch (error) {
    console.error("Error creating checkout session:", error);
    return NextResponse.json({
      errorRedirect: "Failed to create checkout session",
    });
  }
}

export async function createStripePortal(user: User) {
  try {
    if (!user) {
      throw new Error("Could not get user session.");
    }

    if (!user.subscription_customer_id) {
      throw new Error("No subscription_customer_id, could not create portal.");
    }
    let params: Stripe.BillingPortal.SessionCreateParams = {
      customer: user.subscription_customer_id,
      return_url: `${baseUrl}/conversations`,
    };

    try {
      const { url } = await stripe.billingPortal.sessions.create(params);
      if (!url) {
        throw new Error("Could not create billing portal");
      }
      return url;
    } catch (err) {
      console.error(err);
      throw new Error("Could not create billing portal");
    }
  } catch (error) {
    console.error(error);
    return NextResponse.json({
      errorRedirect: "Failed to create billing portal",
    });
  }
}
